{"name": "AU- MyAccount", "created": "2025-06-27 15:52:29", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "pjFNt3w5Fr", "executionTime": "3339ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "u6bRYZZFAv", "duration": 5, "executionTime": "5016ms", "time": 5, "timestamp": *************, "type": "wait"}, {"action_id": "xAPeBnVHrT", "executionTime": "5638ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ly2oT3zqmf", "executionTime": "1167ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "vjK6GqOF3r", "expanded": false, "steps_loaded": true, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "fzahGiveKf", "executionTime": "2744ms", "text": "<EMAIL>", "timestamp": *********8431, "type": "text"}, {"action_id": "ZCdhKRqSd3", "image_filename": "keyboard_done_iphoneSE.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746937843235, "type": "tap"}, {"action_id": "0q79ZemF4g", "text_to_find": "Continue", "timeout": 30, "timestamp": 1746582763588, "type": "tapOnText"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "qPv5C4K0a2", "enter": true, "executionTime": "3988ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1745666288726, "type": "iosFunctions"}, {"action_id": "tH68tAo0l0", "executionTime": "3414ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": 1745666406257, "type": "exists"}], "test_case_steps_count": 8, "timestamp": *************, "type": "multiStep"}, {"action_id": "sl3Wk1gK8X", "executionTime": "2403ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "V59u3l1wkM", "executionTime": "1706ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "timeout": 20, "timestamp": *************, "type": "waitTill"}, {"action_id": "pFlYwTS53v", "double_tap": false, "executionTime": "3626ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"My orders & receipts\"]", "method": "locator", "text_to_find": "receipts", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "Z6g3sGuHTp", "duration": 5, "executionTime": "5016ms", "time": 5, "timestamp": *************, "type": "wait"}, {"action_id": "7g6MFJSGIO", "executionTime": "2460ms", "image_filename": "order-link-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "f3OrHHzTFN", "count": 4, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11269ms", "interval": 1, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Email tax invoice\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746520326535, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "GgQaBLWYkb", "executionTime": "2334ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Email tax invoice\"]", "method": "locator", "timeout": 10, "timestamp": 1746521169867, "type": "tap"}, {"action_id": "g0PE7Mofye", "executionTime": "4259ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Print order details", "method": "locator", "timeout": 10, "timestamp": 1746521217340, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "2572ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1746521262417, "type": "tap"}, {"action_id": "qXsL3wzg6J", "executionTime": "2713ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749442241837, "type": "tap"}, {"action_id": "zNwyPagPE1", "duration": 5, "executionTime": "5015ms", "time": 5, "timestamp": 1746575257741, "type": "wait"}, {"action_id": "7g6MFJSGIO", "executionTime": "2424ms", "image_filename": "order-link-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746521295094, "type": "tap"}, {"action_id": "mRTYzOFRRw", "executionTime": "1438ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "timeout": 15, "timestamp": 1746521347391, "type": "exists"}, {"action_id": "9iOZGMqAZK", "executionTime": "2369ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "method": "locator", "timeout": 10, "timestamp": 1746521360629, "type": "tap"}, {"action_id": "aAaTtUE92h", "executionTime": "1464ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Exchanges Returns\"]", "timeout": 15, "timestamp": 1746521417323, "type": "exists"}, {"action_id": "OwWeZes4aT", "executionTime": "2708ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749442317313, "type": "tap"}, {"action_id": "zeu0wd1vcF", "count": 4, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11956ms", "image_filename": "email-tax-btn.png", "interval": 0.5, "locator_type": "image", "locator_value": "email-tax-btn.png", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746522039770, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "vmc01sHkbr", "duration": 5, "executionTime": "5013ms", "time": 5, "timestamp": 1746575241902, "type": "wait"}, {"action_id": "FAvQgIuHc1", "double_tap": false, "executionTime": "22235ms", "image_filename": "return-to-orders-btn.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Return to my orders\"]", "method": "locator", "text_to_find": "Return", "threshold": 0.7, "timeout": 30, "timestamp": 1746522414963, "type": "tapOnText"}, {"action_id": "6qZnk86hGg", "executionTime": "2346ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "PbfHAtFQPP", "executionTime": "2514ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "yJi0WxnERj", "executionTime": "2289ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "method": "locator", "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "3hOTINBVMf", "executionTime": "3595ms", "text_to_find": "details", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "6HR2weiXoT", "executionTime": "2725ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "20qUCJgpE9", "double_tap": false, "executionTime": "3591ms", "text_to_find": "address", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "ekqt95ZRol", "executionTime": "2598ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "napKDohf3Z", "double_tap": false, "executionTime": "3608ms", "text_to_find": "payment", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "EO3cMmdUyM", "executionTime": "2625ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749442277501, "type": "tap"}, {"action_id": "MkTFxfzubv", "executionTime": "2509ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749442267978, "type": "tap"}, {"action_id": "BracBsfa3Y", "executionTime": "3551ms", "text_to_find": "Flybuys", "timeout": 30, "timestamp": 1746573390611, "type": "tapOnText"}, {"action_id": "40hnWPsQ9P", "executionTime": "3222ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btneditFlybuysCard", "method": "locator", "timeout": 15, "timestamp": 1746523229356, "type": "waitTill"}, {"action_id": "40hnWPsQ9P", "executionTime": "4095ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btneditFlybuysCard", "method": "locator", "timeout": 10, "timestamp": 1746577799791, "type": "tap"}, {"action_id": "3ZFgwFaiXp", "executionTime": "4033ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Remove card", "method": "locator", "timeout": 15, "timestamp": 1746523274958, "type": "tap"}, {"action_id": "Ds5GfNVb3x", "executionTime": "4041ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnRemove", "method": "locator", "timeout": 15, "timestamp": 1746523316209, "type": "tap"}, {"action_id": "BracBsfa3Y", "executionTime": "3884ms", "text_to_find": "Flybuys", "timeout": 30, "timestamp": 1746523184246, "type": "tapOnText"}, {"action_id": "Gxhf3XGc6e", "executionTime": "4152ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnLinkFlyBuys", "method": "locator", "timeout": 10, "timestamp": 1746573488051, "type": "tap"}, {"action_id": "Ey86YRVRzU", "executionTime": "4133ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Flybuys barcode number", "method": "locator", "timeout": 10, "timestamp": 1746573530684, "type": "tap"}, {"action_id": "sLe0Wurhgm", "locator_type": "accessibility_id", "locator_value": "Flybuys barcode number", "method": "locator", "text": "2791234567890", "timeout": 15, "timestamp": 1746573561688, "type": "text"}, {"action_id": "8cFGh3GD68", "executionTime": "4248ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Done", "method": "locator", "timeout": 10, "timestamp": 1746573767892, "type": "tap"}, {"action_id": "biRyWs3nSs", "executionTime": "4119ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveFlybuysCard", "method": "locator", "timeout": 10, "timestamp": 1746573806006, "type": "tap"}, {"action_id": "LBgsj3oLcu", "executionTime": "2679ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749442258936, "type": "tap"}, {"action_id": "2M0KHOVecv", "executionTime": "2007ms", "locator_type": "accessibility_id", "locator_value": "txtMy Flybuys card", "timeout": 10, "timestamp": 1746573880079, "type": "exists"}, {"action_id": "s6tWdQ5URW", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6005ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748085981704, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "3442ms", "text_to_find": "locator", "timeout": 30, "timestamp": 1746573912172, "type": "tapOnText"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "4175ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746573997116, "type": "tapOnText"}, {"action_id": "3Si0csRNaw", "executionTime": "5297ms", "method": "coordinates", "text": "env[store-locator-postcode]", "timeout": 15, "timestamp": 1749444122904, "type": "tapAndType", "x": "env[store-locator-x]", "y": "env[store-locator-y]"}, {"action_id": "PgjJCrKFYo", "double_tap": false, "executionTime": "4199ms", "image_filename": "storelocator-3000-se.png", "method": "image", "text_to_find": "VIC", "threshold": 0.7, "timeout": 30, "timestamp": 1746574179875, "type": "tapOnText"}, {"action_id": "8kQkC2FGyZ", "executionTime": "1622ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Melbourne Cbd\"]", "timeout": 10, "timestamp": 1746574233249, "type": "exists"}, {"action_id": "xVbCNStsOP", "executionTime": "2741ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749442229171, "type": "tap"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "3922ms", "text_to_find": "Invite", "timeout": 30, "timestamp": 1746574278949, "type": "tapOnText"}, {"action_id": "ePyaYpttQA", "executionTime": "1665ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"I’m loving the new Kmart app\")]", "timeout": 10, "timestamp": 1746574369210, "type": "exists"}, {"action_id": "H4WfwVU8YP", "executionTime": "3359ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746574392875, "type": "tap"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "3538ms", "text_to_find": "Customer", "timeout": 30, "timestamp": 1746574406045, "type": "tapOnText"}, {"action_id": "wNGRrfUjpK", "executionTime": "2596ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749442213782, "type": "tap"}, {"action_id": "s6tWdQ5URW", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6195ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1746575871287, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "3624ms", "text_to_find": "out", "timeout": 30, "timestamp": 1746574489503, "type": "tapOnText"}, {"type": "cleanupSteps", "timestamp": 1751003542408, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0, "action_id": "AeQaElnzUN"}], "labels": [], "updated": "2025-06-27 15:52:29", "test_case_id": "tc_54aba833e5e3"}