{"name": "Kmart-Prod-Signin", "created": "2025-07-27 20:33:10", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "Vxt7QOYeDD", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "2cTZvK1psn", "executionTime": "11940ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "q4hPXCBtx4", "executionTime": "1276ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "ImienLpJEN", "executionTime": "3022ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "8OsQmoVYqW", "executionTime": "4788ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "WMl5g82CCq", "executionTime": "2587ms", "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "I5bRbYY1hD", "executionTime": "4756ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "dyECdbRifp", "enter": true, "executionTime": "3823ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "IsGWxLFpIn", "executionTime": "3601ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "08NzsvhQXK", "executionTime": "4066ms", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "FciJcOsMsB", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11329ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "  Sign in with  OnePass", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "LlRfimKPrn", "executionTime": "6115ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "FlEukNkjlS", "executionTime": "2721ms", "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "cJDpd7aK3d", "executionTime": "10392ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2kwu2VBmuZ", "executionTime": "1236ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "NCyuT8W5Xz", "executionTime": "3068ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "UUhQjmzfO2", "count": 1, "direction": "up", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "3199ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "  Sign in with  OnePass", "method": "locator", "start_x": 50, "start_y": 70, "text_to_find": "OnePass", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tapOnText", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "y8ZMTkG38M", "executionTime": "5176ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email*\"]", "method": "locator", "timeout": 10, "timestamp": 1745667155033, "type": "tap"}, {"action_id": "Gb2Do6AtSN", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1753476072724, "type": "iosFunctions"}, {"type": "tap", "timestamp": 1753612332620, "image_filename": "captha-chkbox-op-ios.png", "threshold": 0.7, "timeout": 20, "method": "image", "action_id": "2fmkjdQPtJ"}, {"action_id": "DaVBARRwft", "executionTime": "5100ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password*\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "6wYIn0igez", "enter": true, "function_name": "text", "text": "Wonderbaby@6", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "M6HdLxu76S", "executionTime": "5009ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "2qOXZcEmK8", "executionTime": "4003ms", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "FciJcOsMsB", "count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "11329ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "  Sign in with  OnePass", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "oiPcknTonJ", "executionTime": "6095ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "taf19mtrUT", "executionTime": "2744ms", "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "HJzOYZNnGr", "executionTime": "10436ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AYiwFSLTBD", "executionTime": "1265ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "CJ88OgjKXp", "executionTime": "2958ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "VsSlyhXuVD", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11235ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Apple Sign in with Apple", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "NL2gtj6qIu", "double_tap": false, "executionTime": "3210ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Apple Sign in with Apple\"]", "method": "locator", "text_to_find": "Apple", "timeout": 30, "timestamp": 1745668551385, "type": "tapOnText"}, {"action_id": "CzVeOTdAX9", "duration": 10, "executionTime": "10010ms", "locator_type": "image", "locator_value": "SigninwithPasscode.png", "time": 10, "timeout": 10, "timestamp": 1746163993315, "type": "wait"}, {"action_id": "hnH3ayslCh", "double_tap": false, "executionTime": "1446ms", "image_filename": "SigninwithPasscode.png", "method": "image", "text_to_find": "Passcode", "threshold": 0.7, "timeout": 30, "timestamp": 1745985394449, "type": "tapOnText"}, {"action_id": "soKM0KayFJ", "executionTime": "7711ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"5\"]", "method": "locator", "timeout": 10, "timestamp": 1745668718729, "type": "tap"}, {"action_id": "0pwZCYAtOv", "executionTime": "2408ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"9\"]", "method": "locator", "timeout": 10, "timestamp": 1745668723169, "type": "tap"}, {"action_id": "J7BPGVnRJI", "executionTime": "2570ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"1\"]", "method": "locator", "timeout": 10, "timestamp": 1745668728929, "type": "tap"}, {"action_id": "iSckENpXrN", "executionTime": "2576ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"2\"]", "method": "locator", "timeout": 10, "timestamp": 1745668733552, "type": "tap"}, {"action_id": "5nsUXQ5L7u", "executionTime": "2571ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"3\"]", "method": "locator", "timeout": 10, "timestamp": 1745668738152, "type": "tap"}, {"action_id": "zsVeGHiIgX", "executionTime": "2454ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"4\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "KfMHchi8cx", "executionTime": "5415ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "CWkqGp5ndO", "executionTime": "4134ms", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "FciJcOsMsB", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11329ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "  Sign in with  OnePass", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "ts3qyFxyMf", "executionTime": "6070ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "jQYHQIvQ8l", "executionTime": "2712ms", "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "quzlwPw42x", "executionTime": "10533ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "6HhScBaqQp", "executionTime": "1276ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "SDtskxyVpg", "executionTime": "3056ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 20, "timestamp": *************, "type": "waitTill"}, {"action_id": "UtVRXwa86e", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11407ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Sign in with Google\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "xLGm9FefWE", "double_tap": false, "executionTime": "3199ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Sign in with Google\"]", "method": "locator", "text_to_find": "Google", "timeout": 20, "timestamp": 1745669156330, "type": "tap"}, {"action_id": "vwFwkK6ydQ", "executionTime": "5589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "bZCkx4U9Gk", "executionTime": "2045ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "FARWZvOj0x", "executionTime": "4213ms", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "FciJcOsMsB", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11329ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "  Sign in with  OnePass", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Bdhe5AoUlM", "executionTime": "6094ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "LzGkAcsQyE", "executionTime": "1076ms", "package_id": "env[appid]", "timestamp": *************, "type": "terminateApp"}, {"action_id": "PGvsG6rpU4", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1751002107908, "type": "cleanupSteps"}], "labels": [], "updated": "2025-07-27 20:33:10"}