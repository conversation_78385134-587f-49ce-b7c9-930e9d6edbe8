{"name": "App Settings AU", "created": "2025-07-24 14:22:56", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "XEbZHdi0GT", "executionTime": "3369ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "veukWo4573", "executionTime": "2427ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rJ86z4njuR", "executionTime": "1300ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "gx05zu87DK", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "2240ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3011ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "6vtTsVjGmr", "enter": true, "executionTime": "3311ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1752446259233, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "3759ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "ImKkcRCyRi", "enter": true, "executionTime": "3103ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1752446273568, "type": "iosFunctions"}], "test_case_steps_count": 5, "timestamp": 1753329273083, "type": "multiStep"}, {"action_id": "mIKA85kXaW", "executionTime": "1233ms", "package_id": "com.apple.Preferences", "timestamp": 1749444829578, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1236ms", "package_id": "com.apple.Preferences", "timestamp": 1749444810598, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "3298ms", "text_to_find": "Wi-Fi", "timeout": 30, "timestamp": 1749444898707, "type": "tapOnText"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5018ms", "time": 5, "timestamp": 1749445273934, "type": "wait"}, {"action_id": "jUCAk6GJc4", "executionTime": "1020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "method": "locator", "timeout": 10, "timestamp": 1749445081254, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5021ms", "time": 5, "timestamp": 1750843578808, "type": "wait"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "3293ms", "package_id": "env[appid]", "timestamp": 1749445437019, "type": "restartApp"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445125565, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "803ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445177888, "type": "tap"}, {"action_id": "eSr9EFlJek", "executionTime": "259ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445183545, "type": "exists"}, {"action_id": "6xgrAWyfZ4", "executionTime": "675ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445193141, "type": "tap"}, {"action_id": "WoymrHdtrO", "executionTime": "247ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445211742, "type": "exists"}, {"action_id": "UpUSVInizv", "executionTime": "677ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445217156, "type": "tap"}, {"action_id": "seQcUKjkSU", "executionTime": "256ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445234048, "type": "exists"}, {"action_id": "LfyQctrEJn", "executionTime": "124ms", "package_id": "com.apple.Preferences", "timestamp": 1749445249154, "type": "launchApp"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5014ms", "time": 5, "timestamp": 1750843560629, "type": "wait"}, {"action_id": "GRwHMVK4sA", "executionTime": "819ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "method": "locator", "timeout": 10, "timestamp": 1749445257372, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5014ms", "time": 5, "timestamp": 1750838420209, "type": "wait"}, {"action_id": "hCCEvRtj1A", "executionTime": "3265ms", "package_id": "env[appid]", "timestamp": 1749445309230, "type": "restartApp"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445687300, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2818ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749445784426, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "LcYLwUffqj", "executionTime": "2994ms", "text_to_find": "out", "timeout": 30, "timestamp": 1749445809311, "type": "tapOnText"}, {"action_id": "xVuuejtCFA", "executionTime": "3263ms", "package_id": "com.apple.mobilesafari", "timestamp": 1749445877653, "type": "restartApp"}, {"action_id": "0Q0fm6OTij", "executionTime": "1120ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "method": "locator", "timeout": 10, "timestamp": 1749445933995, "type": "tap"}, {"action_id": "rYJcLPh8Aq", "enter": true, "executionTime": "1803ms", "function_name": "text", "text": "kmart au", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "fTdGMJ3NH3", "executionTime": "1322ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "0QtNHB5WEK", "executionTime": "1574ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "UpUSVInizv", "executionTime": "2532ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "2208ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 50, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.5], "x": 0, "y": 0}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "text_to_find": "Catalogue", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "Jh6RTFWeOU", "executionTime": "21259ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]", "timeout": 20, "timestamp": 1752531571229, "type": "tapIfLocatorExists"}, {"action_id": "igReeDqips", "executionTime": "2833ms", "image_filename": "env[catalogue-menu-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749471352255, "type": "tap"}, {"action_id": "Pd7cReoJM6", "executionTime": "4726ms", "text_to_find": "List", "timeout": 30, "timestamp": 1749472320276, "type": "tapOnText"}, {"action_id": "JcAR0JctQ6", "executionTime": "2419ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749472290567, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751711463086, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Cmvm82hiAa", "executionTime": "5524ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1749472379198, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "2954ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749470221523, "type": "tap"}, {"type": "tapIfLocatorExists", "timestamp": 1753330939337, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Chat now\"]/preceding-sibling::XCUIElementTypeImage[1]", "timeout": 10, "action_id": "VpOhIxEl53"}, {"action_id": "gkkQzTCmma", "executionTime": "3089ms", "text_to_find": "Catalogue", "timeout": 30, "timestamp": 1749472424498, "type": "tapOnText"}, {"action_id": "gcSsGpqKwk", "executionTime": "20720ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]", "timeout": 20, "timestamp": 1752531605642, "type": "tapIfLocatorExists"}, {"action_id": "igReeDqips", "executionTime": "2804ms", "image_filename": "env[catalogue-menu-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749472450590, "type": "tap"}, {"action_id": "YHaMIjULRf", "executionTime": "4713ms", "text_to_find": "List", "timeout": 30, "timestamp": 1749472769571, "type": "tapOnText"}, {"action_id": "Qy0Y0uJchm", "executionTime": "2407ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749472775719, "type": "tap"}, {"action_id": "Iab9zCfpqO", "executionTime": "17678ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1749472786795, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "2934ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749472811046, "type": "tap"}, {"action_id": "saiPPHQSPa", "executionTime": "3599ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781418763, "type": "tapIfLocatorExists"}, {"action_id": "DbM0d0m6rU", "executionTime": "13019ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Increase quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473005084, "type": "tap"}, {"action_id": "IW6uAwdtiW", "executionTime": "2534ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Decrease quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473072817, "type": "tap"}, {"action_id": "K0c1gL9UK1", "executionTime": "2543ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1749472862396, "type": "tap"}, {"action_id": "3NOS1fbxZs", "executionTime": "2296ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749473253040, "type": "tap"}, {"action_id": "3KNqlNy6Bj", "executionTime": "2336ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749473131022, "type": "tap"}, {"action_id": "OKiI82VdnE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4974ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "start_x": 50, "start_y": 70, "timestamp": 1749474098726, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "L59V5hqMX9", "executionTime": "3289ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Home & Living\"]", "method": "locator", "timeout": 10, "timestamp": 1749889216570, "type": "tap"}, {"action_id": "n57KEWjTea", "executionTime": "2496ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "method": "locator", "timeout": 10, "timestamp": 1749474131811, "type": "tap"}, {"action_id": "2hGhWulI52", "executionTime": "2304ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "method": "locator", "timeout": 10, "timestamp": 1749474207391, "type": "tap"}, {"action_id": "MA2re5cDWr", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "4188ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 50, "timestamp": 1751711957966, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.5], "x": 0, "y": 0}, {"action_id": "Teyz3d55XS", "executionTime": "20298ms", "locator_type": "accessibility_id", "locator_value": "Add to bag", "timeout": 20, "timestamp": 1752531713958, "type": "tapIfLocatorExists"}, {"action_id": "c4T3INQkzn", "enabled": true, "executionTime": "3379ms", "package_id": "env[appid]", "timestamp": 1749889997693, "type": "restartApp"}, {"action_id": "UpUSVInizv", "enabled": true, "executionTime": "2479ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749474449613, "type": "tap"}, {"action_id": "P26OyuqWlb", "executionTime": "10388ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781427246, "type": "tapIfLocatorExists"}, {"action_id": "K2w7X1cPdH", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "2130ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 50, "timestamp": 1751711977154, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.5], "x": 0, "y": 0}, {"action_id": "yxlzTytgFT", "executionTime": "20422ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "timeout": 20, "timestamp": 1752531676923, "type": "tapIfLocatorExists"}, {"action_id": "Qb1AArnpCH", "duration": 5, "executionTime": "5011ms", "time": 5, "timestamp": 1750975463814, "type": "wait"}, {"action_id": "kPdSiomhwu", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1750975487799, "type": "cleanupSteps"}], "labels": [], "updated": "2025-07-24 14:22:56"}