Action Log - 2025-08-02 17:00:04
================================================================================

[[17:00:04]] [INFO] Generating execution report...
[[17:00:04]] [SUCCESS] All tests passed successfully!
[[17:00:04]] [SUCCESS] Screenshot refreshed
[[17:00:04]] [INFO] Refreshing screenshot...
[[17:00:04]] [INFO] 0OyVUxNbaF=pass
[[17:00:00]] [SUCCESS] Screenshot refreshed successfully
[[17:00:00]] [SUCCESS] Screenshot refreshed successfully
[[17:00:00]] [INFO] 0OyVUxNbaF=running
[[17:00:00]] [INFO] Executing action 11/11: Tap on Text: "Shopping"
[[16:59:59]] [SUCCESS] Screenshot refreshed
[[16:59:59]] [INFO] Refreshing screenshot...
[[16:59:59]] [INFO] vwjicir8ov=pass
[[16:59:57]] [INFO] vwjicir8ov=running
[[16:59:57]] [INFO] Executing action 10/11: Tap on element with xpath: //XCUIElementTypeButton[@name="TrolleyListRemoveProductButton"]
[[16:59:57]] [SUCCESS] Screenshot refreshed successfully
[[16:59:57]] [SUCCESS] Screenshot refreshed successfully
[[16:59:57]] [SUCCESS] Screenshot refreshed
[[16:59:57]] [INFO] Refreshing screenshot...
[[16:59:57]] [INFO] KFfbUGUAsp=pass
[[16:59:54]] [SUCCESS] Screenshot refreshed successfully
[[16:59:54]] [SUCCESS] Screenshot refreshed successfully
[[16:59:54]] [INFO] KFfbUGUAsp=running
[[16:59:54]] [INFO] Executing action 9/11: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"in Cart")]
[[16:59:54]] [SUCCESS] Screenshot refreshed
[[16:59:54]] [INFO] Refreshing screenshot...
[[16:59:54]] [INFO] yL81dtqigl=pass
[[16:59:51]] [SUCCESS] Screenshot refreshed successfully
[[16:59:51]] [SUCCESS] Screenshot refreshed successfully
[[16:59:51]] [INFO] yL81dtqigl=running
[[16:59:51]] [INFO] Executing action 8/11: Tap on element with xpath: //XCUIElementTypeButton[@name="HomePageQuantitySelectorAddToCartButton"]
[[16:59:50]] [SUCCESS] Screenshot refreshed
[[16:59:50]] [INFO] Refreshing screenshot...
[[16:59:50]] [INFO] q1ZdKSiX2c=pass
[[16:59:48]] [INFO] q1ZdKSiX2c=running
[[16:59:48]] [INFO] Executing action 7/11: Tap on element with xpath: (//XCUIElementTypeButton[@name="ProductCellActionButton"])[1]
[[16:59:48]] [SUCCESS] Screenshot refreshed successfully
[[16:59:48]] [SUCCESS] Screenshot refreshed successfully
[[16:59:47]] [SUCCESS] Screenshot refreshed
[[16:59:47]] [INFO] Refreshing screenshot...
[[16:59:47]] [INFO] hHL72YSZHM=pass
[[16:59:45]] [SUCCESS] Screenshot refreshed successfully
[[16:59:45]] [SUCCESS] Screenshot refreshed successfully
[[16:59:45]] [INFO] hHL72YSZHM=running
[[16:59:45]] [INFO] Executing action 6/11: Tap on element with xpath: //XCUIElementTypeButton[@name="Search"]
[[16:59:45]] [SUCCESS] Screenshot refreshed
[[16:59:45]] [INFO] Refreshing screenshot...
[[16:59:45]] [INFO] PHhUKvweRf=pass
[[16:59:42]] [SUCCESS] Screenshot refreshed successfully
[[16:59:42]] [SUCCESS] Screenshot refreshed successfully
[[16:59:42]] [INFO] PHhUKvweRf=running
[[16:59:42]] [INFO] Executing action 5/11: Input text: "bread"
[[16:59:42]] [SUCCESS] Screenshot refreshed
[[16:59:42]] [INFO] Refreshing screenshot...
[[16:59:42]] [INFO] A0IS4A1nhc=pass
[[16:59:39]] [SUCCESS] Screenshot refreshed successfully
[[16:59:39]] [SUCCESS] Screenshot refreshed successfully
[[16:59:39]] [INFO] A0IS4A1nhc=running
[[16:59:39]] [INFO] Executing action 4/11: Tap on element with xpath: //XCUIElementTypeButton[@name="SearchButton"]
[[16:59:38]] [SUCCESS] Screenshot refreshed
[[16:59:38]] [INFO] Refreshing screenshot...
[[16:59:38]] [INFO] g35EwHVyF1=pass
[[16:59:36]] [SUCCESS] Screenshot refreshed successfully
[[16:59:36]] [SUCCESS] Screenshot refreshed successfully
[[16:59:36]] [INFO] g35EwHVyF1=running
[[16:59:36]] [INFO] Executing action 3/11: Tap on element with xpath: //XCUIElementTypeButton[@name="Products"]
[[16:59:35]] [SUCCESS] Screenshot refreshed
[[16:59:35]] [INFO] Refreshing screenshot...
[[16:59:35]] [INFO] YDCEwNh5Nm=pass
[[16:59:31]] [SUCCESS] Screenshot refreshed successfully
[[16:59:31]] [SUCCESS] Screenshot refreshed successfully
[[16:59:30]] [INFO] YDCEwNh5Nm=running
[[16:59:30]] [INFO] Executing action 2/11: Tap on element with xpath: //XCUIElementTypeImage[@name="tabLists"]
[[16:59:30]] [SUCCESS] Screenshot refreshed
[[16:59:30]] [INFO] Refreshing screenshot...
[[16:59:30]] [INFO] yHeSvvpOZo=pass
[[16:59:24]] [INFO] yHeSvvpOZo=running
[[16:59:24]] [INFO] Executing action 1/11: Restart app: com.woolworths.supers
[[16:59:24]] [INFO] ExecutionManager: Starting execution of 11 actions...
[[16:59:24]] [SUCCESS] Cleared 1 screenshots from database
[[16:59:24]] [INFO] Clearing screenshots from database before execution...
[[16:59:24]] [SUCCESS] All screenshots deleted successfully
[[16:59:24]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:59:24]] [INFO] Skipping report initialization - single test case execution
[[16:59:15]] [SUCCESS] All screenshots deleted successfully
[[16:59:15]] [SUCCESS] Loaded test case "Woolworth1" with 11 actions
[[16:59:15]] [SUCCESS] Added action: tapOnText
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: text
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: tap
[[16:59:15]] [SUCCESS] Added action: restartApp
[[16:59:15]] [INFO] All actions cleared
[[16:59:15]] [INFO] Cleaning up screenshots...
[[16:47:25]] [SUCCESS] Screenshot refreshed successfully
[[16:47:23]] [SUCCESS] Screenshot refreshed
[[16:47:23]] [INFO] Refreshing screenshot...
[[16:47:22]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[16:47:22]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[16:47:00]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[16:45:11]] [ERROR] Failed to connect to BrowserStack device
[[16:45:09]] [INFO] Connecting to BrowserStack device - Platform: ios, Device: iPhone 14 Pro Max
[[16:43:45]] [SUCCESS] Found 1 device(s)
[[16:43:44]] [INFO] Refreshing device list...
