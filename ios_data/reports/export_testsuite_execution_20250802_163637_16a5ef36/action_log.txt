Action Log - 2025-08-02 16:37:44
================================================================================

[[16:37:44]] [INFO] Generating execution report...
[[16:37:44]] [SUCCESS] All tests passed successfully!
[[16:37:44]] [SUCCESS] Screenshot refreshed successfully
[[16:37:44]] [SUCCESS] Screenshot refreshed successfully
[[16:37:44]] [SUCCESS] Screenshot refreshed
[[16:37:44]] [INFO] Refreshing screenshot...
[[16:37:44]] [INFO] WaakzZc6xF=pass
[[16:37:42]] [INFO] WaakzZc6xF=running
[[16:37:42]] [INFO] Executing action 19/19: Take Screenshot: "after_closing_health_app"
[[16:37:42]] [SUCCESS] Screenshot refreshed successfully
[[16:37:42]] [SUCCESS] Screenshot refreshed successfully
[[16:37:41]] [SUCCESS] Screenshot refreshed
[[16:37:41]] [INFO] Refreshing screenshot...
[[16:37:41]] [INFO] jF4jRny1iE=pass
[[16:37:39]] [INFO] jF4jRny1iE=running
[[16:37:39]] [INFO] Executing action 18/19: Terminate app: com.apple.Health
[[16:37:39]] [SUCCESS] Screenshot refreshed successfully
[[16:37:39]] [SUCCESS] Screenshot refreshed successfully
[[16:37:38]] [SUCCESS] Screenshot refreshed
[[16:37:38]] [INFO] Refreshing screenshot...
[[16:37:38]] [INFO] oIAtyQB5wY=pass
[[16:37:36]] [INFO] oIAtyQB5wY=running
[[16:37:36]] [INFO] Executing action 17/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[16:37:35]] [SUCCESS] Screenshot refreshed successfully
[[16:37:35]] [SUCCESS] Screenshot refreshed successfully
[[16:37:35]] [SUCCESS] Screenshot refreshed
[[16:37:35]] [INFO] Refreshing screenshot...
[[16:37:35]] [INFO] 7MOUNxtPJz=pass
[[16:37:33]] [INFO] 7MOUNxtPJz=running
[[16:37:33]] [INFO] Executing action 16/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[16:37:32]] [SUCCESS] Screenshot refreshed successfully
[[16:37:32]] [SUCCESS] Screenshot refreshed successfully
[[16:37:32]] [SUCCESS] Screenshot refreshed
[[16:37:32]] [INFO] Refreshing screenshot...
[[16:37:32]] [INFO] UppP3ZuqY6=pass
[[16:37:29]] [INFO] UppP3ZuqY6=running
[[16:37:29]] [INFO] Executing action 15/19: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[16:37:29]] [SUCCESS] Screenshot refreshed successfully
[[16:37:29]] [SUCCESS] Screenshot refreshed successfully
[[16:37:29]] [SUCCESS] Screenshot refreshed
[[16:37:29]] [INFO] Refreshing screenshot...
[[16:37:29]] [INFO] ag29wsBP24=pass
[[16:37:27]] [INFO] ag29wsBP24=running
[[16:37:27]] [INFO] Executing action 14/19: Take Screenshot: "after_edit_link_click"
[[16:37:27]] [SUCCESS] Screenshot refreshed successfully
[[16:37:27]] [SUCCESS] Screenshot refreshed successfully
[[16:37:27]] [SUCCESS] Screenshot refreshed
[[16:37:27]] [INFO] Refreshing screenshot...
[[16:37:27]] [INFO] SaJtvXOGlT=pass
[[16:37:24]] [INFO] SaJtvXOGlT=running
[[16:37:24]] [INFO] Executing action 13/19: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[16:37:24]] [SUCCESS] Screenshot refreshed successfully
[[16:37:24]] [SUCCESS] Screenshot refreshed successfully
[[16:37:24]] [SUCCESS] Screenshot refreshed
[[16:37:24]] [INFO] Refreshing screenshot...
[[16:37:24]] [INFO] To6rgFtm9R=pass
[[16:37:11]] [INFO] To6rgFtm9R=running
[[16:37:11]] [INFO] Executing action 12/19: Launch app: com.apple.Health
[[16:37:11]] [SUCCESS] Screenshot refreshed successfully
[[16:37:11]] [SUCCESS] Screenshot refreshed successfully
[[16:37:11]] [SUCCESS] Screenshot refreshed
[[16:37:11]] [INFO] Refreshing screenshot...
[[16:37:11]] [INFO] 0OyVUxNbaF=pass
[[16:37:08]] [INFO] 0OyVUxNbaF=running
[[16:37:08]] [INFO] Executing action 11/19: Tap on Text: "Shopping"
[[16:37:08]] [SUCCESS] Screenshot refreshed successfully
[[16:37:08]] [SUCCESS] Screenshot refreshed successfully
[[16:37:07]] [SUCCESS] Screenshot refreshed
[[16:37:07]] [INFO] Refreshing screenshot...
[[16:37:07]] [INFO] vwjicir8ov=pass
[[16:37:05]] [INFO] vwjicir8ov=running
[[16:37:05]] [INFO] Executing action 10/19: Tap on element with xpath: //XCUIElementTypeButton[@name="TrolleyListRemoveProductButton"]
[[16:37:05]] [SUCCESS] Screenshot refreshed successfully
[[16:37:05]] [SUCCESS] Screenshot refreshed successfully
[[16:37:05]] [SUCCESS] Screenshot refreshed
[[16:37:05]] [INFO] Refreshing screenshot...
[[16:37:05]] [INFO] KFfbUGUAsp=pass
[[16:37:03]] [INFO] KFfbUGUAsp=running
[[16:37:03]] [INFO] Executing action 9/19: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"in Cart")]
[[16:37:02]] [SUCCESS] Screenshot refreshed successfully
[[16:37:02]] [SUCCESS] Screenshot refreshed successfully
[[16:37:02]] [SUCCESS] Screenshot refreshed
[[16:37:02]] [INFO] Refreshing screenshot...
[[16:37:02]] [INFO] yL81dtqigl=pass
[[16:37:00]] [INFO] yL81dtqigl=running
[[16:37:00]] [INFO] Executing action 8/19: Tap on element with xpath: //XCUIElementTypeButton[@name="HomePageQuantitySelectorAddToCartButton"]
[[16:37:00]] [SUCCESS] Screenshot refreshed successfully
[[16:37:00]] [SUCCESS] Screenshot refreshed successfully
[[16:36:59]] [SUCCESS] Screenshot refreshed
[[16:36:59]] [INFO] Refreshing screenshot...
[[16:36:59]] [INFO] q1ZdKSiX2c=pass
[[16:36:57]] [INFO] q1ZdKSiX2c=running
[[16:36:57]] [INFO] Executing action 7/19: Tap on element with xpath: (//XCUIElementTypeButton[@name="ProductCellActionButton"])[1]
[[16:36:57]] [SUCCESS] Screenshot refreshed successfully
[[16:36:57]] [SUCCESS] Screenshot refreshed successfully
[[16:36:57]] [SUCCESS] Screenshot refreshed
[[16:36:57]] [INFO] Refreshing screenshot...
[[16:36:57]] [INFO] hHL72YSZHM=pass
[[16:36:55]] [INFO] hHL72YSZHM=running
[[16:36:55]] [INFO] Executing action 6/19: Tap on element with xpath: //XCUIElementTypeButton[@name="Search"]
[[16:36:54]] [SUCCESS] Screenshot refreshed successfully
[[16:36:54]] [SUCCESS] Screenshot refreshed successfully
[[16:36:54]] [SUCCESS] Screenshot refreshed
[[16:36:54]] [INFO] Refreshing screenshot...
[[16:36:54]] [INFO] PHhUKvweRf=pass
[[16:36:52]] [INFO] PHhUKvweRf=running
[[16:36:52]] [INFO] Executing action 5/19: Input text: "bread"
[[16:36:52]] [SUCCESS] Screenshot refreshed successfully
[[16:36:52]] [SUCCESS] Screenshot refreshed successfully
[[16:36:52]] [SUCCESS] Screenshot refreshed
[[16:36:52]] [INFO] Refreshing screenshot...
[[16:36:52]] [INFO] A0IS4A1nhc=pass
[[16:36:49]] [INFO] A0IS4A1nhc=running
[[16:36:49]] [INFO] Executing action 4/19: Tap on element with xpath: //XCUIElementTypeButton[@name="SearchButton"]
[[16:36:49]] [SUCCESS] Screenshot refreshed successfully
[[16:36:49]] [SUCCESS] Screenshot refreshed successfully
[[16:36:49]] [SUCCESS] Screenshot refreshed
[[16:36:49]] [INFO] Refreshing screenshot...
[[16:36:49]] [INFO] g35EwHVyF1=pass
[[16:36:46]] [INFO] g35EwHVyF1=running
[[16:36:46]] [INFO] Executing action 3/19: Tap on element with xpath: //XCUIElementTypeButton[@name="Products"]
[[16:36:46]] [SUCCESS] Screenshot refreshed successfully
[[16:36:46]] [SUCCESS] Screenshot refreshed successfully
[[16:36:46]] [SUCCESS] Screenshot refreshed
[[16:36:46]] [INFO] Refreshing screenshot...
[[16:36:46]] [INFO] YDCEwNh5Nm=pass
[[16:36:42]] [INFO] YDCEwNh5Nm=running
[[16:36:42]] [INFO] Executing action 2/19: Tap on element with xpath: //XCUIElementTypeImage[@name="tabLists"]
[[16:36:42]] [SUCCESS] Screenshot refreshed successfully
[[16:36:42]] [SUCCESS] Screenshot refreshed successfully
[[16:36:41]] [SUCCESS] Screenshot refreshed
[[16:36:41]] [INFO] Refreshing screenshot...
[[16:36:41]] [INFO] yHeSvvpOZo=pass
[[16:36:37]] [INFO] yHeSvvpOZo=running
[[16:36:37]] [INFO] Executing action 1/19: Restart app: com.woolworths.supers
[[16:36:37]] [INFO] ExecutionManager: Starting execution of 19 actions...
[[16:36:37]] [SUCCESS] Cleared 0 screenshots from database
[[16:36:37]] [INFO] Clearing screenshots from database before execution...
[[16:36:37]] [SUCCESS] All screenshots deleted successfully
[[16:36:37]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:36:37]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250802_163637/screenshots
[[16:36:37]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250802_163637
[[16:36:37]] [SUCCESS] Report directory initialized successfully
[[16:36:36]] [INFO] Initializing report directory and screenshots folder for test suite...
[[16:36:26]] [INFO] Collapsed all test cases
[[16:36:25]] [SUCCESS] All screenshots deleted successfully
[[16:36:25]] [INFO] All actions cleared
[[16:36:25]] [INFO] Cleaning up screenshots...
[[16:36:19]] [SUCCESS] Test case Woolworth1 saved successfully
[[16:36:19]] [INFO] Saving test case "Woolworth1"...
[[16:36:18]] [INFO] Action removed at index 1
[[16:36:12]] [SUCCESS] Added restartApp action
[[16:36:12]] [SUCCESS] Added action: restartApp
[[16:35:58]] [SUCCESS] Screenshot refreshed successfully
[[16:35:58]] [SUCCESS] Screenshot refreshed
[[16:35:58]] [INFO] Refreshing screenshot...
[[16:35:57]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[16:35:57]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[16:35:55]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[16:35:53]] [SUCCESS] All screenshots deleted successfully
[[16:35:53]] [SUCCESS] Loaded test case "Woolworth1" with 11 actions
[[16:35:53]] [SUCCESS] Added action: tapOnText
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: text
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: tap
[[16:35:53]] [SUCCESS] Added action: launchApp
[[16:35:53]] [INFO] All actions cleared
[[16:35:53]] [INFO] Cleaning up screenshots...
[[16:35:53]] [INFO] Selected device: iPhone15,2 (00008120-00186C801E13C01E)
[[16:35:52]] [SUCCESS] Found 1 device(s)
[[16:35:51]] [INFO] Refreshing device list...
