Action Log - 2025-08-04 11:03:20
================================================================================

[[11:03:19]] [INFO] Generating execution report...
[[11:03:19]] [WARNING] 1 test failed.
[[11:03:19]] [SUCCESS] Screenshot refreshed
[[11:03:19]] [INFO] Refreshing screenshot...
[[11:03:19]] [SUCCESS] Screenshot refreshed successfully
[[11:03:19]] [SUCCESS] Screenshot refreshed successfully
[[11:03:19]] [SUCCESS] Screenshot refreshed
[[11:03:19]] [INFO] Refreshing screenshot...
[[11:03:16]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[11:03:16]] [SUCCESS] Screenshot refreshed successfully
[[11:03:16]] [SUCCESS] Screenshot refreshed successfully
[[11:03:16]] [SUCCESS] Screenshot refreshed
[[11:03:16]] [INFO] Refreshing screenshot...
[[11:03:11]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[11:03:10]] [SUCCESS] Screenshot refreshed successfully
[[11:03:10]] [SUCCESS] Screenshot refreshed successfully
[[11:03:10]] [SUCCESS] Screenshot refreshed
[[11:03:10]] [INFO] Refreshing screenshot...
[[11:03:07]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[11:03:06]] [SUCCESS] Screenshot refreshed successfully
[[11:03:06]] [SUCCESS] Screenshot refreshed successfully
[[11:03:06]] [SUCCESS] Screenshot refreshed
[[11:03:06]] [INFO] Refreshing screenshot...
[[11:03:02]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[11:03:02]] [SUCCESS] Screenshot refreshed successfully
[[11:03:02]] [SUCCESS] Screenshot refreshed successfully
[[11:03:02]] [SUCCESS] Screenshot refreshed
[[11:03:02]] [INFO] Refreshing screenshot...
[[11:02:56]] [SUCCESS] Screenshot refreshed successfully
[[11:02:56]] [SUCCESS] Screenshot refreshed successfully
[[11:02:55]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[11:02:55]] [SUCCESS] Screenshot refreshed
[[11:02:55]] [INFO] Refreshing screenshot...
[[11:02:48]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[11:02:48]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[11:02:48]] [INFO] Loading steps for multiStep action: Kmart_AU_Cleanup
[[11:02:48]] [INFO] 4pFqgUdIwt=running
[[11:02:48]] [INFO] Executing action 643/643: Execute Test Case: Kmart_AU_Cleanup (6 steps)
[[11:02:48]] [SUCCESS] Screenshot refreshed successfully
[[11:02:48]] [SUCCESS] Screenshot refreshed successfully
[[11:02:47]] [SUCCESS] Screenshot refreshed
[[11:02:47]] [INFO] Refreshing screenshot...
[[11:02:47]] [INFO] q6kSH9e0MI=pass
[[11:02:44]] [INFO] q6kSH9e0MI=running
[[11:02:44]] [INFO] Executing action 642/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[11:02:43]] [SUCCESS] Screenshot refreshed successfully
[[11:02:43]] [SUCCESS] Screenshot refreshed successfully
[[11:02:43]] [SUCCESS] Screenshot refreshed
[[11:02:43]] [INFO] Refreshing screenshot...
[[11:02:43]] [INFO] a4pJa7EAyI=pass
[[11:02:39]] [INFO] a4pJa7EAyI=running
[[11:02:39]] [INFO] Executing action 641/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[11:02:39]] [SUCCESS] Screenshot refreshed successfully
[[11:02:39]] [SUCCESS] Screenshot refreshed successfully
[[11:02:39]] [SUCCESS] Screenshot refreshed
[[11:02:39]] [INFO] Refreshing screenshot...
[[11:02:39]] [INFO] 2bcxKJ2cPg=pass
[[11:02:33]] [INFO] 2bcxKJ2cPg=running
[[11:02:33]] [INFO] Executing action 640/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[11:02:32]] [SUCCESS] Screenshot refreshed successfully
[[11:02:32]] [SUCCESS] Screenshot refreshed successfully
[[11:02:32]] [SUCCESS] Screenshot refreshed
[[11:02:32]] [INFO] Refreshing screenshot...
[[11:02:32]] [INFO] aqBkqyVhrZ=pass
[[11:02:29]] [INFO] aqBkqyVhrZ=running
[[11:02:29]] [INFO] Executing action 639/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[11:02:28]] [SUCCESS] Screenshot refreshed successfully
[[11:02:28]] [SUCCESS] Screenshot refreshed successfully
[[11:02:28]] [SUCCESS] Screenshot refreshed
[[11:02:28]] [INFO] Refreshing screenshot...
[[11:02:28]] [INFO] wSHsGWAwPm=pass
[[11:02:16]] [INFO] wSHsGWAwPm=running
[[11:02:16]] [INFO] Executing action 638/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[11:02:16]] [SUCCESS] Screenshot refreshed successfully
[[11:02:16]] [SUCCESS] Screenshot refreshed successfully
[[11:02:16]] [SUCCESS] Screenshot refreshed
[[11:02:16]] [INFO] Refreshing screenshot...
[[11:02:16]] [INFO] gPYNwJ0HKo=pass
[[11:02:12]] [INFO] gPYNwJ0HKo=running
[[11:02:12]] [INFO] Executing action 637/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[11:02:11]] [SUCCESS] Screenshot refreshed successfully
[[11:02:11]] [SUCCESS] Screenshot refreshed successfully
[[11:02:11]] [SUCCESS] Screenshot refreshed
[[11:02:11]] [INFO] Refreshing screenshot...
[[11:02:11]] [INFO] vYLhraWpQm=pass
[[11:02:07]] [INFO] vYLhraWpQm=running
[[11:02:07]] [INFO] Executing action 636/643: Tap on image: banner-close-updated.png
[[11:02:07]] [SUCCESS] Screenshot refreshed successfully
[[11:02:07]] [SUCCESS] Screenshot refreshed successfully
[[11:02:07]] [SUCCESS] Screenshot refreshed
[[11:02:07]] [INFO] Refreshing screenshot...
[[11:02:07]] [INFO] TAKgcEDqvz=pass
[[11:02:04]] [INFO] TAKgcEDqvz=running
[[11:02:04]] [INFO] Executing action 635/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[11:02:04]] [SUCCESS] Screenshot refreshed successfully
[[11:02:04]] [SUCCESS] Screenshot refreshed successfully
[[11:02:03]] [SUCCESS] Screenshot refreshed
[[11:02:03]] [INFO] Refreshing screenshot...
[[11:02:03]] [INFO] UgjXUTZy7Z=pass
[[11:02:00]] [INFO] UgjXUTZy7Z=running
[[11:02:00]] [INFO] Executing action 634/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[11:01:59]] [SUCCESS] Screenshot refreshed successfully
[[11:01:59]] [SUCCESS] Screenshot refreshed successfully
[[11:01:59]] [SUCCESS] Screenshot refreshed
[[11:01:59]] [INFO] Refreshing screenshot...
[[11:01:59]] [INFO] YqmO7h7VP0=pass
[[11:01:55]] [INFO] YqmO7h7VP0=running
[[11:01:55]] [INFO] Executing action 633/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[11:01:55]] [SUCCESS] Screenshot refreshed successfully
[[11:01:55]] [SUCCESS] Screenshot refreshed successfully
[[11:01:55]] [SUCCESS] Screenshot refreshed
[[11:01:55]] [INFO] Refreshing screenshot...
[[11:01:55]] [INFO] vYLhraWpQm=pass
[[11:01:51]] [INFO] vYLhraWpQm=running
[[11:01:51]] [INFO] Executing action 632/643: Tap on image: banner-close-updated.png
[[11:01:51]] [SUCCESS] Screenshot refreshed successfully
[[11:01:51]] [SUCCESS] Screenshot refreshed successfully
[[11:01:51]] [SUCCESS] Screenshot refreshed
[[11:01:51]] [INFO] Refreshing screenshot...
[[11:01:51]] [INFO] lSG7un0qKK=pass
[[11:01:46]] [INFO] lSG7un0qKK=running
[[11:01:46]] [INFO] Executing action 631/643: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[11:01:46]] [SUCCESS] Screenshot refreshed successfully
[[11:01:46]] [SUCCESS] Screenshot refreshed successfully
[[11:01:45]] [SUCCESS] Screenshot refreshed
[[11:01:45]] [INFO] Refreshing screenshot...
[[11:01:45]] [INFO] 9Pwdq32eUk=pass
[[11:01:42]] [INFO] 9Pwdq32eUk=running
[[11:01:42]] [INFO] Executing action 630/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[11:01:41]] [SUCCESS] Screenshot refreshed successfully
[[11:01:41]] [SUCCESS] Screenshot refreshed successfully
[[11:01:41]] [SUCCESS] Screenshot refreshed
[[11:01:41]] [INFO] Refreshing screenshot...
[[11:01:41]] [INFO] YBT2MVclAv=pass
[[11:01:37]] [INFO] YBT2MVclAv=running
[[11:01:37]] [INFO] Executing action 629/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[11:01:37]] [SUCCESS] Screenshot refreshed successfully
[[11:01:37]] [SUCCESS] Screenshot refreshed successfully
[[11:01:37]] [SUCCESS] Screenshot refreshed
[[11:01:37]] [INFO] Refreshing screenshot...
[[11:01:37]] [INFO] TzPItWbvDR=pass
[[11:01:33]] [INFO] TzPItWbvDR=running
[[11:01:33]] [INFO] Executing action 628/643: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[11:01:33]] [SUCCESS] Screenshot refreshed successfully
[[11:01:33]] [SUCCESS] Screenshot refreshed successfully
[[11:01:32]] [SUCCESS] Screenshot refreshed
[[11:01:32]] [INFO] Refreshing screenshot...
[[11:01:32]] [INFO] wSdfNe4Kww=pass
[[11:01:29]] [INFO] wSdfNe4Kww=running
[[11:01:29]] [INFO] Executing action 627/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[11:01:29]] [SUCCESS] Screenshot refreshed successfully
[[11:01:29]] [SUCCESS] Screenshot refreshed successfully
[[11:01:29]] [SUCCESS] Screenshot refreshed
[[11:01:29]] [INFO] Refreshing screenshot...
[[11:01:29]] [INFO] GN587Y6VBQ=pass
[[11:01:25]] [INFO] GN587Y6VBQ=running
[[11:01:25]] [INFO] Executing action 626/643: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[11:01:25]] [SUCCESS] Screenshot refreshed successfully
[[11:01:25]] [SUCCESS] Screenshot refreshed successfully
[[11:01:24]] [SUCCESS] Screenshot refreshed
[[11:01:24]] [INFO] Refreshing screenshot...
[[11:01:24]] [INFO] dkSs61jGvX=pass
[[11:01:21]] [INFO] dkSs61jGvX=running
[[11:01:21]] [INFO] Executing action 625/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[11:01:20]] [SUCCESS] Screenshot refreshed successfully
[[11:01:20]] [SUCCESS] Screenshot refreshed successfully
[[11:01:20]] [SUCCESS] Screenshot refreshed
[[11:01:20]] [INFO] Refreshing screenshot...
[[11:01:20]] [INFO] XLpUP3Wr93=pass
[[11:01:16]] [INFO] XLpUP3Wr93=running
[[11:01:16]] [INFO] Executing action 624/643: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[11:01:16]] [SUCCESS] Screenshot refreshed successfully
[[11:01:16]] [SUCCESS] Screenshot refreshed successfully
[[11:01:16]] [SUCCESS] Screenshot refreshed
[[11:01:16]] [INFO] Refreshing screenshot...
[[11:01:16]] [INFO] mfOWujfRpL=pass
[[11:01:13]] [INFO] mfOWujfRpL=running
[[11:01:13]] [INFO] Executing action 623/643: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[11:01:12]] [SUCCESS] Screenshot refreshed successfully
[[11:01:12]] [SUCCESS] Screenshot refreshed successfully
[[11:01:12]] [SUCCESS] Screenshot refreshed
[[11:01:12]] [INFO] Refreshing screenshot...
[[11:01:12]] [INFO] ftA0OJvd0W=pass
[[11:01:08]] [INFO] ftA0OJvd0W=running
[[11:01:08]] [INFO] Executing action 622/643: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[11:01:08]] [SUCCESS] Screenshot refreshed successfully
[[11:01:08]] [SUCCESS] Screenshot refreshed successfully
[[11:01:08]] [SUCCESS] Screenshot refreshed
[[11:01:08]] [INFO] Refreshing screenshot...
[[11:01:08]] [INFO] CBBib3pFkq=pass
[[11:01:00]] [INFO] CBBib3pFkq=running
[[11:01:00]] [INFO] Executing action 621/643: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[11:01:00]] [SUCCESS] Screenshot refreshed successfully
[[11:01:00]] [SUCCESS] Screenshot refreshed successfully
[[11:01:00]] [SUCCESS] Screenshot refreshed
[[11:01:00]] [INFO] Refreshing screenshot...
[[11:01:00]] [INFO] 6LQ5cq0f6N=pass
[[11:00:52]] [INFO] 6LQ5cq0f6N=running
[[11:00:52]] [INFO] Executing action 620/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[11:00:51]] [SUCCESS] Screenshot refreshed successfully
[[11:00:51]] [SUCCESS] Screenshot refreshed successfully
[[11:00:51]] [SUCCESS] Screenshot refreshed
[[11:00:51]] [INFO] Refreshing screenshot...
[[11:00:51]] [INFO] 1Lirmyxkft=pass
[[11:00:47]] [INFO] 1Lirmyxkft=running
[[11:00:47]] [INFO] Executing action 619/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[11:00:47]] [SUCCESS] Screenshot refreshed successfully
[[11:00:47]] [SUCCESS] Screenshot refreshed successfully
[[11:00:47]] [SUCCESS] Screenshot refreshed
[[11:00:47]] [INFO] Refreshing screenshot...
[[11:00:47]] [INFO] TTpwkHEyuE=pass
[[11:00:39]] [INFO] TTpwkHEyuE=running
[[11:00:39]] [INFO] Executing action 618/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[11:00:39]] [SUCCESS] Screenshot refreshed successfully
[[11:00:39]] [SUCCESS] Screenshot refreshed successfully
[[11:00:39]] [SUCCESS] Screenshot refreshed
[[11:00:39]] [INFO] Refreshing screenshot...
[[11:00:39]] [INFO] mMnRNh3NEd=pass
[[11:00:35]] [INFO] mMnRNh3NEd=running
[[11:00:35]] [INFO] Executing action 617/643: Tap on image: env[delivery-address-img]
[[11:00:35]] [SUCCESS] Screenshot refreshed successfully
[[11:00:35]] [SUCCESS] Screenshot refreshed successfully
[[11:00:35]] [SUCCESS] Screenshot refreshed
[[11:00:35]] [INFO] Refreshing screenshot...
[[11:00:35]] [INFO] NcU6aex76k=pass
[[11:00:31]] [INFO] NcU6aex76k=running
[[11:00:31]] [INFO] Executing action 616/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[11:00:30]] [SUCCESS] Screenshot refreshed successfully
[[11:00:30]] [SUCCESS] Screenshot refreshed successfully
[[11:00:30]] [SUCCESS] Screenshot refreshed
[[11:00:30]] [INFO] Refreshing screenshot...
[[11:00:30]] [INFO] SQ1i1ElZCE=pass
[[11:00:23]] [INFO] SQ1i1ElZCE=running
[[11:00:23]] [INFO] Executing action 615/643: Tap and Type at (54, 304): "305 238 Flinders"
[[11:00:23]] [SUCCESS] Screenshot refreshed successfully
[[11:00:23]] [SUCCESS] Screenshot refreshed successfully
[[11:00:22]] [SUCCESS] Screenshot refreshed
[[11:00:22]] [INFO] Refreshing screenshot...
[[11:00:22]] [INFO] 5ZzW1VVSzy=pass
[[11:00:18]] [INFO] 5ZzW1VVSzy=running
[[11:00:18]] [INFO] Executing action 614/643: Tap on Text: "address"
[[11:00:18]] [SUCCESS] Screenshot refreshed successfully
[[11:00:18]] [SUCCESS] Screenshot refreshed successfully
[[11:00:17]] [SUCCESS] Screenshot refreshed
[[11:00:17]] [INFO] Refreshing screenshot...
[[11:00:17]] [INFO] kDpsm2D3xt=pass
[[11:00:13]] [INFO] kDpsm2D3xt=running
[[11:00:13]] [INFO] Executing action 613/643: iOS Function: text - Text: " "
[[11:00:13]] [SUCCESS] Screenshot refreshed successfully
[[11:00:13]] [SUCCESS] Screenshot refreshed successfully
[[11:00:13]] [SUCCESS] Screenshot refreshed
[[11:00:13]] [INFO] Refreshing screenshot...
[[11:00:13]] [INFO] SFj4Aa7RHQ=pass
[[11:00:06]] [INFO] SFj4Aa7RHQ=running
[[11:00:06]] [INFO] Executing action 612/643: textClear action
[[11:00:06]] [SUCCESS] Screenshot refreshed successfully
[[11:00:06]] [SUCCESS] Screenshot refreshed successfully
[[11:00:06]] [SUCCESS] Screenshot refreshed
[[11:00:06]] [INFO] Refreshing screenshot...
[[11:00:06]] [INFO] yi5EsHEFvc=pass
[[11:00:02]] [INFO] yi5EsHEFvc=running
[[11:00:02]] [INFO] Executing action 611/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[11:00:02]] [SUCCESS] Screenshot refreshed successfully
[[11:00:02]] [SUCCESS] Screenshot refreshed successfully
[[11:00:01]] [SUCCESS] Screenshot refreshed
[[11:00:01]] [INFO] Refreshing screenshot...
[[11:00:01]] [INFO] lWJtKSqlPS=pass
[[10:59:54]] [INFO] lWJtKSqlPS=running
[[10:59:54]] [INFO] Executing action 610/643: textClear action
[[10:59:54]] [SUCCESS] Screenshot refreshed successfully
[[10:59:54]] [SUCCESS] Screenshot refreshed successfully
[[10:59:54]] [SUCCESS] Screenshot refreshed
[[10:59:54]] [INFO] Refreshing screenshot...
[[10:59:54]] [INFO] 9B5MQGTmpP=pass
[[10:59:50]] [INFO] 9B5MQGTmpP=running
[[10:59:50]] [INFO] Executing action 609/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:59:50]] [SUCCESS] Screenshot refreshed successfully
[[10:59:50]] [SUCCESS] Screenshot refreshed successfully
[[10:59:49]] [SUCCESS] Screenshot refreshed
[[10:59:49]] [INFO] Refreshing screenshot...
[[10:59:49]] [INFO] QvuueoTR8W=pass
[[10:59:43]] [INFO] QvuueoTR8W=running
[[10:59:43]] [INFO] Executing action 608/643: textClear action
[[10:59:43]] [SUCCESS] Screenshot refreshed successfully
[[10:59:43]] [SUCCESS] Screenshot refreshed successfully
[[10:59:42]] [SUCCESS] Screenshot refreshed
[[10:59:42]] [INFO] Refreshing screenshot...
[[10:59:42]] [INFO] p8rfQL9ara=pass
[[10:59:39]] [INFO] p8rfQL9ara=running
[[10:59:39]] [INFO] Executing action 607/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[10:59:38]] [SUCCESS] Screenshot refreshed successfully
[[10:59:38]] [SUCCESS] Screenshot refreshed successfully
[[10:59:38]] [SUCCESS] Screenshot refreshed
[[10:59:38]] [INFO] Refreshing screenshot...
[[10:59:38]] [INFO] CLMmkV1OIM=pass
[[10:59:31]] [INFO] CLMmkV1OIM=running
[[10:59:31]] [INFO] Executing action 606/643: textClear action
[[10:59:31]] [SUCCESS] Screenshot refreshed successfully
[[10:59:31]] [SUCCESS] Screenshot refreshed successfully
[[10:59:31]] [SUCCESS] Screenshot refreshed
[[10:59:31]] [INFO] Refreshing screenshot...
[[10:59:31]] [INFO] h9trcMrvxt=pass
[[10:59:27]] [INFO] h9trcMrvxt=running
[[10:59:27]] [INFO] Executing action 605/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[10:59:27]] [SUCCESS] Screenshot refreshed successfully
[[10:59:27]] [SUCCESS] Screenshot refreshed successfully
[[10:59:27]] [SUCCESS] Screenshot refreshed
[[10:59:27]] [INFO] Refreshing screenshot...
[[10:59:27]] [INFO] Q5A0cNaJ24=pass
[[10:59:23]] [INFO] Q5A0cNaJ24=running
[[10:59:23]] [INFO] Executing action 604/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[10:59:23]] [SUCCESS] Screenshot refreshed successfully
[[10:59:23]] [SUCCESS] Screenshot refreshed successfully
[[10:59:22]] [SUCCESS] Screenshot refreshed
[[10:59:22]] [INFO] Refreshing screenshot...
[[10:59:22]] [INFO] xAa049Qgls=pass
[[10:58:54]] [INFO] xAa049Qgls=running
[[10:58:54]] [INFO] Executing action 603/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[10:58:54]] [SUCCESS] Screenshot refreshed successfully
[[10:58:54]] [SUCCESS] Screenshot refreshed successfully
[[10:58:54]] [SUCCESS] Screenshot refreshed
[[10:58:54]] [INFO] Refreshing screenshot...
[[10:58:54]] [INFO] hwdyCKFAUJ=pass
[[10:58:50]] [INFO] hwdyCKFAUJ=running
[[10:58:50]] [INFO] Executing action 602/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[10:58:50]] [SUCCESS] Screenshot refreshed successfully
[[10:58:50]] [SUCCESS] Screenshot refreshed successfully
[[10:58:50]] [SUCCESS] Screenshot refreshed
[[10:58:50]] [INFO] Refreshing screenshot...
[[10:58:50]] [INFO] aqBkqyVhrZ=pass
[[10:58:46]] [INFO] aqBkqyVhrZ=running
[[10:58:46]] [INFO] Executing action 601/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[10:58:46]] [SUCCESS] Screenshot refreshed successfully
[[10:58:46]] [SUCCESS] Screenshot refreshed successfully
[[10:58:46]] [SUCCESS] Screenshot refreshed
[[10:58:46]] [INFO] Refreshing screenshot...
[[10:58:46]] [INFO] E3RDcrIH6J=pass
[[10:58:34]] [INFO] E3RDcrIH6J=running
[[10:58:34]] [INFO] Executing action 600/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[10:58:33]] [SUCCESS] Screenshot refreshed successfully
[[10:58:33]] [SUCCESS] Screenshot refreshed successfully
[[10:58:33]] [SUCCESS] Screenshot refreshed
[[10:58:33]] [INFO] Refreshing screenshot...
[[10:58:33]] [INFO] gPYNwJ0HKo=pass
[[10:58:29]] [INFO] gPYNwJ0HKo=running
[[10:58:29]] [INFO] Executing action 599/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[10:58:29]] [SUCCESS] Screenshot refreshed successfully
[[10:58:29]] [SUCCESS] Screenshot refreshed successfully
[[10:58:28]] [SUCCESS] Screenshot refreshed
[[10:58:28]] [INFO] Refreshing screenshot...
[[10:58:28]] [INFO] VLrfDHfkI8=pass
[[10:58:22]] [INFO] VLrfDHfkI8=running
[[10:58:22]] [INFO] Executing action 598/643: Tap on element with accessibility_id: Add to bag
[[10:58:21]] [SUCCESS] Screenshot refreshed successfully
[[10:58:21]] [SUCCESS] Screenshot refreshed successfully
[[10:58:21]] [SUCCESS] Screenshot refreshed
[[10:58:21]] [INFO] Refreshing screenshot...
[[10:58:21]] [INFO] PzxTDnwsZ7=pass
[[10:58:17]] [INFO] PzxTDnwsZ7=running
[[10:58:17]] [INFO] Executing action 597/643: Swipe from (50%, 70%) to (50%, 50%)
[[10:58:16]] [SUCCESS] Screenshot refreshed successfully
[[10:58:16]] [SUCCESS] Screenshot refreshed successfully
[[10:58:16]] [SUCCESS] Screenshot refreshed
[[10:58:16]] [INFO] Refreshing screenshot...
[[10:58:16]] [INFO] 6GkdPPZo8e=pass
[[10:58:12]] [INFO] 6GkdPPZo8e=running
[[10:58:12]] [INFO] Executing action 596/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:58:12]] [SUCCESS] Screenshot refreshed successfully
[[10:58:12]] [SUCCESS] Screenshot refreshed successfully
[[10:58:12]] [SUCCESS] Screenshot refreshed
[[10:58:12]] [INFO] Refreshing screenshot...
[[10:58:12]] [INFO] FSM5PqLDko=pass
[[10:58:09]] [INFO] FSM5PqLDko=running
[[10:58:09]] [INFO] Executing action 595/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[10:58:08]] [SUCCESS] Screenshot refreshed successfully
[[10:58:08]] [SUCCESS] Screenshot refreshed successfully
[[10:58:08]] [SUCCESS] Screenshot refreshed
[[10:58:08]] [INFO] Refreshing screenshot...
[[10:58:08]] [INFO] GPTMDcrFC2=pass
[[10:58:04]] [INFO] GPTMDcrFC2=running
[[10:58:04]] [INFO] Executing action 594/643: iOS Function: text - Text: "P_42691341"
[[10:58:04]] [SUCCESS] Screenshot refreshed successfully
[[10:58:04]] [SUCCESS] Screenshot refreshed successfully
[[10:58:04]] [SUCCESS] Screenshot refreshed
[[10:58:04]] [INFO] Refreshing screenshot...
[[10:58:04]] [INFO] 91WZz4k3NI=pass
[[10:57:59]] [INFO] 91WZz4k3NI=running
[[10:57:59]] [INFO] Executing action 593/643: Tap on Text: "Find"
[[10:57:58]] [SUCCESS] Screenshot refreshed successfully
[[10:57:58]] [SUCCESS] Screenshot refreshed successfully
[[10:57:58]] [SUCCESS] Screenshot refreshed
[[10:57:58]] [INFO] Refreshing screenshot...
[[10:57:58]] [INFO] ACaNCAo69V=pass
[[10:57:52]] [SUCCESS] Screenshot refreshed successfully
[[10:57:52]] [SUCCESS] Screenshot refreshed successfully
[[10:57:52]] [INFO] ACaNCAo69V=running
[[10:57:52]] [INFO] Executing action 592/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[10:57:52]] [SUCCESS] Screenshot refreshed
[[10:57:52]] [INFO] Refreshing screenshot...
[[10:57:52]] [SUCCESS] Screenshot refreshed successfully
[[10:57:52]] [SUCCESS] Screenshot refreshed successfully
[[10:57:52]] [SUCCESS] Screenshot refreshed
[[10:57:52]] [INFO] Refreshing screenshot...
[[10:57:47]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:57:47]] [SUCCESS] Screenshot refreshed successfully
[[10:57:47]] [SUCCESS] Screenshot refreshed successfully
[[10:57:47]] [SUCCESS] Screenshot refreshed
[[10:57:47]] [INFO] Refreshing screenshot...
[[10:57:43]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:57:42]] [SUCCESS] Screenshot refreshed successfully
[[10:57:42]] [SUCCESS] Screenshot refreshed successfully
[[10:57:42]] [SUCCESS] Screenshot refreshed
[[10:57:42]] [INFO] Refreshing screenshot...
[[10:57:38]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:57:37]] [SUCCESS] Screenshot refreshed successfully
[[10:57:37]] [SUCCESS] Screenshot refreshed successfully
[[10:57:37]] [SUCCESS] Screenshot refreshed
[[10:57:37]] [INFO] Refreshing screenshot...
[[10:57:33]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:57:33]] [SUCCESS] Screenshot refreshed successfully
[[10:57:33]] [SUCCESS] Screenshot refreshed successfully
[[10:57:33]] [SUCCESS] Screenshot refreshed
[[10:57:33]] [INFO] Refreshing screenshot...
[[10:57:27]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:57:27]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[10:57:27]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[10:57:27]] [INFO] JEpLBji8jZ=running
[[10:57:27]] [INFO] Executing action 591/643: Execute Test Case: Kmart-Signin (5 steps)
[[10:57:27]] [SUCCESS] Screenshot refreshed successfully
[[10:57:27]] [SUCCESS] Screenshot refreshed successfully
[[10:57:27]] [SUCCESS] Screenshot refreshed
[[10:57:27]] [INFO] Refreshing screenshot...
[[10:57:27]] [INFO] TrbMRAIV8i=pass
[[10:57:24]] [INFO] TrbMRAIV8i=running
[[10:57:24]] [INFO] Executing action 590/643: iOS Function: alert_accept
[[10:57:24]] [SUCCESS] Screenshot refreshed successfully
[[10:57:24]] [SUCCESS] Screenshot refreshed successfully
[[10:57:24]] [SUCCESS] Screenshot refreshed
[[10:57:24]] [INFO] Refreshing screenshot...
[[10:57:24]] [INFO] MxtVneSHFi=pass
[[10:57:17]] [INFO] MxtVneSHFi=running
[[10:57:17]] [INFO] Executing action 589/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:57:17]] [SUCCESS] Screenshot refreshed successfully
[[10:57:17]] [SUCCESS] Screenshot refreshed successfully
[[10:57:16]] [SUCCESS] Screenshot refreshed
[[10:57:16]] [INFO] Refreshing screenshot...
[[10:57:16]] [INFO] 3uORTsBIAg=pass
[[10:57:13]] [INFO] 3uORTsBIAg=running
[[10:57:13]] [INFO] Executing action 588/643: Restart app: au.com.kmart
[[10:57:12]] [SUCCESS] Screenshot refreshed successfully
[[10:57:12]] [SUCCESS] Screenshot refreshed successfully
[[10:57:12]] [SUCCESS] Screenshot refreshed
[[10:57:12]] [INFO] Refreshing screenshot...
[[10:57:12]] [INFO] K8uGC1LDOS=pass
[[10:57:01]] [SUCCESS] Screenshot refreshed successfully
[[10:57:01]] [SUCCESS] Screenshot refreshed successfully
[[10:57:00]] [INFO] K8uGC1LDOS=running
[[10:57:00]] [INFO] Executing action 587/643: Terminate app: au.com.kmart
[[10:57:00]] [SUCCESS] Screenshot refreshed
[[10:57:00]] [INFO] Refreshing screenshot...
[[10:57:00]] [SUCCESS] Screenshot refreshed successfully
[[10:57:00]] [SUCCESS] Screenshot refreshed successfully
[[10:57:00]] [SUCCESS] Screenshot refreshed
[[10:57:00]] [INFO] Refreshing screenshot...
[[10:56:58]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:56:57]] [SUCCESS] Screenshot refreshed successfully
[[10:56:57]] [SUCCESS] Screenshot refreshed successfully
[[10:56:57]] [SUCCESS] Screenshot refreshed
[[10:56:57]] [INFO] Refreshing screenshot...
[[10:56:45]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:56:44]] [SUCCESS] Screenshot refreshed successfully
[[10:56:44]] [SUCCESS] Screenshot refreshed successfully
[[10:56:44]] [SUCCESS] Screenshot refreshed
[[10:56:44]] [INFO] Refreshing screenshot...
[[10:56:41]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:56:40]] [SUCCESS] Screenshot refreshed successfully
[[10:56:40]] [SUCCESS] Screenshot refreshed successfully
[[10:56:40]] [SUCCESS] Screenshot refreshed
[[10:56:40]] [INFO] Refreshing screenshot...
[[10:56:36]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:56:36]] [SUCCESS] Screenshot refreshed successfully
[[10:56:36]] [SUCCESS] Screenshot refreshed successfully
[[10:56:36]] [SUCCESS] Screenshot refreshed
[[10:56:36]] [INFO] Refreshing screenshot...
[[10:56:29]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:56:29]] [SUCCESS] Screenshot refreshed successfully
[[10:56:29]] [SUCCESS] Screenshot refreshed successfully
[[10:56:29]] [SUCCESS] Screenshot refreshed
[[10:56:29]] [INFO] Refreshing screenshot...
[[10:56:23]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:56:23]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:56:23]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:56:23]] [INFO] Ll4UlkE3L9=running
[[10:56:23]] [INFO] Executing action 586/643: cleanupSteps action
[[10:56:23]] [SUCCESS] Screenshot refreshed successfully
[[10:56:23]] [SUCCESS] Screenshot refreshed successfully
[[10:56:23]] [SUCCESS] Screenshot refreshed
[[10:56:23]] [INFO] Refreshing screenshot...
[[10:56:23]] [INFO] 25UEKPIknm=pass
[[10:56:20]] [INFO] 25UEKPIknm=running
[[10:56:20]] [INFO] Executing action 585/643: Terminate app: env[appid]
[[10:56:20]] [SUCCESS] Screenshot refreshed successfully
[[10:56:20]] [SUCCESS] Screenshot refreshed successfully
[[10:56:19]] [SUCCESS] Screenshot refreshed
[[10:56:19]] [INFO] Refreshing screenshot...
[[10:56:19]] [INFO] UqgDn5CuPY=pass
[[10:56:16]] [INFO] UqgDn5CuPY=running
[[10:56:16]] [INFO] Executing action 584/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[10:56:16]] [SUCCESS] Screenshot refreshed successfully
[[10:56:16]] [SUCCESS] Screenshot refreshed successfully
[[10:56:16]] [SUCCESS] Screenshot refreshed
[[10:56:16]] [INFO] Refreshing screenshot...
[[10:56:16]] [INFO] VfTTTtrliQ=pass
[[10:56:13]] [INFO] VfTTTtrliQ=running
[[10:56:13]] [INFO] Executing action 583/643: iOS Function: alert_accept
[[10:56:13]] [SUCCESS] Screenshot refreshed successfully
[[10:56:13]] [SUCCESS] Screenshot refreshed successfully
[[10:56:13]] [SUCCESS] Screenshot refreshed
[[10:56:13]] [INFO] Refreshing screenshot...
[[10:56:13]] [INFO] ipT2XD9io6=pass
[[10:56:09]] [INFO] ipT2XD9io6=running
[[10:56:09]] [INFO] Executing action 582/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[10:56:09]] [SUCCESS] Screenshot refreshed successfully
[[10:56:09]] [SUCCESS] Screenshot refreshed successfully
[[10:56:09]] [SUCCESS] Screenshot refreshed
[[10:56:09]] [INFO] Refreshing screenshot...
[[10:56:09]] [INFO] OKCHAK6HCJ=pass
[[10:56:05]] [INFO] OKCHAK6HCJ=running
[[10:56:05]] [INFO] Executing action 581/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:56:05]] [SUCCESS] Screenshot refreshed successfully
[[10:56:05]] [SUCCESS] Screenshot refreshed successfully
[[10:56:04]] [SUCCESS] Screenshot refreshed
[[10:56:04]] [INFO] Refreshing screenshot...
[[10:56:04]] [INFO] VLlqyGmmr8=pass
[[10:55:52]] [INFO] VLlqyGmmr8=running
[[10:55:52]] [INFO] Executing action 580/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Remove item"]"
[[10:55:52]] [SUCCESS] Screenshot refreshed successfully
[[10:55:52]] [SUCCESS] Screenshot refreshed successfully
[[10:55:52]] [SUCCESS] Screenshot refreshed
[[10:55:52]] [INFO] Refreshing screenshot...
[[10:55:52]] [INFO] rWuyGodCon=pass
[[10:55:39]] [INFO] rWuyGodCon=running
[[10:55:39]] [INFO] Executing action 579/643: Tap if locator exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]"
[[10:55:39]] [SUCCESS] Screenshot refreshed successfully
[[10:55:39]] [SUCCESS] Screenshot refreshed successfully
[[10:55:39]] [SUCCESS] Screenshot refreshed
[[10:55:39]] [INFO] Refreshing screenshot...
[[10:55:39]] [INFO] HlpBHrQZnk=pass
[[10:55:35]] [INFO] HlpBHrQZnk=running
[[10:55:35]] [INFO] Executing action 578/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[10:55:34]] [SUCCESS] Screenshot refreshed successfully
[[10:55:34]] [SUCCESS] Screenshot refreshed successfully
[[10:55:34]] [SUCCESS] Screenshot refreshed
[[10:55:34]] [INFO] Refreshing screenshot...
[[10:55:34]] [INFO] AEnFqnkOa1=pass
[[10:55:31]] [INFO] AEnFqnkOa1=running
[[10:55:31]] [INFO] Executing action 577/643: Tap on image: banner-close-updated.png
[[10:55:30]] [SUCCESS] Screenshot refreshed successfully
[[10:55:30]] [SUCCESS] Screenshot refreshed successfully
[[10:55:30]] [SUCCESS] Screenshot refreshed
[[10:55:30]] [INFO] Refreshing screenshot...
[[10:55:30]] [INFO] z1CfcW4xYT=pass
[[10:55:15]] [INFO] z1CfcW4xYT=running
[[10:55:15]] [INFO] Executing action 576/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[10:55:15]] [SUCCESS] Screenshot refreshed successfully
[[10:55:15]] [SUCCESS] Screenshot refreshed successfully
[[10:55:14]] [SUCCESS] Screenshot refreshed
[[10:55:14]] [INFO] Refreshing screenshot...
[[10:55:14]] [INFO] dJNRgTXoqs=pass
[[10:55:10]] [INFO] dJNRgTXoqs=running
[[10:55:10]] [INFO] Executing action 575/643: Swipe from (50%, 30%) to (50%, 70%)
[[10:55:10]] [SUCCESS] Screenshot refreshed successfully
[[10:55:10]] [SUCCESS] Screenshot refreshed successfully
[[10:55:10]] [SUCCESS] Screenshot refreshed
[[10:55:10]] [INFO] Refreshing screenshot...
[[10:55:10]] [INFO] ceF4VRTJlO=pass
[[10:55:06]] [INFO] ceF4VRTJlO=running
[[10:55:06]] [INFO] Executing action 574/643: Tap on image: banner-close-updated.png
[[10:55:06]] [SUCCESS] Screenshot refreshed successfully
[[10:55:06]] [SUCCESS] Screenshot refreshed successfully
[[10:55:05]] [SUCCESS] Screenshot refreshed
[[10:55:05]] [INFO] Refreshing screenshot...
[[10:55:05]] [INFO] 8hCPyY2zPt=pass
[[10:55:02]] [INFO] 8hCPyY2zPt=running
[[10:55:02]] [INFO] Executing action 573/643: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[10:55:01]] [SUCCESS] Screenshot refreshed successfully
[[10:55:01]] [SUCCESS] Screenshot refreshed successfully
[[10:55:01]] [SUCCESS] Screenshot refreshed
[[10:55:01]] [INFO] Refreshing screenshot...
[[10:55:01]] [INFO] r0FfJ85LFM=pass
[[10:54:58]] [INFO] r0FfJ85LFM=running
[[10:54:58]] [INFO] Executing action 572/643: Tap on image: banner-close-updated.png
[[10:54:57]] [SUCCESS] Screenshot refreshed successfully
[[10:54:57]] [SUCCESS] Screenshot refreshed successfully
[[10:54:57]] [SUCCESS] Screenshot refreshed
[[10:54:57]] [INFO] Refreshing screenshot...
[[10:54:57]] [INFO] 2QEdm5WM18=pass
[[10:54:53]] [INFO] 2QEdm5WM18=running
[[10:54:53]] [INFO] Executing action 571/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[10:54:53]] [SUCCESS] Screenshot refreshed successfully
[[10:54:53]] [SUCCESS] Screenshot refreshed successfully
[[10:54:53]] [SUCCESS] Screenshot refreshed
[[10:54:53]] [INFO] Refreshing screenshot...
[[10:54:53]] [INFO] NW6M15JbAy=pass
[[10:54:35]] [INFO] NW6M15JbAy=running
[[10:54:35]] [INFO] Executing action 570/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[10:54:34]] [SUCCESS] Screenshot refreshed successfully
[[10:54:34]] [SUCCESS] Screenshot refreshed successfully
[[10:54:34]] [SUCCESS] Screenshot refreshed
[[10:54:34]] [INFO] Refreshing screenshot...
[[10:54:34]] [INFO] njiHWyVooT=pass
[[10:54:30]] [INFO] njiHWyVooT=running
[[10:54:30]] [INFO] Executing action 569/643: Tap on image: banner-close-updated.png
[[10:54:30]] [SUCCESS] Screenshot refreshed successfully
[[10:54:30]] [SUCCESS] Screenshot refreshed successfully
[[10:54:30]] [SUCCESS] Screenshot refreshed
[[10:54:30]] [INFO] Refreshing screenshot...
[[10:54:30]] [INFO] 93bAew9Y4Y=pass
[[10:54:26]] [INFO] 93bAew9Y4Y=running
[[10:54:26]] [INFO] Executing action 568/643: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[10:54:25]] [SUCCESS] Screenshot refreshed successfully
[[10:54:25]] [SUCCESS] Screenshot refreshed successfully
[[10:54:25]] [SUCCESS] Screenshot refreshed
[[10:54:25]] [INFO] Refreshing screenshot...
[[10:54:25]] [INFO] rPQ5EkTza1=pass
[[10:54:21]] [SUCCESS] Screenshot refreshed successfully
[[10:54:21]] [SUCCESS] Screenshot refreshed successfully
[[10:54:21]] [INFO] rPQ5EkTza1=running
[[10:54:21]] [INFO] Executing action 567/643: Tap on Text: "Click"
[[10:54:21]] [SUCCESS] Screenshot refreshed
[[10:54:21]] [INFO] Refreshing screenshot...
[[10:54:21]] [SUCCESS] Screenshot refreshed successfully
[[10:54:21]] [SUCCESS] Screenshot refreshed successfully
[[10:54:21]] [SUCCESS] Screenshot refreshed
[[10:54:21]] [INFO] Refreshing screenshot...
[[10:54:17]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[10:54:17]] [SUCCESS] Screenshot refreshed successfully
[[10:54:17]] [SUCCESS] Screenshot refreshed successfully
[[10:54:17]] [SUCCESS] Screenshot refreshed
[[10:54:17]] [INFO] Refreshing screenshot...
[[10:54:05]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[10:54:05]] [SUCCESS] Screenshot refreshed successfully
[[10:54:05]] [SUCCESS] Screenshot refreshed successfully
[[10:54:05]] [SUCCESS] Screenshot refreshed
[[10:54:05]] [INFO] Refreshing screenshot...
[[10:53:58]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[10:53:58]] [SUCCESS] Screenshot refreshed successfully
[[10:53:58]] [SUCCESS] Screenshot refreshed successfully
[[10:53:58]] [SUCCESS] Screenshot refreshed
[[10:53:58]] [INFO] Refreshing screenshot...
[[10:53:54]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[10:53:54]] [SUCCESS] Screenshot refreshed successfully
[[10:53:54]] [SUCCESS] Screenshot refreshed successfully
[[10:53:53]] [SUCCESS] Screenshot refreshed
[[10:53:53]] [INFO] Refreshing screenshot...
[[10:53:50]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[10:53:49]] [SUCCESS] Screenshot refreshed successfully
[[10:53:49]] [SUCCESS] Screenshot refreshed successfully
[[10:53:49]] [SUCCESS] Screenshot refreshed
[[10:53:49]] [INFO] Refreshing screenshot...
[[10:53:46]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[10:53:45]] [SUCCESS] Screenshot refreshed successfully
[[10:53:45]] [SUCCESS] Screenshot refreshed successfully
[[10:53:45]] [SUCCESS] Screenshot refreshed
[[10:53:45]] [INFO] Refreshing screenshot...
[[10:53:41]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[10:53:41]] [SUCCESS] Screenshot refreshed successfully
[[10:53:41]] [SUCCESS] Screenshot refreshed successfully
[[10:53:41]] [SUCCESS] Screenshot refreshed
[[10:53:41]] [INFO] Refreshing screenshot...
[[10:53:34]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[10:53:34]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[10:53:34]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[10:53:34]] [INFO] 0YgZZfWdYY=running
[[10:53:34]] [INFO] Executing action 566/643: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[10:53:33]] [SUCCESS] Screenshot refreshed successfully
[[10:53:33]] [SUCCESS] Screenshot refreshed successfully
[[10:53:33]] [SUCCESS] Screenshot refreshed
[[10:53:33]] [INFO] Refreshing screenshot...
[[10:53:33]] [INFO] arH1CZCPXh=pass
[[10:53:28]] [INFO] arH1CZCPXh=running
[[10:53:28]] [INFO] Executing action 565/643: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[10:53:28]] [SUCCESS] Screenshot refreshed successfully
[[10:53:28]] [SUCCESS] Screenshot refreshed successfully
[[10:53:28]] [SUCCESS] Screenshot refreshed
[[10:53:28]] [INFO] Refreshing screenshot...
[[10:53:28]] [INFO] JLAJhxPdsl=pass
[[10:53:23]] [INFO] JLAJhxPdsl=running
[[10:53:23]] [INFO] Executing action 564/643: Tap on Text: "Cancel"
[[10:53:23]] [SUCCESS] Screenshot refreshed successfully
[[10:53:23]] [SUCCESS] Screenshot refreshed successfully
[[10:53:22]] [SUCCESS] Screenshot refreshed
[[10:53:22]] [INFO] Refreshing screenshot...
[[10:53:22]] [INFO] UqgDn5CuPY=pass
[[10:53:19]] [INFO] UqgDn5CuPY=running
[[10:53:19]] [INFO] Executing action 563/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[10:53:19]] [SUCCESS] Screenshot refreshed successfully
[[10:53:19]] [SUCCESS] Screenshot refreshed successfully
[[10:53:19]] [SUCCESS] Screenshot refreshed
[[10:53:19]] [INFO] Refreshing screenshot...
[[10:53:19]] [INFO] VfTTTtrliQ=pass
[[10:53:16]] [INFO] VfTTTtrliQ=running
[[10:53:16]] [INFO] Executing action 562/643: iOS Function: alert_accept
[[10:53:16]] [SUCCESS] Screenshot refreshed successfully
[[10:53:16]] [SUCCESS] Screenshot refreshed successfully
[[10:53:16]] [SUCCESS] Screenshot refreshed
[[10:53:16]] [INFO] Refreshing screenshot...
[[10:53:16]] [INFO] ipT2XD9io6=pass
[[10:53:12]] [INFO] ipT2XD9io6=running
[[10:53:12]] [INFO] Executing action 561/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[10:53:12]] [SUCCESS] Screenshot refreshed successfully
[[10:53:12]] [SUCCESS] Screenshot refreshed successfully
[[10:53:11]] [SUCCESS] Screenshot refreshed
[[10:53:11]] [INFO] Refreshing screenshot...
[[10:53:11]] [INFO] OKCHAK6HCJ=pass
[[10:53:07]] [INFO] OKCHAK6HCJ=running
[[10:53:07]] [INFO] Executing action 560/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:53:07]] [SUCCESS] Screenshot refreshed successfully
[[10:53:07]] [SUCCESS] Screenshot refreshed successfully
[[10:53:07]] [SUCCESS] Screenshot refreshed
[[10:53:07]] [INFO] Refreshing screenshot...
[[10:53:07]] [INFO] RbD937Xbte=pass
[[10:53:03]] [INFO] RbD937Xbte=running
[[10:53:03]] [INFO] Executing action 559/643: Tap on Text: "out"
[[10:53:02]] [SUCCESS] Screenshot refreshed successfully
[[10:53:02]] [SUCCESS] Screenshot refreshed successfully
[[10:53:02]] [SUCCESS] Screenshot refreshed
[[10:53:02]] [INFO] Refreshing screenshot...
[[10:53:02]] [INFO] ylslyLAYKb=pass
[[10:52:59]] [INFO] ylslyLAYKb=running
[[10:52:59]] [INFO] Executing action 558/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:52:58]] [SUCCESS] Screenshot refreshed successfully
[[10:52:58]] [SUCCESS] Screenshot refreshed successfully
[[10:52:58]] [SUCCESS] Screenshot refreshed
[[10:52:58]] [INFO] Refreshing screenshot...
[[10:52:58]] [INFO] wguGCt7OoB=pass
[[10:52:55]] [INFO] wguGCt7OoB=running
[[10:52:55]] [INFO] Executing action 557/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:52:54]] [SUCCESS] Screenshot refreshed successfully
[[10:52:54]] [SUCCESS] Screenshot refreshed successfully
[[10:52:54]] [SUCCESS] Screenshot refreshed
[[10:52:54]] [INFO] Refreshing screenshot...
[[10:52:54]] [INFO] RDQCFIxjA0=pass
[[10:52:51]] [INFO] RDQCFIxjA0=running
[[10:52:51]] [INFO] Executing action 556/643: Swipe from (90%, 30%) to (30%, 30%)
[[10:52:50]] [SUCCESS] Screenshot refreshed successfully
[[10:52:50]] [SUCCESS] Screenshot refreshed successfully
[[10:52:50]] [SUCCESS] Screenshot refreshed
[[10:52:50]] [INFO] Refreshing screenshot...
[[10:52:50]] [INFO] x4Mid4HQ0Z=pass
[[10:52:47]] [INFO] x4Mid4HQ0Z=running
[[10:52:47]] [INFO] Executing action 555/643: Swipe from (90%, 30%) to (30%, 30%)
[[10:52:46]] [SUCCESS] Screenshot refreshed successfully
[[10:52:46]] [SUCCESS] Screenshot refreshed successfully
[[10:52:46]] [SUCCESS] Screenshot refreshed
[[10:52:46]] [INFO] Refreshing screenshot...
[[10:52:46]] [INFO] wguGCt7OoB=pass
[[10:52:42]] [INFO] wguGCt7OoB=running
[[10:52:42]] [INFO] Executing action 554/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[10:52:42]] [SUCCESS] Screenshot refreshed successfully
[[10:52:42]] [SUCCESS] Screenshot refreshed successfully
[[10:52:41]] [SUCCESS] Screenshot refreshed
[[10:52:41]] [INFO] Refreshing screenshot...
[[10:52:41]] [INFO] 39pu9NW124=pass
[[10:52:30]] [INFO] 39pu9NW124=running
[[10:52:30]] [INFO] Executing action 553/643: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Select to add") and contains(@name,"to wishlist")]"
[[10:52:30]] [SUCCESS] Screenshot refreshed successfully
[[10:52:30]] [SUCCESS] Screenshot refreshed successfully
[[10:52:29]] [SUCCESS] Screenshot refreshed
[[10:52:29]] [INFO] Refreshing screenshot...
[[10:52:29]] [INFO] ylslyLAYKb=pass
[[10:52:24]] [INFO] ylslyLAYKb=running
[[10:52:24]] [INFO] Executing action 552/643: Swipe from (50%, 70%) to (50%, 40%)
[[10:52:24]] [SUCCESS] Screenshot refreshed successfully
[[10:52:24]] [SUCCESS] Screenshot refreshed successfully
[[10:52:23]] [SUCCESS] Screenshot refreshed
[[10:52:23]] [INFO] Refreshing screenshot...
[[10:52:23]] [INFO] 0bnBNoqPt8=pass
[[10:52:20]] [INFO] 0bnBNoqPt8=running
[[10:52:20]] [INFO] Executing action 551/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:52:19]] [SUCCESS] Screenshot refreshed successfully
[[10:52:19]] [SUCCESS] Screenshot refreshed successfully
[[10:52:19]] [SUCCESS] Screenshot refreshed
[[10:52:19]] [INFO] Refreshing screenshot...
[[10:52:19]] [INFO] xmelRkcdVx=pass
[[10:52:15]] [INFO] xmelRkcdVx=running
[[10:52:15]] [INFO] Executing action 550/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:52:15]] [SUCCESS] Screenshot refreshed successfully
[[10:52:15]] [SUCCESS] Screenshot refreshed successfully
[[10:52:15]] [SUCCESS] Screenshot refreshed
[[10:52:15]] [INFO] Refreshing screenshot...
[[10:52:15]] [INFO] ksCBjJiwHZ=pass
[[10:52:11]] [INFO] ksCBjJiwHZ=running
[[10:52:11]] [INFO] Executing action 549/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[10:52:11]] [SUCCESS] Screenshot refreshed successfully
[[10:52:11]] [SUCCESS] Screenshot refreshed successfully
[[10:52:11]] [SUCCESS] Screenshot refreshed
[[10:52:11]] [INFO] Refreshing screenshot...
[[10:52:11]] [INFO] d40Oo7famr=pass
[[10:52:07]] [INFO] d40Oo7famr=running
[[10:52:07]] [INFO] Executing action 548/643: iOS Function: text - Text: "env[cooker-id]"
[[10:52:06]] [SUCCESS] Screenshot refreshed successfully
[[10:52:06]] [SUCCESS] Screenshot refreshed successfully
[[10:52:06]] [SUCCESS] Screenshot refreshed
[[10:52:06]] [INFO] Refreshing screenshot...
[[10:52:06]] [INFO] ewuLtuqVuo=pass
[[10:52:01]] [INFO] ewuLtuqVuo=running
[[10:52:01]] [INFO] Executing action 547/643: Tap on Text: "Find"
[[10:52:01]] [SUCCESS] Screenshot refreshed successfully
[[10:52:01]] [SUCCESS] Screenshot refreshed successfully
[[10:52:01]] [SUCCESS] Screenshot refreshed
[[10:52:01]] [INFO] Refreshing screenshot...
[[10:52:01]] [INFO] GTXmST3hEA=pass
[[10:51:55]] [INFO] GTXmST3hEA=running
[[10:51:55]] [INFO] Executing action 546/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:51:55]] [SUCCESS] Screenshot refreshed successfully
[[10:51:55]] [SUCCESS] Screenshot refreshed successfully
[[10:51:55]] [SUCCESS] Screenshot refreshed
[[10:51:55]] [INFO] Refreshing screenshot...
[[10:51:55]] [INFO] qkZ5KShdEU=pass
[[10:51:50]] [INFO] qkZ5KShdEU=running
[[10:51:50]] [INFO] Executing action 545/643: iOS Function: text - Text: "env[pwd]"
[[10:51:50]] [SUCCESS] Screenshot refreshed successfully
[[10:51:50]] [SUCCESS] Screenshot refreshed successfully
[[10:51:50]] [SUCCESS] Screenshot refreshed
[[10:51:50]] [INFO] Refreshing screenshot...
[[10:51:50]] [INFO] 7g2LmvjtEZ=pass
[[10:51:46]] [INFO] 7g2LmvjtEZ=running
[[10:51:46]] [INFO] Executing action 544/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:51:46]] [SUCCESS] Screenshot refreshed successfully
[[10:51:46]] [SUCCESS] Screenshot refreshed successfully
[[10:51:45]] [SUCCESS] Screenshot refreshed
[[10:51:45]] [INFO] Refreshing screenshot...
[[10:51:45]] [INFO] OUT2ASweb6=pass
[[10:51:41]] [INFO] OUT2ASweb6=running
[[10:51:41]] [INFO] Executing action 543/643: iOS Function: text - Text: "env[uname]"
[[10:51:40]] [SUCCESS] Screenshot refreshed successfully
[[10:51:40]] [SUCCESS] Screenshot refreshed successfully
[[10:51:40]] [SUCCESS] Screenshot refreshed
[[10:51:40]] [INFO] Refreshing screenshot...
[[10:51:40]] [INFO] TV4kJIIV9v=pass
[[10:51:36]] [INFO] TV4kJIIV9v=running
[[10:51:36]] [INFO] Executing action 542/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:51:36]] [SUCCESS] Screenshot refreshed successfully
[[10:51:36]] [SUCCESS] Screenshot refreshed successfully
[[10:51:36]] [SUCCESS] Screenshot refreshed
[[10:51:36]] [INFO] Refreshing screenshot...
[[10:51:36]] [INFO] kQJbqm7uCi=pass
[[10:51:33]] [INFO] kQJbqm7uCi=running
[[10:51:33]] [INFO] Executing action 541/643: iOS Function: alert_accept
[[10:51:33]] [SUCCESS] Screenshot refreshed successfully
[[10:51:33]] [SUCCESS] Screenshot refreshed successfully
[[10:51:33]] [SUCCESS] Screenshot refreshed
[[10:51:33]] [INFO] Refreshing screenshot...
[[10:51:33]] [INFO] SPE01N6pyp=pass
[[10:51:27]] [INFO] SPE01N6pyp=running
[[10:51:27]] [INFO] Executing action 540/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:51:27]] [SUCCESS] Screenshot refreshed successfully
[[10:51:27]] [SUCCESS] Screenshot refreshed successfully
[[10:51:26]] [SUCCESS] Screenshot refreshed
[[10:51:26]] [INFO] Refreshing screenshot...
[[10:51:26]] [INFO] WEB5St2Mb7=pass
[[10:51:23]] [INFO] WEB5St2Mb7=running
[[10:51:23]] [INFO] Executing action 539/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:51:22]] [SUCCESS] Screenshot refreshed successfully
[[10:51:22]] [SUCCESS] Screenshot refreshed successfully
[[10:51:22]] [SUCCESS] Screenshot refreshed
[[10:51:22]] [INFO] Refreshing screenshot...
[[10:51:22]] [INFO] To7bij5MnF=pass
[[10:51:17]] [INFO] To7bij5MnF=running
[[10:51:17]] [INFO] Executing action 538/643: Swipe from (5%, 50%) to (90%, 50%)
[[10:51:17]] [SUCCESS] Screenshot refreshed successfully
[[10:51:17]] [SUCCESS] Screenshot refreshed successfully
[[10:51:17]] [SUCCESS] Screenshot refreshed
[[10:51:17]] [INFO] Refreshing screenshot...
[[10:51:17]] [INFO] NkybTKfs2U=pass
[[10:51:11]] [INFO] NkybTKfs2U=running
[[10:51:11]] [INFO] Executing action 537/643: Swipe from (5%, 50%) to (90%, 50%)
[[10:51:11]] [SUCCESS] Screenshot refreshed successfully
[[10:51:11]] [SUCCESS] Screenshot refreshed successfully
[[10:51:11]] [SUCCESS] Screenshot refreshed
[[10:51:11]] [INFO] Refreshing screenshot...
[[10:51:11]] [INFO] dYEtjrv6lz=pass
[[10:51:07]] [INFO] dYEtjrv6lz=running
[[10:51:07]] [INFO] Executing action 536/643: Tap on Text: "Months"
[[10:51:07]] [SUCCESS] Screenshot refreshed successfully
[[10:51:07]] [SUCCESS] Screenshot refreshed successfully
[[10:51:06]] [SUCCESS] Screenshot refreshed
[[10:51:06]] [INFO] Refreshing screenshot...
[[10:51:06]] [INFO] eGQ7VrKUSo=pass
[[10:51:02]] [INFO] eGQ7VrKUSo=running
[[10:51:02]] [INFO] Executing action 535/643: Tap on Text: "Age"
[[10:51:02]] [SUCCESS] Screenshot refreshed successfully
[[10:51:02]] [SUCCESS] Screenshot refreshed successfully
[[10:51:02]] [SUCCESS] Screenshot refreshed
[[10:51:02]] [INFO] Refreshing screenshot...
[[10:51:02]] [INFO] zNRPvs2cC4=pass
[[10:50:58]] [INFO] zNRPvs2cC4=running
[[10:50:58]] [INFO] Executing action 534/643: Tap on Text: "Toys"
[[10:50:58]] [SUCCESS] Screenshot refreshed successfully
[[10:50:58]] [SUCCESS] Screenshot refreshed successfully
[[10:50:57]] [SUCCESS] Screenshot refreshed
[[10:50:57]] [INFO] Refreshing screenshot...
[[10:50:57]] [INFO] KyyS139agr=pass
[[10:50:53]] [INFO] KyyS139agr=running
[[10:50:53]] [INFO] Executing action 533/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[10:50:53]] [SUCCESS] Screenshot refreshed successfully
[[10:50:53]] [SUCCESS] Screenshot refreshed successfully
[[10:50:52]] [SUCCESS] Screenshot refreshed
[[10:50:52]] [INFO] Refreshing screenshot...
[[10:50:52]] [INFO] 5e4LeoW1YU=pass
[[10:50:48]] [SUCCESS] Screenshot refreshed successfully
[[10:50:48]] [SUCCESS] Screenshot refreshed successfully
[[10:50:47]] [INFO] 5e4LeoW1YU=running
[[10:50:47]] [INFO] Executing action 532/643: Restart app: env[appid]
[[10:50:47]] [SUCCESS] Screenshot refreshed
[[10:50:47]] [INFO] Refreshing screenshot...
[[10:50:47]] [SUCCESS] Screenshot refreshed successfully
[[10:50:47]] [SUCCESS] Screenshot refreshed successfully
[[10:50:47]] [SUCCESS] Screenshot refreshed
[[10:50:47]] [INFO] Refreshing screenshot...
[[10:50:29]] [INFO] Executing Multi Step action step 8/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[10:50:29]] [SUCCESS] Screenshot refreshed successfully
[[10:50:29]] [SUCCESS] Screenshot refreshed successfully
[[10:50:29]] [SUCCESS] Screenshot refreshed
[[10:50:29]] [INFO] Refreshing screenshot...
[[10:49:44]] [INFO] Executing Multi Step action step 7/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:49:44]] [SUCCESS] Screenshot refreshed successfully
[[10:49:44]] [SUCCESS] Screenshot refreshed successfully
[[10:49:44]] [SUCCESS] Screenshot refreshed
[[10:49:44]] [INFO] Refreshing screenshot...
[[10:49:27]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[10:49:27]] [SUCCESS] Screenshot refreshed successfully
[[10:49:27]] [SUCCESS] Screenshot refreshed successfully
[[10:49:26]] [SUCCESS] Screenshot refreshed
[[10:49:26]] [INFO] Refreshing screenshot...
[[10:48:43]] [INFO] Executing Multi Step action step 5/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:48:42]] [SUCCESS] Screenshot refreshed successfully
[[10:48:42]] [SUCCESS] Screenshot refreshed successfully
[[10:48:42]] [SUCCESS] Screenshot refreshed
[[10:48:42]] [INFO] Refreshing screenshot...
[[10:48:25]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[10:48:25]] [SUCCESS] Screenshot refreshed successfully
[[10:48:25]] [SUCCESS] Screenshot refreshed successfully
[[10:48:24]] [SUCCESS] Screenshot refreshed
[[10:48:24]] [INFO] Refreshing screenshot...
[[10:47:40]] [INFO] Executing Multi Step action step 3/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:47:40]] [SUCCESS] Screenshot refreshed successfully
[[10:47:40]] [SUCCESS] Screenshot refreshed successfully
[[10:47:40]] [SUCCESS] Screenshot refreshed
[[10:47:40]] [INFO] Refreshing screenshot...
[[10:47:22]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[10:47:22]] [SUCCESS] Screenshot refreshed successfully
[[10:47:22]] [SUCCESS] Screenshot refreshed successfully
[[10:47:22]] [SUCCESS] Screenshot refreshed
[[10:47:22]] [INFO] Refreshing screenshot...
[[10:46:35]] [INFO] Executing Multi Step action step 1/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:46:35]] [INFO] Loaded 8 steps from test case: Click_Paginations
[[10:46:35]] [INFO] Loading steps for multiStep action: Click_Paginations
[[10:46:35]] [INFO] Z86xBjGUKY=running
[[10:46:35]] [INFO] Executing action 531/643: Execute Test Case: Click_Paginations (8 steps)
[[10:46:35]] [SUCCESS] Screenshot refreshed successfully
[[10:46:35]] [SUCCESS] Screenshot refreshed successfully
[[10:46:35]] [SUCCESS] Screenshot refreshed
[[10:46:35]] [INFO] Refreshing screenshot...
[[10:46:35]] [INFO] IL6kON0uQ9=pass
[[10:46:30]] [INFO] IL6kON0uQ9=running
[[10:46:30]] [INFO] Executing action 530/643: iOS Function: text - Text: "kids toys"
[[10:46:30]] [SUCCESS] Screenshot refreshed successfully
[[10:46:30]] [SUCCESS] Screenshot refreshed successfully
[[10:46:30]] [SUCCESS] Screenshot refreshed
[[10:46:30]] [INFO] Refreshing screenshot...
[[10:46:30]] [INFO] 6G6P3UE7Uy=pass
[[10:46:25]] [INFO] 6G6P3UE7Uy=running
[[10:46:25]] [INFO] Executing action 529/643: Tap on Text: "Find"
[[10:46:25]] [SUCCESS] Screenshot refreshed successfully
[[10:46:25]] [SUCCESS] Screenshot refreshed successfully
[[10:46:24]] [SUCCESS] Screenshot refreshed
[[10:46:24]] [INFO] Refreshing screenshot...
[[10:46:24]] [INFO] 7xs3GiydGF=pass
[[10:46:21]] [INFO] 7xs3GiydGF=running
[[10:46:21]] [INFO] Executing action 528/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:46:20]] [SUCCESS] Screenshot refreshed successfully
[[10:46:20]] [SUCCESS] Screenshot refreshed successfully
[[10:46:20]] [SUCCESS] Screenshot refreshed
[[10:46:20]] [INFO] Refreshing screenshot...
[[10:46:20]] [INFO] VqSa9z9R2Q=pass
[[10:46:19]] [INFO] VqSa9z9R2Q=running
[[10:46:19]] [INFO] Executing action 527/643: Launch app: env[appid]
[[10:46:18]] [SUCCESS] Screenshot refreshed successfully
[[10:46:18]] [SUCCESS] Screenshot refreshed successfully
[[10:46:18]] [SUCCESS] Screenshot refreshed
[[10:46:18]] [INFO] Refreshing screenshot...
[[10:46:18]] [INFO] RHEU77LRMw=pass
[[10:46:15]] [INFO] RHEU77LRMw=running
[[10:46:15]] [INFO] Executing action 526/643: Tap on Text: "+61"
[[10:46:14]] [SUCCESS] Screenshot refreshed successfully
[[10:46:14]] [SUCCESS] Screenshot refreshed successfully
[[10:46:14]] [SUCCESS] Screenshot refreshed
[[10:46:14]] [INFO] Refreshing screenshot...
[[10:46:14]] [INFO] MTRbUlaRvI=pass
[[10:46:08]] [INFO] MTRbUlaRvI=running
[[10:46:08]] [INFO] Executing action 525/643: Tap on Text: "1800"
[[10:46:07]] [SUCCESS] Screenshot refreshed successfully
[[10:46:07]] [SUCCESS] Screenshot refreshed successfully
[[10:46:07]] [SUCCESS] Screenshot refreshed
[[10:46:07]] [INFO] Refreshing screenshot...
[[10:46:07]] [INFO] I0tM87Yjhc=pass
[[10:46:03]] [INFO] I0tM87Yjhc=running
[[10:46:03]] [INFO] Executing action 524/643: Tap on Text: "click"
[[10:46:02]] [SUCCESS] Screenshot refreshed successfully
[[10:46:02]] [SUCCESS] Screenshot refreshed successfully
[[10:46:02]] [SUCCESS] Screenshot refreshed
[[10:46:02]] [INFO] Refreshing screenshot...
[[10:46:02]] [INFO] t6L5vWfBYM=pass
[[10:45:42]] [INFO] t6L5vWfBYM=running
[[10:45:42]] [INFO] Executing action 523/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:45:42]] [SUCCESS] Screenshot refreshed successfully
[[10:45:42]] [SUCCESS] Screenshot refreshed successfully
[[10:45:42]] [SUCCESS] Screenshot refreshed
[[10:45:42]] [INFO] Refreshing screenshot...
[[10:45:42]] [INFO] DhFJzlme9K=pass
[[10:45:38]] [INFO] DhFJzlme9K=running
[[10:45:38]] [INFO] Executing action 522/643: Tap on Text: "FAQ"
[[10:45:38]] [SUCCESS] Screenshot refreshed successfully
[[10:45:38]] [SUCCESS] Screenshot refreshed successfully
[[10:45:37]] [SUCCESS] Screenshot refreshed
[[10:45:37]] [INFO] Refreshing screenshot...
[[10:45:37]] [INFO] g17Boaefhg=pass
[[10:45:33]] [INFO] g17Boaefhg=running
[[10:45:33]] [INFO] Executing action 521/643: Tap on Text: "Help"
[[10:45:33]] [SUCCESS] Screenshot refreshed successfully
[[10:45:33]] [SUCCESS] Screenshot refreshed successfully
[[10:45:33]] [SUCCESS] Screenshot refreshed
[[10:45:33]] [INFO] Refreshing screenshot...
[[10:45:33]] [INFO] nPp27xJcCn=pass
[[10:45:20]] [INFO] nPp27xJcCn=running
[[10:45:20]] [INFO] Executing action 520/643: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[10:45:20]] [SUCCESS] Screenshot refreshed successfully
[[10:45:20]] [SUCCESS] Screenshot refreshed successfully
[[10:45:20]] [SUCCESS] Screenshot refreshed
[[10:45:20]] [INFO] Refreshing screenshot...
[[10:45:20]] [INFO] SqDiBhmyOG=pass
[[10:45:16]] [INFO] SqDiBhmyOG=running
[[10:45:16]] [INFO] Executing action 519/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:45:16]] [SUCCESS] Screenshot refreshed successfully
[[10:45:16]] [SUCCESS] Screenshot refreshed successfully
[[10:45:15]] [SUCCESS] Screenshot refreshed
[[10:45:15]] [INFO] Refreshing screenshot...
[[10:45:15]] [INFO] OR0SKKnFxy=pass
[[10:45:11]] [INFO] OR0SKKnFxy=running
[[10:45:11]] [INFO] Executing action 518/643: Restart app: env[appid]
[[10:45:09]] [INFO] === RETRYING TEST CASE: temp_20250615085036.json (Attempt 2 of 3) ===
[[10:45:09]] [INFO] GTXmST3hEA=fail
[[10:45:09]] [ERROR] Action 546 failed: Element not found: xpath='//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]'
[[10:44:37]] [INFO] GTXmST3hEA=running
[[10:44:37]] [INFO] Executing action 546/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:44:37]] [SUCCESS] Screenshot refreshed successfully
[[10:44:37]] [SUCCESS] Screenshot refreshed successfully
[[10:44:36]] [SUCCESS] Screenshot refreshed
[[10:44:36]] [INFO] Refreshing screenshot...
[[10:44:36]] [INFO] qkZ5KShdEU=pass
[[10:44:32]] [INFO] qkZ5KShdEU=running
[[10:44:32]] [INFO] Executing action 545/643: iOS Function: text - Text: "env[pwd]"
[[10:44:32]] [SUCCESS] Screenshot refreshed successfully
[[10:44:32]] [SUCCESS] Screenshot refreshed successfully
[[10:44:31]] [SUCCESS] Screenshot refreshed
[[10:44:31]] [INFO] Refreshing screenshot...
[[10:44:31]] [INFO] 7g2LmvjtEZ=pass
[[10:44:27]] [INFO] 7g2LmvjtEZ=running
[[10:44:27]] [INFO] Executing action 544/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:44:27]] [SUCCESS] Screenshot refreshed successfully
[[10:44:27]] [SUCCESS] Screenshot refreshed successfully
[[10:44:27]] [SUCCESS] Screenshot refreshed
[[10:44:27]] [INFO] Refreshing screenshot...
[[10:44:27]] [INFO] OUT2ASweb6=pass
[[10:44:22]] [INFO] OUT2ASweb6=running
[[10:44:22]] [INFO] Executing action 543/643: iOS Function: text - Text: "env[uname]"
[[10:44:22]] [SUCCESS] Screenshot refreshed successfully
[[10:44:22]] [SUCCESS] Screenshot refreshed successfully
[[10:44:22]] [SUCCESS] Screenshot refreshed
[[10:44:22]] [INFO] Refreshing screenshot...
[[10:44:22]] [INFO] TV4kJIIV9v=pass
[[10:44:18]] [INFO] TV4kJIIV9v=running
[[10:44:18]] [INFO] Executing action 542/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:44:17]] [SUCCESS] Screenshot refreshed successfully
[[10:44:17]] [SUCCESS] Screenshot refreshed successfully
[[10:44:17]] [SUCCESS] Screenshot refreshed
[[10:44:17]] [INFO] Refreshing screenshot...
[[10:44:17]] [INFO] kQJbqm7uCi=pass
[[10:44:15]] [INFO] kQJbqm7uCi=running
[[10:44:15]] [INFO] Executing action 541/643: iOS Function: alert_accept
[[10:44:14]] [SUCCESS] Screenshot refreshed successfully
[[10:44:14]] [SUCCESS] Screenshot refreshed successfully
[[10:44:14]] [SUCCESS] Screenshot refreshed
[[10:44:14]] [INFO] Refreshing screenshot...
[[10:44:14]] [INFO] SPE01N6pyp=pass
[[10:44:08]] [INFO] SPE01N6pyp=running
[[10:44:08]] [INFO] Executing action 540/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:44:08]] [SUCCESS] Screenshot refreshed successfully
[[10:44:08]] [SUCCESS] Screenshot refreshed successfully
[[10:44:08]] [SUCCESS] Screenshot refreshed
[[10:44:08]] [INFO] Refreshing screenshot...
[[10:44:08]] [INFO] WEB5St2Mb7=pass
[[10:44:04]] [INFO] WEB5St2Mb7=running
[[10:44:04]] [INFO] Executing action 539/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:44:04]] [SUCCESS] Screenshot refreshed successfully
[[10:44:04]] [SUCCESS] Screenshot refreshed successfully
[[10:44:03]] [SUCCESS] Screenshot refreshed
[[10:44:03]] [INFO] Refreshing screenshot...
[[10:44:03]] [INFO] To7bij5MnF=pass
[[10:43:58]] [INFO] To7bij5MnF=running
[[10:43:58]] [INFO] Executing action 538/643: Swipe from (5%, 50%) to (90%, 50%)
[[10:43:58]] [SUCCESS] Screenshot refreshed successfully
[[10:43:58]] [SUCCESS] Screenshot refreshed successfully
[[10:43:58]] [SUCCESS] Screenshot refreshed
[[10:43:58]] [INFO] Refreshing screenshot...
[[10:43:58]] [INFO] NkybTKfs2U=pass
[[10:43:52]] [INFO] NkybTKfs2U=running
[[10:43:52]] [INFO] Executing action 537/643: Swipe from (5%, 50%) to (90%, 50%)
[[10:43:52]] [SUCCESS] Screenshot refreshed successfully
[[10:43:52]] [SUCCESS] Screenshot refreshed successfully
[[10:43:52]] [SUCCESS] Screenshot refreshed
[[10:43:52]] [INFO] Refreshing screenshot...
[[10:43:52]] [INFO] dYEtjrv6lz=pass
[[10:43:48]] [INFO] dYEtjrv6lz=running
[[10:43:48]] [INFO] Executing action 536/643: Tap on Text: "Months"
[[10:43:48]] [SUCCESS] Screenshot refreshed successfully
[[10:43:48]] [SUCCESS] Screenshot refreshed successfully
[[10:43:47]] [SUCCESS] Screenshot refreshed
[[10:43:47]] [INFO] Refreshing screenshot...
[[10:43:47]] [INFO] eGQ7VrKUSo=pass
[[10:43:44]] [INFO] eGQ7VrKUSo=running
[[10:43:44]] [INFO] Executing action 535/643: Tap on Text: "Age"
[[10:43:43]] [SUCCESS] Screenshot refreshed successfully
[[10:43:43]] [SUCCESS] Screenshot refreshed successfully
[[10:43:43]] [SUCCESS] Screenshot refreshed
[[10:43:43]] [INFO] Refreshing screenshot...
[[10:43:43]] [INFO] zNRPvs2cC4=pass
[[10:43:39]] [INFO] zNRPvs2cC4=running
[[10:43:39]] [INFO] Executing action 534/643: Tap on Text: "Toys"
[[10:43:39]] [SUCCESS] Screenshot refreshed successfully
[[10:43:39]] [SUCCESS] Screenshot refreshed successfully
[[10:43:39]] [SUCCESS] Screenshot refreshed
[[10:43:39]] [INFO] Refreshing screenshot...
[[10:43:39]] [INFO] KyyS139agr=pass
[[10:43:34]] [INFO] KyyS139agr=running
[[10:43:34]] [INFO] Executing action 533/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[10:43:34]] [SUCCESS] Screenshot refreshed successfully
[[10:43:34]] [SUCCESS] Screenshot refreshed successfully
[[10:43:34]] [SUCCESS] Screenshot refreshed
[[10:43:34]] [INFO] Refreshing screenshot...
[[10:43:34]] [INFO] 5e4LeoW1YU=pass
[[10:43:29]] [SUCCESS] Screenshot refreshed successfully
[[10:43:29]] [SUCCESS] Screenshot refreshed successfully
[[10:43:29]] [INFO] 5e4LeoW1YU=running
[[10:43:29]] [INFO] Executing action 532/643: Restart app: env[appid]
[[10:43:29]] [SUCCESS] Screenshot refreshed
[[10:43:29]] [INFO] Refreshing screenshot...
[[10:43:29]] [SUCCESS] Screenshot refreshed successfully
[[10:43:29]] [SUCCESS] Screenshot refreshed successfully
[[10:43:29]] [SUCCESS] Screenshot refreshed
[[10:43:29]] [INFO] Refreshing screenshot...
[[10:43:11]] [INFO] Executing Multi Step action step 8/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[10:43:11]] [SUCCESS] Screenshot refreshed successfully
[[10:43:11]] [SUCCESS] Screenshot refreshed successfully
[[10:43:11]] [SUCCESS] Screenshot refreshed
[[10:43:11]] [INFO] Refreshing screenshot...
[[10:42:26]] [INFO] Executing Multi Step action step 7/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:42:26]] [SUCCESS] Screenshot refreshed successfully
[[10:42:26]] [SUCCESS] Screenshot refreshed successfully
[[10:42:26]] [SUCCESS] Screenshot refreshed
[[10:42:26]] [INFO] Refreshing screenshot...
[[10:42:09]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[10:42:09]] [SUCCESS] Screenshot refreshed successfully
[[10:42:09]] [SUCCESS] Screenshot refreshed successfully
[[10:42:09]] [SUCCESS] Screenshot refreshed
[[10:42:09]] [INFO] Refreshing screenshot...
[[10:41:25]] [INFO] Executing Multi Step action step 5/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:41:24]] [SUCCESS] Screenshot refreshed successfully
[[10:41:24]] [SUCCESS] Screenshot refreshed successfully
[[10:41:24]] [SUCCESS] Screenshot refreshed
[[10:41:24]] [INFO] Refreshing screenshot...
[[10:41:07]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[10:41:07]] [SUCCESS] Screenshot refreshed successfully
[[10:41:07]] [SUCCESS] Screenshot refreshed successfully
[[10:41:06]] [SUCCESS] Screenshot refreshed
[[10:41:06]] [INFO] Refreshing screenshot...
[[10:40:22]] [INFO] Executing Multi Step action step 3/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:40:22]] [SUCCESS] Screenshot refreshed successfully
[[10:40:22]] [SUCCESS] Screenshot refreshed successfully
[[10:40:22]] [SUCCESS] Screenshot refreshed
[[10:40:22]] [INFO] Refreshing screenshot...
[[10:40:04]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[10:40:04]] [SUCCESS] Screenshot refreshed successfully
[[10:40:04]] [SUCCESS] Screenshot refreshed successfully
[[10:40:04]] [SUCCESS] Screenshot refreshed
[[10:40:04]] [INFO] Refreshing screenshot...
[[10:39:17]] [INFO] Executing Multi Step action step 1/8: Swipe from (50%, 80%) to (50%, 10%)
[[10:39:17]] [INFO] Loaded 8 steps from test case: Click_Paginations
[[10:39:17]] [INFO] Loading steps for multiStep action: Click_Paginations
[[10:39:17]] [INFO] Z86xBjGUKY=running
[[10:39:17]] [INFO] Executing action 531/643: Execute Test Case: Click_Paginations (8 steps)
[[10:39:17]] [SUCCESS] Screenshot refreshed successfully
[[10:39:17]] [SUCCESS] Screenshot refreshed successfully
[[10:39:17]] [SUCCESS] Screenshot refreshed
[[10:39:17]] [INFO] Refreshing screenshot...
[[10:39:17]] [INFO] IL6kON0uQ9=pass
[[10:39:13]] [INFO] IL6kON0uQ9=running
[[10:39:13]] [INFO] Executing action 530/643: iOS Function: text - Text: "kids toys"
[[10:39:12]] [SUCCESS] Screenshot refreshed successfully
[[10:39:12]] [SUCCESS] Screenshot refreshed successfully
[[10:39:12]] [SUCCESS] Screenshot refreshed
[[10:39:12]] [INFO] Refreshing screenshot...
[[10:39:12]] [INFO] 6G6P3UE7Uy=pass
[[10:39:07]] [INFO] 6G6P3UE7Uy=running
[[10:39:07]] [INFO] Executing action 529/643: Tap on Text: "Find"
[[10:39:07]] [SUCCESS] Screenshot refreshed successfully
[[10:39:07]] [SUCCESS] Screenshot refreshed successfully
[[10:39:07]] [SUCCESS] Screenshot refreshed
[[10:39:07]] [INFO] Refreshing screenshot...
[[10:39:07]] [INFO] 7xs3GiydGF=pass
[[10:39:03]] [INFO] 7xs3GiydGF=running
[[10:39:03]] [INFO] Executing action 528/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:39:02]] [SUCCESS] Screenshot refreshed successfully
[[10:39:02]] [SUCCESS] Screenshot refreshed successfully
[[10:39:02]] [SUCCESS] Screenshot refreshed
[[10:39:02]] [INFO] Refreshing screenshot...
[[10:39:02]] [INFO] VqSa9z9R2Q=pass
[[10:39:01]] [INFO] VqSa9z9R2Q=running
[[10:39:01]] [INFO] Executing action 527/643: Launch app: env[appid]
[[10:39:01]] [SUCCESS] Screenshot refreshed successfully
[[10:39:01]] [SUCCESS] Screenshot refreshed successfully
[[10:39:00]] [SUCCESS] Screenshot refreshed
[[10:39:00]] [INFO] Refreshing screenshot...
[[10:39:00]] [INFO] RHEU77LRMw=pass
[[10:38:57]] [INFO] RHEU77LRMw=running
[[10:38:57]] [INFO] Executing action 526/643: Tap on Text: "+61"
[[10:38:57]] [SUCCESS] Screenshot refreshed successfully
[[10:38:57]] [SUCCESS] Screenshot refreshed successfully
[[10:38:56]] [SUCCESS] Screenshot refreshed
[[10:38:56]] [INFO] Refreshing screenshot...
[[10:38:56]] [INFO] MTRbUlaRvI=pass
[[10:38:52]] [INFO] MTRbUlaRvI=running
[[10:38:52]] [INFO] Executing action 525/643: Tap on Text: "1800"
[[10:38:52]] [SUCCESS] Screenshot refreshed successfully
[[10:38:52]] [SUCCESS] Screenshot refreshed successfully
[[10:38:52]] [SUCCESS] Screenshot refreshed
[[10:38:52]] [INFO] Refreshing screenshot...
[[10:38:52]] [INFO] I0tM87Yjhc=pass
[[10:38:47]] [INFO] I0tM87Yjhc=running
[[10:38:47]] [INFO] Executing action 524/643: Tap on Text: "click"
[[10:38:47]] [SUCCESS] Screenshot refreshed successfully
[[10:38:47]] [SUCCESS] Screenshot refreshed successfully
[[10:38:47]] [SUCCESS] Screenshot refreshed
[[10:38:47]] [INFO] Refreshing screenshot...
[[10:38:47]] [INFO] t6L5vWfBYM=pass
[[10:38:17]] [INFO] t6L5vWfBYM=running
[[10:38:17]] [INFO] Executing action 523/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:38:16]] [SUCCESS] Screenshot refreshed successfully
[[10:38:16]] [SUCCESS] Screenshot refreshed successfully
[[10:38:16]] [SUCCESS] Screenshot refreshed
[[10:38:16]] [INFO] Refreshing screenshot...
[[10:38:16]] [INFO] DhFJzlme9K=pass
[[10:38:12]] [INFO] DhFJzlme9K=running
[[10:38:12]] [INFO] Executing action 522/643: Tap on Text: "FAQ"
[[10:38:12]] [SUCCESS] Screenshot refreshed successfully
[[10:38:12]] [SUCCESS] Screenshot refreshed successfully
[[10:38:12]] [SUCCESS] Screenshot refreshed
[[10:38:12]] [INFO] Refreshing screenshot...
[[10:38:12]] [INFO] g17Boaefhg=pass
[[10:38:08]] [INFO] g17Boaefhg=running
[[10:38:08]] [INFO] Executing action 521/643: Tap on Text: "Help"
[[10:38:07]] [SUCCESS] Screenshot refreshed successfully
[[10:38:07]] [SUCCESS] Screenshot refreshed successfully
[[10:38:07]] [SUCCESS] Screenshot refreshed
[[10:38:07]] [INFO] Refreshing screenshot...
[[10:38:07]] [INFO] nPp27xJcCn=pass
[[10:37:55]] [INFO] nPp27xJcCn=running
[[10:37:55]] [INFO] Executing action 520/643: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[10:37:54]] [SUCCESS] Screenshot refreshed successfully
[[10:37:54]] [SUCCESS] Screenshot refreshed successfully
[[10:37:54]] [SUCCESS] Screenshot refreshed
[[10:37:54]] [INFO] Refreshing screenshot...
[[10:37:54]] [INFO] SqDiBhmyOG=pass
[[10:37:50]] [INFO] SqDiBhmyOG=running
[[10:37:50]] [INFO] Executing action 519/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:37:50]] [SUCCESS] Screenshot refreshed successfully
[[10:37:50]] [SUCCESS] Screenshot refreshed successfully
[[10:37:49]] [SUCCESS] Screenshot refreshed
[[10:37:49]] [INFO] Refreshing screenshot...
[[10:37:49]] [INFO] OR0SKKnFxy=pass
[[10:37:36]] [SUCCESS] Screenshot refreshed successfully
[[10:37:36]] [SUCCESS] Screenshot refreshed successfully
[[10:37:36]] [INFO] OR0SKKnFxy=running
[[10:37:36]] [INFO] Executing action 518/643: Restart app: env[appid]
[[10:37:36]] [SUCCESS] Screenshot refreshed
[[10:37:36]] [INFO] Refreshing screenshot...
[[10:37:36]] [SUCCESS] Screenshot refreshed successfully
[[10:37:36]] [SUCCESS] Screenshot refreshed successfully
[[10:37:35]] [SUCCESS] Screenshot refreshed
[[10:37:35]] [INFO] Refreshing screenshot...
[[10:37:33]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:37:33]] [SUCCESS] Screenshot refreshed successfully
[[10:37:33]] [SUCCESS] Screenshot refreshed successfully
[[10:37:32]] [SUCCESS] Screenshot refreshed
[[10:37:32]] [INFO] Refreshing screenshot...
[[10:37:20]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:37:20]] [SUCCESS] Screenshot refreshed successfully
[[10:37:20]] [SUCCESS] Screenshot refreshed successfully
[[10:37:19]] [SUCCESS] Screenshot refreshed
[[10:37:19]] [INFO] Refreshing screenshot...
[[10:37:16]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:37:16]] [SUCCESS] Screenshot refreshed successfully
[[10:37:16]] [SUCCESS] Screenshot refreshed successfully
[[10:37:16]] [SUCCESS] Screenshot refreshed
[[10:37:16]] [INFO] Refreshing screenshot...
[[10:37:12]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:37:12]] [SUCCESS] Screenshot refreshed successfully
[[10:37:12]] [SUCCESS] Screenshot refreshed successfully
[[10:37:11]] [SUCCESS] Screenshot refreshed
[[10:37:11]] [INFO] Refreshing screenshot...
[[10:37:05]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:37:05]] [SUCCESS] Screenshot refreshed successfully
[[10:37:05]] [SUCCESS] Screenshot refreshed successfully
[[10:37:04]] [SUCCESS] Screenshot refreshed
[[10:37:04]] [INFO] Refreshing screenshot...
[[10:36:58]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:36:58]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:36:58]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:36:58]] [INFO] kPdSiomhwu=running
[[10:36:58]] [INFO] Executing action 517/643: cleanupSteps action
[[10:36:57]] [SUCCESS] Screenshot refreshed successfully
[[10:36:57]] [SUCCESS] Screenshot refreshed successfully
[[10:36:57]] [SUCCESS] Screenshot refreshed
[[10:36:57]] [INFO] Refreshing screenshot...
[[10:36:57]] [INFO] Qb1AArnpCH=pass
[[10:36:51]] [INFO] Qb1AArnpCH=running
[[10:36:51]] [INFO] Executing action 516/643: Wait for 5 ms
[[10:36:50]] [SUCCESS] Screenshot refreshed successfully
[[10:36:50]] [SUCCESS] Screenshot refreshed successfully
[[10:36:50]] [SUCCESS] Screenshot refreshed
[[10:36:50]] [INFO] Refreshing screenshot...
[[10:36:50]] [INFO] yxlzTytgFT=pass
[[10:36:45]] [INFO] yxlzTytgFT=running
[[10:36:45]] [INFO] Executing action 515/643: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Remove")]"
[[10:36:45]] [SUCCESS] Screenshot refreshed successfully
[[10:36:45]] [SUCCESS] Screenshot refreshed successfully
[[10:36:45]] [SUCCESS] Screenshot refreshed
[[10:36:45]] [INFO] Refreshing screenshot...
[[10:36:45]] [INFO] K2w7X1cPdH=pass
[[10:36:40]] [INFO] K2w7X1cPdH=running
[[10:36:40]] [INFO] Executing action 514/643: Swipe from (50%, 50%) to (50%, 30%)
[[10:36:39]] [SUCCESS] Screenshot refreshed successfully
[[10:36:39]] [SUCCESS] Screenshot refreshed successfully
[[10:36:39]] [SUCCESS] Screenshot refreshed
[[10:36:39]] [INFO] Refreshing screenshot...
[[10:36:39]] [INFO] P26OyuqWlb=pass
[[10:36:27]] [INFO] P26OyuqWlb=running
[[10:36:27]] [INFO] Executing action 513/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[10:36:27]] [SUCCESS] Screenshot refreshed successfully
[[10:36:27]] [SUCCESS] Screenshot refreshed successfully
[[10:36:27]] [SUCCESS] Screenshot refreshed
[[10:36:27]] [INFO] Refreshing screenshot...
[[10:36:27]] [INFO] UpUSVInizv=pass
[[10:36:23]] [INFO] UpUSVInizv=running
[[10:36:23]] [INFO] Executing action 512/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[10:36:23]] [SUCCESS] Screenshot refreshed successfully
[[10:36:23]] [SUCCESS] Screenshot refreshed successfully
[[10:36:22]] [SUCCESS] Screenshot refreshed
[[10:36:22]] [INFO] Refreshing screenshot...
[[10:36:22]] [INFO] c4T3INQkzn=pass
[[10:36:18]] [INFO] c4T3INQkzn=running
[[10:36:18]] [INFO] Executing action 511/643: Restart app: env[appid]
[[10:36:17]] [SUCCESS] Screenshot refreshed successfully
[[10:36:17]] [SUCCESS] Screenshot refreshed successfully
[[10:36:17]] [SUCCESS] Screenshot refreshed
[[10:36:17]] [INFO] Refreshing screenshot...
[[10:36:17]] [INFO] Teyz3d55XS=pass
[[10:36:10]] [INFO] Teyz3d55XS=running
[[10:36:10]] [INFO] Executing action 510/643: Tap if locator exists: accessibility_id="Add to bag"
[[10:36:09]] [SUCCESS] Screenshot refreshed successfully
[[10:36:09]] [SUCCESS] Screenshot refreshed successfully
[[10:36:09]] [SUCCESS] Screenshot refreshed
[[10:36:09]] [INFO] Refreshing screenshot...
[[10:36:09]] [INFO] MA2re5cDWr=pass
[[10:36:04]] [INFO] MA2re5cDWr=running
[[10:36:04]] [INFO] Executing action 509/643: Swipe from (50%, 50%) to (50%, 30%)
[[10:36:04]] [SUCCESS] Screenshot refreshed successfully
[[10:36:04]] [SUCCESS] Screenshot refreshed successfully
[[10:36:03]] [SUCCESS] Screenshot refreshed
[[10:36:03]] [INFO] Refreshing screenshot...
[[10:36:03]] [INFO] 2hGhWulI52=pass
[[10:35:59]] [INFO] 2hGhWulI52=running
[[10:35:59]] [INFO] Executing action 508/643: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[10:35:59]] [SUCCESS] Screenshot refreshed successfully
[[10:35:59]] [SUCCESS] Screenshot refreshed successfully
[[10:35:59]] [SUCCESS] Screenshot refreshed
[[10:35:59]] [INFO] Refreshing screenshot...
[[10:35:59]] [INFO] n57KEWjTea=pass
[[10:35:55]] [INFO] n57KEWjTea=running
[[10:35:55]] [INFO] Executing action 507/643: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[10:35:54]] [SUCCESS] Screenshot refreshed successfully
[[10:35:54]] [SUCCESS] Screenshot refreshed successfully
[[10:35:54]] [SUCCESS] Screenshot refreshed
[[10:35:54]] [INFO] Refreshing screenshot...
[[10:35:54]] [INFO] L59V5hqMX9=pass
[[10:35:50]] [INFO] L59V5hqMX9=running
[[10:35:50]] [INFO] Executing action 506/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[10:35:50]] [SUCCESS] Screenshot refreshed successfully
[[10:35:50]] [SUCCESS] Screenshot refreshed successfully
[[10:35:50]] [SUCCESS] Screenshot refreshed
[[10:35:50]] [INFO] Refreshing screenshot...
[[10:35:50]] [INFO] OKiI82VdnE=pass
[[10:35:43]] [INFO] OKiI82VdnE=running
[[10:35:43]] [INFO] Executing action 505/643: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[10:35:43]] [SUCCESS] Screenshot refreshed successfully
[[10:35:43]] [SUCCESS] Screenshot refreshed successfully
[[10:35:43]] [SUCCESS] Screenshot refreshed
[[10:35:43]] [INFO] Refreshing screenshot...
[[10:35:43]] [INFO] 3KNqlNy6Bj=pass
[[10:35:39]] [INFO] 3KNqlNy6Bj=running
[[10:35:39]] [INFO] Executing action 504/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[10:35:38]] [SUCCESS] Screenshot refreshed successfully
[[10:35:38]] [SUCCESS] Screenshot refreshed successfully
[[10:35:38]] [SUCCESS] Screenshot refreshed
[[10:35:38]] [INFO] Refreshing screenshot...
[[10:35:38]] [INFO] 3NOS1fbxZs=pass
[[10:35:35]] [INFO] 3NOS1fbxZs=running
[[10:35:35]] [INFO] Executing action 503/643: Tap on image: banner-close-updated.png
[[10:35:34]] [SUCCESS] Screenshot refreshed successfully
[[10:35:34]] [SUCCESS] Screenshot refreshed successfully
[[10:35:34]] [SUCCESS] Screenshot refreshed
[[10:35:34]] [INFO] Refreshing screenshot...
[[10:35:34]] [INFO] K0c1gL9UK1=pass
[[10:35:30]] [INFO] K0c1gL9UK1=running
[[10:35:30]] [INFO] Executing action 502/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[10:35:30]] [SUCCESS] Screenshot refreshed successfully
[[10:35:30]] [SUCCESS] Screenshot refreshed successfully
[[10:35:30]] [SUCCESS] Screenshot refreshed
[[10:35:30]] [INFO] Refreshing screenshot...
[[10:35:30]] [INFO] IW6uAwdtiW=pass
[[10:35:26]] [INFO] IW6uAwdtiW=running
[[10:35:26]] [INFO] Executing action 501/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[10:35:26]] [SUCCESS] Screenshot refreshed successfully
[[10:35:26]] [SUCCESS] Screenshot refreshed successfully
[[10:35:25]] [SUCCESS] Screenshot refreshed
[[10:35:25]] [INFO] Refreshing screenshot...
[[10:35:25]] [INFO] DbM0d0m6rU=pass
[[10:35:22]] [INFO] DbM0d0m6rU=running
[[10:35:22]] [INFO] Executing action 500/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[10:35:21]] [SUCCESS] Screenshot refreshed successfully
[[10:35:21]] [SUCCESS] Screenshot refreshed successfully
[[10:35:21]] [SUCCESS] Screenshot refreshed
[[10:35:21]] [INFO] Refreshing screenshot...
[[10:35:21]] [INFO] saiPPHQSPa=pass
[[10:35:09]] [INFO] saiPPHQSPa=running
[[10:35:09]] [INFO] Executing action 499/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[10:35:09]] [SUCCESS] Screenshot refreshed successfully
[[10:35:09]] [SUCCESS] Screenshot refreshed successfully
[[10:35:09]] [SUCCESS] Screenshot refreshed
[[10:35:09]] [INFO] Refreshing screenshot...
[[10:35:09]] [INFO] UpUSVInizv=pass
[[10:35:05]] [INFO] UpUSVInizv=running
[[10:35:05]] [INFO] Executing action 498/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[10:35:04]] [SUCCESS] Screenshot refreshed successfully
[[10:35:04]] [SUCCESS] Screenshot refreshed successfully
[[10:35:04]] [SUCCESS] Screenshot refreshed
[[10:35:04]] [INFO] Refreshing screenshot...
[[10:35:04]] [INFO] Iab9zCfpqO=pass
[[10:34:47]] [INFO] Iab9zCfpqO=running
[[10:34:47]] [INFO] Executing action 497/643: Tap on element with accessibility_id: Add to bag
[[10:34:46]] [SUCCESS] Screenshot refreshed successfully
[[10:34:46]] [SUCCESS] Screenshot refreshed successfully
[[10:34:46]] [SUCCESS] Screenshot refreshed
[[10:34:46]] [INFO] Refreshing screenshot...
[[10:34:46]] [INFO] Qy0Y0uJchm=pass
[[10:34:43]] [INFO] Qy0Y0uJchm=running
[[10:34:43]] [INFO] Executing action 496/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[10:34:42]] [SUCCESS] Screenshot refreshed successfully
[[10:34:42]] [SUCCESS] Screenshot refreshed successfully
[[10:34:42]] [SUCCESS] Screenshot refreshed
[[10:34:42]] [INFO] Refreshing screenshot...
[[10:34:42]] [INFO] YHaMIjULRf=pass
[[10:34:37]] [INFO] YHaMIjULRf=running
[[10:34:37]] [INFO] Executing action 495/643: Tap on Text: "List"
[[10:34:37]] [SUCCESS] Screenshot refreshed successfully
[[10:34:37]] [SUCCESS] Screenshot refreshed successfully
[[10:34:36]] [SUCCESS] Screenshot refreshed
[[10:34:36]] [INFO] Refreshing screenshot...
[[10:34:36]] [INFO] igReeDqips=pass
[[10:34:32]] [INFO] igReeDqips=running
[[10:34:32]] [INFO] Executing action 494/643: Tap on image: env[catalogue-menu-img]
[[10:34:32]] [SUCCESS] Screenshot refreshed successfully
[[10:34:32]] [SUCCESS] Screenshot refreshed successfully
[[10:34:31]] [SUCCESS] Screenshot refreshed
[[10:34:31]] [INFO] Refreshing screenshot...
[[10:34:31]] [INFO] gcSsGpqKwk=pass
[[10:34:08]] [INFO] gcSsGpqKwk=running
[[10:34:08]] [INFO] Executing action 493/643: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[10:34:08]] [SUCCESS] Screenshot refreshed successfully
[[10:34:08]] [SUCCESS] Screenshot refreshed successfully
[[10:34:08]] [SUCCESS] Screenshot refreshed
[[10:34:08]] [INFO] Refreshing screenshot...
[[10:34:08]] [INFO] gkkQzTCmma=pass
[[10:34:04]] [INFO] gkkQzTCmma=running
[[10:34:04]] [INFO] Executing action 492/643: Tap on Text: "Catalogue"
[[10:34:03]] [SUCCESS] Screenshot refreshed successfully
[[10:34:03]] [SUCCESS] Screenshot refreshed successfully
[[10:34:03]] [SUCCESS] Screenshot refreshed
[[10:34:03]] [INFO] Refreshing screenshot...
[[10:34:03]] [INFO] VpOhIxEl53=pass
[[10:33:51]] [INFO] VpOhIxEl53=running
[[10:33:51]] [INFO] Executing action 491/643: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[10:33:50]] [SUCCESS] Screenshot refreshed successfully
[[10:33:50]] [SUCCESS] Screenshot refreshed successfully
[[10:33:50]] [SUCCESS] Screenshot refreshed
[[10:33:50]] [INFO] Refreshing screenshot...
[[10:33:50]] [INFO] UpUSVInizv=pass
[[10:33:46]] [INFO] UpUSVInizv=running
[[10:33:46]] [INFO] Executing action 490/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[10:33:46]] [SUCCESS] Screenshot refreshed successfully
[[10:33:46]] [SUCCESS] Screenshot refreshed successfully
[[10:33:46]] [SUCCESS] Screenshot refreshed
[[10:33:46]] [INFO] Refreshing screenshot...
[[10:33:46]] [INFO] Cmvm82hiAa=pass
[[10:33:39]] [INFO] Cmvm82hiAa=running
[[10:33:39]] [INFO] Executing action 489/643: Tap on element with accessibility_id: Add to bag
[[10:33:39]] [SUCCESS] Screenshot refreshed successfully
[[10:33:39]] [SUCCESS] Screenshot refreshed successfully
[[10:33:39]] [SUCCESS] Screenshot refreshed
[[10:33:39]] [INFO] Refreshing screenshot...
[[10:33:39]] [INFO] ZZPNqTJ65s=pass
[[10:33:34]] [INFO] ZZPNqTJ65s=running
[[10:33:34]] [INFO] Executing action 488/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:33:33]] [SUCCESS] Screenshot refreshed successfully
[[10:33:33]] [SUCCESS] Screenshot refreshed successfully
[[10:33:33]] [SUCCESS] Screenshot refreshed
[[10:33:33]] [INFO] Refreshing screenshot...
[[10:33:33]] [INFO] JcAR0JctQ6=pass
[[10:33:29]] [INFO] JcAR0JctQ6=running
[[10:33:29]] [INFO] Executing action 487/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[10:33:29]] [SUCCESS] Screenshot refreshed successfully
[[10:33:29]] [SUCCESS] Screenshot refreshed successfully
[[10:33:29]] [SUCCESS] Screenshot refreshed
[[10:33:29]] [INFO] Refreshing screenshot...
[[10:33:29]] [INFO] Pd7cReoJM6=pass
[[10:33:24]] [INFO] Pd7cReoJM6=running
[[10:33:24]] [INFO] Executing action 486/643: Tap on Text: "List"
[[10:33:23]] [SUCCESS] Screenshot refreshed successfully
[[10:33:23]] [SUCCESS] Screenshot refreshed successfully
[[10:33:23]] [SUCCESS] Screenshot refreshed
[[10:33:23]] [INFO] Refreshing screenshot...
[[10:33:23]] [INFO] igReeDqips=pass
[[10:33:19]] [INFO] igReeDqips=running
[[10:33:19]] [INFO] Executing action 485/643: Tap on image: env[catalogue-menu-img]
[[10:33:18]] [SUCCESS] Screenshot refreshed successfully
[[10:33:18]] [SUCCESS] Screenshot refreshed successfully
[[10:33:18]] [SUCCESS] Screenshot refreshed
[[10:33:18]] [INFO] Refreshing screenshot...
[[10:33:18]] [INFO] Jh6RTFWeOU=pass
[[10:32:55]] [INFO] Jh6RTFWeOU=running
[[10:32:55]] [INFO] Executing action 484/643: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[10:32:55]] [SUCCESS] Screenshot refreshed successfully
[[10:32:55]] [SUCCESS] Screenshot refreshed successfully
[[10:32:55]] [SUCCESS] Screenshot refreshed
[[10:32:55]] [INFO] Refreshing screenshot...
[[10:32:55]] [INFO] gkkQzTCmma=pass
[[10:32:51]] [INFO] gkkQzTCmma=running
[[10:32:51]] [INFO] Executing action 483/643: Tap on Text: "Catalogue"
[[10:32:50]] [SUCCESS] Screenshot refreshed successfully
[[10:32:50]] [SUCCESS] Screenshot refreshed successfully
[[10:32:50]] [SUCCESS] Screenshot refreshed
[[10:32:50]] [INFO] Refreshing screenshot...
[[10:32:50]] [INFO] QUeGIASAxV=pass
[[10:32:47]] [INFO] QUeGIASAxV=running
[[10:32:47]] [INFO] Executing action 482/643: Swipe from (50%, 50%) to (50%, 30%)
[[10:32:46]] [SUCCESS] Screenshot refreshed successfully
[[10:32:46]] [SUCCESS] Screenshot refreshed successfully
[[10:32:46]] [SUCCESS] Screenshot refreshed
[[10:32:46]] [INFO] Refreshing screenshot...
[[10:32:46]] [INFO] UpUSVInizv=pass
[[10:32:42]] [INFO] UpUSVInizv=running
[[10:32:42]] [INFO] Executing action 481/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[10:32:42]] [SUCCESS] Screenshot refreshed successfully
[[10:32:42]] [SUCCESS] Screenshot refreshed successfully
[[10:32:42]] [SUCCESS] Screenshot refreshed
[[10:32:42]] [INFO] Refreshing screenshot...
[[10:32:42]] [INFO] 0QtNHB5WEK=pass
[[10:32:35]] [INFO] 0QtNHB5WEK=running
[[10:32:35]] [INFO] Executing action 480/643: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[10:32:34]] [SUCCESS] Screenshot refreshed successfully
[[10:32:34]] [SUCCESS] Screenshot refreshed successfully
[[10:32:34]] [SUCCESS] Screenshot refreshed
[[10:32:34]] [INFO] Refreshing screenshot...
[[10:32:34]] [INFO] fTdGMJ3NH3=pass
[[10:32:31]] [INFO] fTdGMJ3NH3=running
[[10:32:31]] [INFO] Executing action 479/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[10:32:31]] [SUCCESS] Screenshot refreshed successfully
[[10:32:31]] [SUCCESS] Screenshot refreshed successfully
[[10:32:31]] [SUCCESS] Screenshot refreshed
[[10:32:31]] [INFO] Refreshing screenshot...
[[10:32:31]] [INFO] rYJcLPh8Aq=pass
[[10:32:27]] [INFO] rYJcLPh8Aq=running
[[10:32:27]] [INFO] Executing action 478/643: iOS Function: text - Text: "kmart au"
[[10:32:26]] [SUCCESS] Screenshot refreshed successfully
[[10:32:26]] [SUCCESS] Screenshot refreshed successfully
[[10:32:26]] [SUCCESS] Screenshot refreshed
[[10:32:26]] [INFO] Refreshing screenshot...
[[10:32:26]] [INFO] 0Q0fm6OTij=pass
[[10:32:22]] [INFO] 0Q0fm6OTij=running
[[10:32:22]] [INFO] Executing action 477/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[10:32:22]] [SUCCESS] Screenshot refreshed successfully
[[10:32:22]] [SUCCESS] Screenshot refreshed successfully
[[10:32:22]] [SUCCESS] Screenshot refreshed
[[10:32:22]] [INFO] Refreshing screenshot...
[[10:32:22]] [INFO] xVuuejtCFA=pass
[[10:32:17]] [INFO] xVuuejtCFA=running
[[10:32:17]] [INFO] Executing action 476/643: Restart app: com.apple.mobilesafari
[[10:32:17]] [SUCCESS] Screenshot refreshed successfully
[[10:32:17]] [SUCCESS] Screenshot refreshed successfully
[[10:32:16]] [SUCCESS] Screenshot refreshed
[[10:32:16]] [INFO] Refreshing screenshot...
[[10:32:16]] [INFO] LcYLwUffqj=pass
[[10:32:12]] [INFO] LcYLwUffqj=running
[[10:32:12]] [INFO] Executing action 475/643: Tap on Text: "out"
[[10:32:12]] [SUCCESS] Screenshot refreshed successfully
[[10:32:12]] [SUCCESS] Screenshot refreshed successfully
[[10:32:12]] [SUCCESS] Screenshot refreshed
[[10:32:12]] [INFO] Refreshing screenshot...
[[10:32:12]] [INFO] ZZPNqTJ65s=pass
[[10:32:08]] [INFO] ZZPNqTJ65s=running
[[10:32:08]] [INFO] Executing action 474/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:32:07]] [SUCCESS] Screenshot refreshed successfully
[[10:32:07]] [SUCCESS] Screenshot refreshed successfully
[[10:32:07]] [SUCCESS] Screenshot refreshed
[[10:32:07]] [INFO] Refreshing screenshot...
[[10:32:07]] [INFO] UpUSVInizv=pass
[[10:32:03]] [INFO] UpUSVInizv=running
[[10:32:03]] [INFO] Executing action 473/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[10:32:03]] [SUCCESS] Screenshot refreshed successfully
[[10:32:03]] [SUCCESS] Screenshot refreshed successfully
[[10:32:03]] [SUCCESS] Screenshot refreshed
[[10:32:03]] [INFO] Refreshing screenshot...
[[10:32:03]] [INFO] hCCEvRtj1A=pass
[[10:31:58]] [INFO] hCCEvRtj1A=running
[[10:31:58]] [INFO] Executing action 472/643: Restart app: env[appid]
[[10:31:58]] [SUCCESS] Screenshot refreshed successfully
[[10:31:58]] [SUCCESS] Screenshot refreshed successfully
[[10:31:58]] [SUCCESS] Screenshot refreshed
[[10:31:58]] [INFO] Refreshing screenshot...
[[10:31:58]] [INFO] V42eHtTRYW=pass
[[10:31:51]] [INFO] V42eHtTRYW=running
[[10:31:51]] [INFO] Executing action 471/643: Wait for 5 ms
[[10:31:51]] [SUCCESS] Screenshot refreshed successfully
[[10:31:51]] [SUCCESS] Screenshot refreshed successfully
[[10:31:51]] [SUCCESS] Screenshot refreshed
[[10:31:51]] [INFO] Refreshing screenshot...
[[10:31:51]] [INFO] GRwHMVK4sA=pass
[[10:31:49]] [INFO] GRwHMVK4sA=running
[[10:31:49]] [INFO] Executing action 470/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[10:31:48]] [SUCCESS] Screenshot refreshed successfully
[[10:31:48]] [SUCCESS] Screenshot refreshed successfully
[[10:31:48]] [SUCCESS] Screenshot refreshed
[[10:31:48]] [INFO] Refreshing screenshot...
[[10:31:48]] [INFO] V42eHtTRYW=pass
[[10:31:42]] [INFO] V42eHtTRYW=running
[[10:31:42]] [INFO] Executing action 469/643: Wait for 5 ms
[[10:31:42]] [SUCCESS] Screenshot refreshed successfully
[[10:31:42]] [SUCCESS] Screenshot refreshed successfully
[[10:31:42]] [SUCCESS] Screenshot refreshed
[[10:31:42]] [INFO] Refreshing screenshot...
[[10:31:42]] [INFO] LfyQctrEJn=pass
[[10:31:40]] [INFO] LfyQctrEJn=running
[[10:31:40]] [INFO] Executing action 468/643: Launch app: com.apple.Preferences
[[10:31:40]] [SUCCESS] Screenshot refreshed successfully
[[10:31:40]] [SUCCESS] Screenshot refreshed successfully
[[10:31:40]] [SUCCESS] Screenshot refreshed
[[10:31:40]] [INFO] Refreshing screenshot...
[[10:31:40]] [INFO] seQcUKjkSU=pass
[[10:31:38]] [INFO] seQcUKjkSU=running
[[10:31:38]] [INFO] Executing action 467/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:31:38]] [SUCCESS] Screenshot refreshed successfully
[[10:31:38]] [SUCCESS] Screenshot refreshed successfully
[[10:31:38]] [SUCCESS] Screenshot refreshed
[[10:31:38]] [INFO] Refreshing screenshot...
[[10:31:38]] [INFO] UpUSVInizv=pass
[[10:31:36]] [INFO] UpUSVInizv=running
[[10:31:36]] [INFO] Executing action 466/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[10:31:35]] [SUCCESS] Screenshot refreshed successfully
[[10:31:35]] [SUCCESS] Screenshot refreshed successfully
[[10:31:35]] [SUCCESS] Screenshot refreshed
[[10:31:35]] [INFO] Refreshing screenshot...
[[10:31:35]] [INFO] WoymrHdtrO=pass
[[10:31:34]] [INFO] WoymrHdtrO=running
[[10:31:34]] [INFO] Executing action 465/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:31:33]] [SUCCESS] Screenshot refreshed successfully
[[10:31:33]] [SUCCESS] Screenshot refreshed successfully
[[10:31:33]] [SUCCESS] Screenshot refreshed
[[10:31:33]] [INFO] Refreshing screenshot...
[[10:31:33]] [INFO] 6xgrAWyfZ4=pass
[[10:31:31]] [INFO] 6xgrAWyfZ4=running
[[10:31:31]] [INFO] Executing action 464/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[10:31:31]] [SUCCESS] Screenshot refreshed successfully
[[10:31:31]] [SUCCESS] Screenshot refreshed successfully
[[10:31:31]] [SUCCESS] Screenshot refreshed
[[10:31:31]] [INFO] Refreshing screenshot...
[[10:31:31]] [INFO] eSr9EFlJek=pass
[[10:31:29]] [INFO] eSr9EFlJek=running
[[10:31:29]] [INFO] Executing action 463/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:31:29]] [SUCCESS] Screenshot refreshed successfully
[[10:31:29]] [SUCCESS] Screenshot refreshed successfully
[[10:31:29]] [SUCCESS] Screenshot refreshed
[[10:31:29]] [INFO] Refreshing screenshot...
[[10:31:29]] [INFO] 3KNqlNy6Bj=pass
[[10:31:27]] [INFO] 3KNqlNy6Bj=running
[[10:31:27]] [INFO] Executing action 462/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[10:31:26]] [SUCCESS] Screenshot refreshed successfully
[[10:31:26]] [SUCCESS] Screenshot refreshed successfully
[[10:31:26]] [SUCCESS] Screenshot refreshed
[[10:31:26]] [INFO] Refreshing screenshot...
[[10:31:26]] [INFO] cokvFXhj4c=pass
[[10:31:24]] [INFO] cokvFXhj4c=running
[[10:31:24]] [INFO] Executing action 461/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:31:24]] [SUCCESS] Screenshot refreshed successfully
[[10:31:24]] [SUCCESS] Screenshot refreshed successfully
[[10:31:24]] [SUCCESS] Screenshot refreshed
[[10:31:24]] [INFO] Refreshing screenshot...
[[10:31:24]] [INFO] oSQ8sPdVOJ=pass
[[10:31:19]] [INFO] oSQ8sPdVOJ=running
[[10:31:19]] [INFO] Executing action 460/643: Restart app: env[appid]
[[10:31:19]] [SUCCESS] Screenshot refreshed successfully
[[10:31:19]] [SUCCESS] Screenshot refreshed successfully
[[10:31:19]] [SUCCESS] Screenshot refreshed
[[10:31:19]] [INFO] Refreshing screenshot...
[[10:31:19]] [INFO] V42eHtTRYW=pass
[[10:31:13]] [INFO] V42eHtTRYW=running
[[10:31:13]] [INFO] Executing action 459/643: Wait for 5 ms
[[10:31:12]] [SUCCESS] Screenshot refreshed successfully
[[10:31:12]] [SUCCESS] Screenshot refreshed successfully
[[10:31:12]] [SUCCESS] Screenshot refreshed
[[10:31:12]] [INFO] Refreshing screenshot...
[[10:31:12]] [INFO] jUCAk6GJc4=pass
[[10:31:10]] [INFO] jUCAk6GJc4=running
[[10:31:10]] [INFO] Executing action 458/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[10:31:09]] [SUCCESS] Screenshot refreshed successfully
[[10:31:09]] [SUCCESS] Screenshot refreshed successfully
[[10:31:09]] [SUCCESS] Screenshot refreshed
[[10:31:09]] [INFO] Refreshing screenshot...
[[10:31:09]] [INFO] V42eHtTRYW=pass
[[10:31:03]] [INFO] V42eHtTRYW=running
[[10:31:03]] [INFO] Executing action 457/643: Wait for 5 ms
[[10:31:03]] [SUCCESS] Screenshot refreshed successfully
[[10:31:03]] [SUCCESS] Screenshot refreshed successfully
[[10:31:02]] [SUCCESS] Screenshot refreshed
[[10:31:02]] [INFO] Refreshing screenshot...
[[10:31:02]] [INFO] w1RV76df9x=pass
[[10:30:58]] [INFO] w1RV76df9x=running
[[10:30:58]] [INFO] Executing action 456/643: Tap on Text: "Wi-Fi"
[[10:30:58]] [SUCCESS] Screenshot refreshed successfully
[[10:30:58]] [SUCCESS] Screenshot refreshed successfully
[[10:30:58]] [SUCCESS] Screenshot refreshed
[[10:30:58]] [INFO] Refreshing screenshot...
[[10:30:58]] [INFO] LfyQctrEJn=pass
[[10:30:55]] [INFO] LfyQctrEJn=running
[[10:30:55]] [INFO] Executing action 455/643: Launch app: com.apple.Preferences
[[10:30:55]] [SUCCESS] Screenshot refreshed successfully
[[10:30:55]] [SUCCESS] Screenshot refreshed successfully
[[10:30:55]] [SUCCESS] Screenshot refreshed
[[10:30:55]] [INFO] Refreshing screenshot...
[[10:30:55]] [INFO] mIKA85kXaW=pass
[[10:30:52]] [SUCCESS] Screenshot refreshed successfully
[[10:30:52]] [SUCCESS] Screenshot refreshed successfully
[[10:30:52]] [INFO] mIKA85kXaW=running
[[10:30:52]] [INFO] Executing action 454/643: Terminate app: com.apple.Preferences
[[10:30:52]] [SUCCESS] Screenshot refreshed
[[10:30:52]] [INFO] Refreshing screenshot...
[[10:30:52]] [SUCCESS] Screenshot refreshed successfully
[[10:30:52]] [SUCCESS] Screenshot refreshed successfully
[[10:30:52]] [SUCCESS] Screenshot refreshed
[[10:30:52]] [INFO] Refreshing screenshot...
[[10:30:47]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:30:47]] [SUCCESS] Screenshot refreshed successfully
[[10:30:47]] [SUCCESS] Screenshot refreshed successfully
[[10:30:47]] [SUCCESS] Screenshot refreshed
[[10:30:47]] [INFO] Refreshing screenshot...
[[10:30:43]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:30:42]] [SUCCESS] Screenshot refreshed successfully
[[10:30:42]] [SUCCESS] Screenshot refreshed successfully
[[10:30:42]] [SUCCESS] Screenshot refreshed
[[10:30:42]] [INFO] Refreshing screenshot...
[[10:30:38]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:30:38]] [SUCCESS] Screenshot refreshed successfully
[[10:30:38]] [SUCCESS] Screenshot refreshed successfully
[[10:30:37]] [SUCCESS] Screenshot refreshed
[[10:30:37]] [INFO] Refreshing screenshot...
[[10:30:33]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:30:33]] [SUCCESS] Screenshot refreshed successfully
[[10:30:33]] [SUCCESS] Screenshot refreshed successfully
[[10:30:33]] [SUCCESS] Screenshot refreshed
[[10:30:33]] [INFO] Refreshing screenshot...
[[10:30:28]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:30:27]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[10:30:27]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[10:30:27]] [INFO] gx05zu87DK=running
[[10:30:27]] [INFO] Executing action 453/643: Execute Test Case: Kmart-Signin (5 steps)
[[10:30:27]] [SUCCESS] Screenshot refreshed successfully
[[10:30:27]] [SUCCESS] Screenshot refreshed successfully
[[10:30:27]] [SUCCESS] Screenshot refreshed
[[10:30:27]] [INFO] Refreshing screenshot...
[[10:30:27]] [INFO] rJ86z4njuR=pass
[[10:30:24]] [INFO] rJ86z4njuR=running
[[10:30:24]] [INFO] Executing action 452/643: iOS Function: alert_accept
[[10:30:24]] [SUCCESS] Screenshot refreshed successfully
[[10:30:24]] [SUCCESS] Screenshot refreshed successfully
[[10:30:24]] [SUCCESS] Screenshot refreshed
[[10:30:24]] [INFO] Refreshing screenshot...
[[10:30:24]] [INFO] veukWo4573=pass
[[10:30:19]] [INFO] veukWo4573=running
[[10:30:19]] [INFO] Executing action 451/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[10:30:19]] [SUCCESS] Screenshot refreshed successfully
[[10:30:19]] [SUCCESS] Screenshot refreshed successfully
[[10:30:19]] [SUCCESS] Screenshot refreshed
[[10:30:19]] [INFO] Refreshing screenshot...
[[10:30:19]] [INFO] XEbZHdi0GT=pass
[[10:30:14]] [INFO] XEbZHdi0GT=running
[[10:30:14]] [INFO] Executing action 450/643: Restart app: env[appid]
[[10:30:12]] [INFO] === RETRYING TEST CASE: App_Settings_AU_20250609145542.json (Attempt 2 of 3) ===
[[10:30:12]] [INFO] 0QtNHB5WEK=fail
[[10:30:12]] [ERROR] Action 480 failed: Element not found: xpath='//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]'
[[10:30:00]] [INFO] 0QtNHB5WEK=running
[[10:30:00]] [INFO] Executing action 480/643: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[10:29:59]] [SUCCESS] Screenshot refreshed successfully
[[10:29:59]] [SUCCESS] Screenshot refreshed successfully
[[10:29:59]] [SUCCESS] Screenshot refreshed
[[10:29:59]] [INFO] Refreshing screenshot...
[[10:29:59]] [INFO] fTdGMJ3NH3=pass
[[10:29:56]] [INFO] fTdGMJ3NH3=running
[[10:29:56]] [INFO] Executing action 479/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[10:29:56]] [SUCCESS] Screenshot refreshed successfully
[[10:29:56]] [SUCCESS] Screenshot refreshed successfully
[[10:29:56]] [SUCCESS] Screenshot refreshed
[[10:29:56]] [INFO] Refreshing screenshot...
[[10:29:56]] [INFO] rYJcLPh8Aq=pass
[[10:29:52]] [INFO] rYJcLPh8Aq=running
[[10:29:52]] [INFO] Executing action 478/643: iOS Function: text - Text: "kmart au"
[[10:29:52]] [SUCCESS] Screenshot refreshed successfully
[[10:29:52]] [SUCCESS] Screenshot refreshed successfully
[[10:29:52]] [SUCCESS] Screenshot refreshed
[[10:29:52]] [INFO] Refreshing screenshot...
[[10:29:52]] [INFO] 0Q0fm6OTij=pass
[[10:29:48]] [INFO] 0Q0fm6OTij=running
[[10:29:48]] [INFO] Executing action 477/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[10:29:48]] [SUCCESS] Screenshot refreshed successfully
[[10:29:48]] [SUCCESS] Screenshot refreshed successfully
[[10:29:48]] [SUCCESS] Screenshot refreshed
[[10:29:48]] [INFO] Refreshing screenshot...
[[10:29:48]] [INFO] xVuuejtCFA=pass
[[10:29:44]] [INFO] xVuuejtCFA=running
[[10:29:44]] [INFO] Executing action 476/643: Restart app: com.apple.mobilesafari
[[10:29:44]] [SUCCESS] Screenshot refreshed successfully
[[10:29:44]] [SUCCESS] Screenshot refreshed successfully
[[10:29:44]] [SUCCESS] Screenshot refreshed
[[10:29:44]] [INFO] Refreshing screenshot...
[[10:29:44]] [INFO] LcYLwUffqj=pass
[[10:29:40]] [INFO] LcYLwUffqj=running
[[10:29:40]] [INFO] Executing action 475/643: Tap on Text: "out"
[[10:29:39]] [SUCCESS] Screenshot refreshed successfully
[[10:29:39]] [SUCCESS] Screenshot refreshed successfully
[[10:29:39]] [SUCCESS] Screenshot refreshed
[[10:29:39]] [INFO] Refreshing screenshot...
[[10:29:39]] [INFO] ZZPNqTJ65s=pass
[[10:29:35]] [INFO] ZZPNqTJ65s=running
[[10:29:35]] [INFO] Executing action 474/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:29:35]] [SUCCESS] Screenshot refreshed successfully
[[10:29:35]] [SUCCESS] Screenshot refreshed successfully
[[10:29:34]] [SUCCESS] Screenshot refreshed
[[10:29:34]] [INFO] Refreshing screenshot...
[[10:29:34]] [INFO] UpUSVInizv=pass
[[10:29:31]] [INFO] UpUSVInizv=running
[[10:29:31]] [INFO] Executing action 473/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[10:29:31]] [SUCCESS] Screenshot refreshed successfully
[[10:29:31]] [SUCCESS] Screenshot refreshed successfully
[[10:29:30]] [SUCCESS] Screenshot refreshed
[[10:29:30]] [INFO] Refreshing screenshot...
[[10:29:30]] [INFO] hCCEvRtj1A=pass
[[10:29:25]] [INFO] hCCEvRtj1A=running
[[10:29:25]] [INFO] Executing action 472/643: Restart app: env[appid]
[[10:29:25]] [SUCCESS] Screenshot refreshed successfully
[[10:29:25]] [SUCCESS] Screenshot refreshed successfully
[[10:29:25]] [SUCCESS] Screenshot refreshed
[[10:29:25]] [INFO] Refreshing screenshot...
[[10:29:25]] [INFO] V42eHtTRYW=pass
[[10:29:19]] [INFO] V42eHtTRYW=running
[[10:29:19]] [INFO] Executing action 471/643: Wait for 5 ms
[[10:29:18]] [SUCCESS] Screenshot refreshed successfully
[[10:29:18]] [SUCCESS] Screenshot refreshed successfully
[[10:29:18]] [SUCCESS] Screenshot refreshed
[[10:29:18]] [INFO] Refreshing screenshot...
[[10:29:18]] [INFO] GRwHMVK4sA=pass
[[10:29:16]] [INFO] GRwHMVK4sA=running
[[10:29:16]] [INFO] Executing action 470/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[10:29:16]] [SUCCESS] Screenshot refreshed successfully
[[10:29:16]] [SUCCESS] Screenshot refreshed successfully
[[10:29:16]] [SUCCESS] Screenshot refreshed
[[10:29:16]] [INFO] Refreshing screenshot...
[[10:29:16]] [INFO] V42eHtTRYW=pass
[[10:29:09]] [INFO] V42eHtTRYW=running
[[10:29:09]] [INFO] Executing action 469/643: Wait for 5 ms
[[10:29:09]] [SUCCESS] Screenshot refreshed successfully
[[10:29:09]] [SUCCESS] Screenshot refreshed successfully
[[10:29:09]] [SUCCESS] Screenshot refreshed
[[10:29:09]] [INFO] Refreshing screenshot...
[[10:29:09]] [INFO] LfyQctrEJn=pass
[[10:29:07]] [INFO] LfyQctrEJn=running
[[10:29:07]] [INFO] Executing action 468/643: Launch app: com.apple.Preferences
[[10:29:07]] [SUCCESS] Screenshot refreshed successfully
[[10:29:07]] [SUCCESS] Screenshot refreshed successfully
[[10:29:07]] [SUCCESS] Screenshot refreshed
[[10:29:07]] [INFO] Refreshing screenshot...
[[10:29:07]] [INFO] seQcUKjkSU=pass
[[10:29:05]] [INFO] seQcUKjkSU=running
[[10:29:05]] [INFO] Executing action 467/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:29:05]] [SUCCESS] Screenshot refreshed successfully
[[10:29:05]] [SUCCESS] Screenshot refreshed successfully
[[10:29:05]] [SUCCESS] Screenshot refreshed
[[10:29:05]] [INFO] Refreshing screenshot...
[[10:29:05]] [INFO] UpUSVInizv=pass
[[10:29:03]] [INFO] UpUSVInizv=running
[[10:29:03]] [INFO] Executing action 466/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[10:29:02]] [SUCCESS] Screenshot refreshed successfully
[[10:29:02]] [SUCCESS] Screenshot refreshed successfully
[[10:29:02]] [SUCCESS] Screenshot refreshed
[[10:29:02]] [INFO] Refreshing screenshot...
[[10:29:02]] [INFO] WoymrHdtrO=pass
[[10:29:01]] [INFO] WoymrHdtrO=running
[[10:29:01]] [INFO] Executing action 465/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:29:00]] [SUCCESS] Screenshot refreshed successfully
[[10:29:00]] [SUCCESS] Screenshot refreshed successfully
[[10:29:00]] [SUCCESS] Screenshot refreshed
[[10:29:00]] [INFO] Refreshing screenshot...
[[10:29:00]] [INFO] 6xgrAWyfZ4=pass
[[10:28:58]] [INFO] 6xgrAWyfZ4=running
[[10:28:58]] [INFO] Executing action 464/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[10:28:58]] [SUCCESS] Screenshot refreshed successfully
[[10:28:58]] [SUCCESS] Screenshot refreshed successfully
[[10:28:58]] [SUCCESS] Screenshot refreshed
[[10:28:58]] [INFO] Refreshing screenshot...
[[10:28:58]] [INFO] eSr9EFlJek=pass
[[10:28:56]] [INFO] eSr9EFlJek=running
[[10:28:56]] [INFO] Executing action 463/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:28:56]] [SUCCESS] Screenshot refreshed successfully
[[10:28:56]] [SUCCESS] Screenshot refreshed successfully
[[10:28:56]] [SUCCESS] Screenshot refreshed
[[10:28:56]] [INFO] Refreshing screenshot...
[[10:28:56]] [INFO] 3KNqlNy6Bj=pass
[[10:28:54]] [INFO] 3KNqlNy6Bj=running
[[10:28:54]] [INFO] Executing action 462/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[10:28:53]] [SUCCESS] Screenshot refreshed successfully
[[10:28:53]] [SUCCESS] Screenshot refreshed successfully
[[10:28:53]] [SUCCESS] Screenshot refreshed
[[10:28:53]] [INFO] Refreshing screenshot...
[[10:28:53]] [INFO] cokvFXhj4c=pass
[[10:28:52]] [INFO] cokvFXhj4c=running
[[10:28:52]] [INFO] Executing action 461/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[10:28:51]] [SUCCESS] Screenshot refreshed successfully
[[10:28:51]] [SUCCESS] Screenshot refreshed successfully
[[10:28:51]] [SUCCESS] Screenshot refreshed
[[10:28:51]] [INFO] Refreshing screenshot...
[[10:28:51]] [INFO] oSQ8sPdVOJ=pass
[[10:28:47]] [INFO] oSQ8sPdVOJ=running
[[10:28:47]] [INFO] Executing action 460/643: Restart app: env[appid]
[[10:28:46]] [SUCCESS] Screenshot refreshed successfully
[[10:28:46]] [SUCCESS] Screenshot refreshed successfully
[[10:28:46]] [SUCCESS] Screenshot refreshed
[[10:28:46]] [INFO] Refreshing screenshot...
[[10:28:46]] [INFO] V42eHtTRYW=pass
[[10:28:40]] [INFO] V42eHtTRYW=running
[[10:28:40]] [INFO] Executing action 459/643: Wait for 5 ms
[[10:28:39]] [SUCCESS] Screenshot refreshed successfully
[[10:28:39]] [SUCCESS] Screenshot refreshed successfully
[[10:28:39]] [SUCCESS] Screenshot refreshed
[[10:28:39]] [INFO] Refreshing screenshot...
[[10:28:39]] [INFO] jUCAk6GJc4=pass
[[10:28:37]] [INFO] jUCAk6GJc4=running
[[10:28:37]] [INFO] Executing action 458/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[10:28:37]] [SUCCESS] Screenshot refreshed successfully
[[10:28:37]] [SUCCESS] Screenshot refreshed successfully
[[10:28:37]] [SUCCESS] Screenshot refreshed
[[10:28:37]] [INFO] Refreshing screenshot...
[[10:28:37]] [INFO] V42eHtTRYW=pass
[[10:28:30]] [INFO] V42eHtTRYW=running
[[10:28:30]] [INFO] Executing action 457/643: Wait for 5 ms
[[10:28:30]] [SUCCESS] Screenshot refreshed successfully
[[10:28:30]] [SUCCESS] Screenshot refreshed successfully
[[10:28:30]] [SUCCESS] Screenshot refreshed
[[10:28:30]] [INFO] Refreshing screenshot...
[[10:28:30]] [INFO] w1RV76df9x=pass
[[10:28:26]] [INFO] w1RV76df9x=running
[[10:28:26]] [INFO] Executing action 456/643: Tap on Text: "Wi-Fi"
[[10:28:26]] [SUCCESS] Screenshot refreshed successfully
[[10:28:26]] [SUCCESS] Screenshot refreshed successfully
[[10:28:26]] [SUCCESS] Screenshot refreshed
[[10:28:26]] [INFO] Refreshing screenshot...
[[10:28:26]] [INFO] LfyQctrEJn=pass
[[10:28:23]] [INFO] LfyQctrEJn=running
[[10:28:23]] [INFO] Executing action 455/643: Launch app: com.apple.Preferences
[[10:28:23]] [SUCCESS] Screenshot refreshed successfully
[[10:28:23]] [SUCCESS] Screenshot refreshed successfully
[[10:28:23]] [SUCCESS] Screenshot refreshed
[[10:28:23]] [INFO] Refreshing screenshot...
[[10:28:23]] [INFO] mIKA85kXaW=pass
[[10:28:22]] [SUCCESS] Screenshot refreshed successfully
[[10:28:22]] [SUCCESS] Screenshot refreshed successfully
[[10:28:21]] [INFO] mIKA85kXaW=running
[[10:28:21]] [INFO] Executing action 454/643: Terminate app: com.apple.Preferences
[[10:28:21]] [SUCCESS] Screenshot refreshed
[[10:28:21]] [INFO] Refreshing screenshot...
[[10:28:21]] [SUCCESS] Screenshot refreshed successfully
[[10:28:21]] [SUCCESS] Screenshot refreshed successfully
[[10:28:21]] [SUCCESS] Screenshot refreshed
[[10:28:21]] [INFO] Refreshing screenshot...
[[10:28:16]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:28:16]] [SUCCESS] Screenshot refreshed successfully
[[10:28:16]] [SUCCESS] Screenshot refreshed successfully
[[10:28:16]] [SUCCESS] Screenshot refreshed
[[10:28:16]] [INFO] Refreshing screenshot...
[[10:28:12]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:28:12]] [SUCCESS] Screenshot refreshed successfully
[[10:28:12]] [SUCCESS] Screenshot refreshed successfully
[[10:28:11]] [SUCCESS] Screenshot refreshed
[[10:28:11]] [INFO] Refreshing screenshot...
[[10:28:07]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:28:07]] [SUCCESS] Screenshot refreshed successfully
[[10:28:07]] [SUCCESS] Screenshot refreshed successfully
[[10:28:06]] [SUCCESS] Screenshot refreshed
[[10:28:06]] [INFO] Refreshing screenshot...
[[10:28:02]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:28:02]] [SUCCESS] Screenshot refreshed successfully
[[10:28:02]] [SUCCESS] Screenshot refreshed successfully
[[10:28:02]] [SUCCESS] Screenshot refreshed
[[10:28:02]] [INFO] Refreshing screenshot...
[[10:27:57]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:27:57]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[10:27:56]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[10:27:56]] [INFO] gx05zu87DK=running
[[10:27:56]] [INFO] Executing action 453/643: Execute Test Case: Kmart-Signin (5 steps)
[[10:27:56]] [SUCCESS] Screenshot refreshed successfully
[[10:27:56]] [SUCCESS] Screenshot refreshed successfully
[[10:27:56]] [SUCCESS] Screenshot refreshed
[[10:27:56]] [INFO] Refreshing screenshot...
[[10:27:56]] [INFO] rJ86z4njuR=pass
[[10:27:53]] [INFO] rJ86z4njuR=running
[[10:27:53]] [INFO] Executing action 452/643: iOS Function: alert_accept
[[10:27:53]] [SUCCESS] Screenshot refreshed successfully
[[10:27:53]] [SUCCESS] Screenshot refreshed successfully
[[10:27:53]] [SUCCESS] Screenshot refreshed
[[10:27:53]] [INFO] Refreshing screenshot...
[[10:27:53]] [INFO] veukWo4573=pass
[[10:27:49]] [INFO] veukWo4573=running
[[10:27:49]] [INFO] Executing action 451/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[10:27:49]] [SUCCESS] Screenshot refreshed successfully
[[10:27:49]] [SUCCESS] Screenshot refreshed successfully
[[10:27:49]] [SUCCESS] Screenshot refreshed
[[10:27:49]] [INFO] Refreshing screenshot...
[[10:27:49]] [INFO] XEbZHdi0GT=pass
[[10:27:35]] [SUCCESS] Screenshot refreshed successfully
[[10:27:35]] [SUCCESS] Screenshot refreshed successfully
[[10:27:35]] [INFO] XEbZHdi0GT=running
[[10:27:35]] [INFO] Executing action 450/643: Restart app: env[appid]
[[10:27:35]] [SUCCESS] Screenshot refreshed
[[10:27:35]] [INFO] Refreshing screenshot...
[[10:27:35]] [SUCCESS] Screenshot refreshed successfully
[[10:27:35]] [SUCCESS] Screenshot refreshed successfully
[[10:27:35]] [SUCCESS] Screenshot refreshed
[[10:27:35]] [INFO] Refreshing screenshot...
[[10:27:32]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:27:32]] [SUCCESS] Screenshot refreshed successfully
[[10:27:32]] [SUCCESS] Screenshot refreshed successfully
[[10:27:32]] [SUCCESS] Screenshot refreshed
[[10:27:32]] [INFO] Refreshing screenshot...
[[10:27:19]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:27:19]] [SUCCESS] Screenshot refreshed successfully
[[10:27:19]] [SUCCESS] Screenshot refreshed successfully
[[10:27:19]] [SUCCESS] Screenshot refreshed
[[10:27:19]] [INFO] Refreshing screenshot...
[[10:27:15]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:27:15]] [SUCCESS] Screenshot refreshed successfully
[[10:27:15]] [SUCCESS] Screenshot refreshed successfully
[[10:27:15]] [SUCCESS] Screenshot refreshed
[[10:27:15]] [INFO] Refreshing screenshot...
[[10:27:11]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:27:11]] [SUCCESS] Screenshot refreshed successfully
[[10:27:11]] [SUCCESS] Screenshot refreshed successfully
[[10:27:10]] [SUCCESS] Screenshot refreshed
[[10:27:10]] [INFO] Refreshing screenshot...
[[10:27:04]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:27:04]] [SUCCESS] Screenshot refreshed successfully
[[10:27:04]] [SUCCESS] Screenshot refreshed successfully
[[10:27:04]] [SUCCESS] Screenshot refreshed
[[10:27:04]] [INFO] Refreshing screenshot...
[[10:26:57]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:26:57]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:26:57]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:26:57]] [INFO] ubySifeF65=running
[[10:26:57]] [INFO] Executing action 449/643: cleanupSteps action
[[10:26:57]] [SUCCESS] Screenshot refreshed successfully
[[10:26:57]] [SUCCESS] Screenshot refreshed successfully
[[10:26:56]] [SUCCESS] Screenshot refreshed
[[10:26:56]] [INFO] Refreshing screenshot...
[[10:26:56]] [INFO] xyHVihJMBi=pass
[[10:26:52]] [INFO] xyHVihJMBi=running
[[10:26:52]] [INFO] Executing action 448/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:26:52]] [SUCCESS] Screenshot refreshed successfully
[[10:26:52]] [SUCCESS] Screenshot refreshed successfully
[[10:26:52]] [SUCCESS] Screenshot refreshed
[[10:26:52]] [INFO] Refreshing screenshot...
[[10:26:52]] [INFO] mWeLQtXiL6=pass
[[10:26:45]] [INFO] mWeLQtXiL6=running
[[10:26:45]] [INFO] Executing action 447/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:26:45]] [SUCCESS] Screenshot refreshed successfully
[[10:26:45]] [SUCCESS] Screenshot refreshed successfully
[[10:26:45]] [SUCCESS] Screenshot refreshed
[[10:26:45]] [INFO] Refreshing screenshot...
[[10:26:45]] [INFO] F4NGh9HrLw=pass
[[10:26:40]] [INFO] F4NGh9HrLw=running
[[10:26:40]] [INFO] Executing action 446/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:26:40]] [SUCCESS] Screenshot refreshed successfully
[[10:26:40]] [SUCCESS] Screenshot refreshed successfully
[[10:26:40]] [SUCCESS] Screenshot refreshed
[[10:26:40]] [INFO] Refreshing screenshot...
[[10:26:40]] [INFO] 0f2FSZYjWq=pass
[[10:26:25]] [INFO] 0f2FSZYjWq=running
[[10:26:25]] [INFO] Executing action 445/643: Check if element with text="Melbourne" exists
[[10:26:24]] [SUCCESS] Screenshot refreshed successfully
[[10:26:24]] [SUCCESS] Screenshot refreshed successfully
[[10:26:24]] [SUCCESS] Screenshot refreshed
[[10:26:24]] [INFO] Refreshing screenshot...
[[10:26:24]] [INFO] Tebej51pT2=pass
[[10:26:20]] [INFO] Tebej51pT2=running
[[10:26:20]] [INFO] Executing action 444/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[10:26:20]] [SUCCESS] Screenshot refreshed successfully
[[10:26:20]] [SUCCESS] Screenshot refreshed successfully
[[10:26:20]] [SUCCESS] Screenshot refreshed
[[10:26:20]] [INFO] Refreshing screenshot...
[[10:26:20]] [INFO] I4gwigwXSj=pass
[[10:26:17]] [INFO] I4gwigwXSj=running
[[10:26:17]] [INFO] Executing action 443/643: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[10:26:16]] [SUCCESS] Screenshot refreshed successfully
[[10:26:16]] [SUCCESS] Screenshot refreshed successfully
[[10:26:16]] [SUCCESS] Screenshot refreshed
[[10:26:16]] [INFO] Refreshing screenshot...
[[10:26:16]] [INFO] eVytJrry9x=pass
[[10:26:13]] [INFO] eVytJrry9x=running
[[10:26:13]] [INFO] Executing action 442/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[10:26:12]] [SUCCESS] Screenshot refreshed successfully
[[10:26:12]] [SUCCESS] Screenshot refreshed successfully
[[10:26:12]] [SUCCESS] Screenshot refreshed
[[10:26:12]] [INFO] Refreshing screenshot...
[[10:26:12]] [INFO] s8h8VDUIOC=pass
[[10:26:08]] [INFO] s8h8VDUIOC=running
[[10:26:08]] [INFO] Executing action 441/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:26:08]] [SUCCESS] Screenshot refreshed successfully
[[10:26:08]] [SUCCESS] Screenshot refreshed successfully
[[10:26:07]] [SUCCESS] Screenshot refreshed
[[10:26:07]] [INFO] Refreshing screenshot...
[[10:26:07]] [INFO] bkU728TrRF=pass
[[10:26:01]] [INFO] bkU728TrRF=running
[[10:26:01]] [INFO] Executing action 440/643: Tap on element with accessibility_id: Done
[[10:26:01]] [SUCCESS] Screenshot refreshed successfully
[[10:26:01]] [SUCCESS] Screenshot refreshed successfully
[[10:26:01]] [SUCCESS] Screenshot refreshed
[[10:26:01]] [INFO] Refreshing screenshot...
[[10:26:01]] [INFO] ZWpYNcpbFA=pass
[[10:25:56]] [INFO] ZWpYNcpbFA=running
[[10:25:56]] [INFO] Executing action 439/643: Tap on Text: "VIC"
[[10:25:56]] [SUCCESS] Screenshot refreshed successfully
[[10:25:56]] [SUCCESS] Screenshot refreshed successfully
[[10:25:56]] [SUCCESS] Screenshot refreshed
[[10:25:56]] [INFO] Refreshing screenshot...
[[10:25:56]] [INFO] Wld5Urg70o=pass
[[10:25:49]] [INFO] Wld5Urg70o=running
[[10:25:49]] [INFO] Executing action 438/643: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[10:25:49]] [SUCCESS] Screenshot refreshed successfully
[[10:25:49]] [SUCCESS] Screenshot refreshed successfully
[[10:25:49]] [SUCCESS] Screenshot refreshed
[[10:25:49]] [INFO] Refreshing screenshot...
[[10:25:49]] [INFO] QpBLC6BStn=pass
[[10:25:43]] [INFO] QpBLC6BStn=running
[[10:25:43]] [INFO] Executing action 437/643: Tap on element with accessibility_id: delete
[[10:25:42]] [SUCCESS] Screenshot refreshed successfully
[[10:25:42]] [SUCCESS] Screenshot refreshed successfully
[[10:25:42]] [SUCCESS] Screenshot refreshed
[[10:25:42]] [INFO] Refreshing screenshot...
[[10:25:42]] [INFO] G4A3KBlXHq=pass
[[10:25:38]] [INFO] G4A3KBlXHq=running
[[10:25:38]] [INFO] Executing action 436/643: Tap on Text: "Nearby"
[[10:25:37]] [SUCCESS] Screenshot refreshed successfully
[[10:25:37]] [SUCCESS] Screenshot refreshed successfully
[[10:25:37]] [SUCCESS] Screenshot refreshed
[[10:25:37]] [INFO] Refreshing screenshot...
[[10:25:37]] [INFO] uArzgeZYf7=pass
[[10:25:34]] [INFO] uArzgeZYf7=running
[[10:25:34]] [INFO] Executing action 435/643: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[10:25:34]] [SUCCESS] Screenshot refreshed successfully
[[10:25:34]] [SUCCESS] Screenshot refreshed successfully
[[10:25:33]] [SUCCESS] Screenshot refreshed
[[10:25:33]] [INFO] Refreshing screenshot...
[[10:25:33]] [INFO] 3gJsiap2Ds=pass
[[10:25:30]] [INFO] 3gJsiap2Ds=running
[[10:25:30]] [INFO] Executing action 434/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[10:25:29]] [SUCCESS] Screenshot refreshed successfully
[[10:25:29]] [SUCCESS] Screenshot refreshed successfully
[[10:25:29]] [SUCCESS] Screenshot refreshed
[[10:25:29]] [INFO] Refreshing screenshot...
[[10:25:29]] [INFO] EReijW5iNX=pass
[[10:25:17]] [INFO] EReijW5iNX=running
[[10:25:17]] [INFO] Executing action 433/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[10:25:17]] [SUCCESS] Screenshot refreshed successfully
[[10:25:17]] [SUCCESS] Screenshot refreshed successfully
[[10:25:17]] [SUCCESS] Screenshot refreshed
[[10:25:17]] [INFO] Refreshing screenshot...
[[10:25:17]] [INFO] 94ikwhIEE2=pass
[[10:25:12]] [INFO] 94ikwhIEE2=running
[[10:25:12]] [INFO] Executing action 432/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[10:25:12]] [SUCCESS] Screenshot refreshed successfully
[[10:25:12]] [SUCCESS] Screenshot refreshed successfully
[[10:25:12]] [SUCCESS] Screenshot refreshed
[[10:25:12]] [INFO] Refreshing screenshot...
[[10:25:12]] [INFO] q8oldD8uZt=pass
[[10:25:09]] [INFO] q8oldD8uZt=running
[[10:25:09]] [INFO] Executing action 431/643: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[10:25:08]] [SUCCESS] Screenshot refreshed successfully
[[10:25:08]] [SUCCESS] Screenshot refreshed successfully
[[10:25:08]] [SUCCESS] Screenshot refreshed
[[10:25:08]] [INFO] Refreshing screenshot...
[[10:25:08]] [INFO] Jf2wJyOphY=pass
[[10:24:51]] [INFO] Jf2wJyOphY=running
[[10:24:51]] [INFO] Executing action 430/643: Tap on element with accessibility_id: Add to bag
[[10:24:51]] [SUCCESS] Screenshot refreshed successfully
[[10:24:51]] [SUCCESS] Screenshot refreshed successfully
[[10:24:50]] [SUCCESS] Screenshot refreshed
[[10:24:50]] [INFO] Refreshing screenshot...
[[10:24:50]] [INFO] eRCmRhc3re=pass
[[10:24:37]] [INFO] eRCmRhc3re=running
[[10:24:37]] [INFO] Executing action 429/643: Check if element with text="Broadway" exists
[[10:24:37]] [SUCCESS] Screenshot refreshed successfully
[[10:24:37]] [SUCCESS] Screenshot refreshed successfully
[[10:24:37]] [SUCCESS] Screenshot refreshed
[[10:24:37]] [INFO] Refreshing screenshot...
[[10:24:37]] [INFO] ORI6ZFMBK1=pass
[[10:24:33]] [INFO] ORI6ZFMBK1=running
[[10:24:33]] [INFO] Executing action 428/643: Tap on Text: "Save"
[[10:24:32]] [SUCCESS] Screenshot refreshed successfully
[[10:24:32]] [SUCCESS] Screenshot refreshed successfully
[[10:24:32]] [SUCCESS] Screenshot refreshed
[[10:24:32]] [INFO] Refreshing screenshot...
[[10:24:32]] [INFO] hr0IVckpYI=pass
[[10:24:28]] [INFO] hr0IVckpYI=running
[[10:24:28]] [INFO] Executing action 427/643: Wait till accessibility_id=btnSaveOrContinue
[[10:24:27]] [SUCCESS] Screenshot refreshed successfully
[[10:24:27]] [SUCCESS] Screenshot refreshed successfully
[[10:24:27]] [SUCCESS] Screenshot refreshed
[[10:24:27]] [INFO] Refreshing screenshot...
[[10:24:27]] [INFO] H0ODFz7sWJ=pass
[[10:24:23]] [INFO] H0ODFz7sWJ=running
[[10:24:23]] [INFO] Executing action 426/643: Tap on Text: "2000"
[[10:24:23]] [SUCCESS] Screenshot refreshed successfully
[[10:24:23]] [SUCCESS] Screenshot refreshed successfully
[[10:24:22]] [SUCCESS] Screenshot refreshed
[[10:24:22]] [INFO] Refreshing screenshot...
[[10:24:22]] [INFO] uZHvvAzVfx=pass
[[10:24:18]] [INFO] uZHvvAzVfx=running
[[10:24:18]] [INFO] Executing action 425/643: textClear action
[[10:24:17]] [SUCCESS] Screenshot refreshed successfully
[[10:24:17]] [SUCCESS] Screenshot refreshed successfully
[[10:24:17]] [SUCCESS] Screenshot refreshed
[[10:24:17]] [INFO] Refreshing screenshot...
[[10:24:17]] [INFO] WmNWcsWVHv=pass
[[10:24:12]] [INFO] WmNWcsWVHv=running
[[10:24:12]] [INFO] Executing action 424/643: Tap on element with accessibility_id: Search suburb or postcode
[[10:24:11]] [SUCCESS] Screenshot refreshed successfully
[[10:24:11]] [SUCCESS] Screenshot refreshed successfully
[[10:24:11]] [SUCCESS] Screenshot refreshed
[[10:24:11]] [INFO] Refreshing screenshot...
[[10:24:11]] [INFO] lnjoz8hHUU=pass
[[10:24:07]] [INFO] lnjoz8hHUU=running
[[10:24:07]] [INFO] Executing action 423/643: Tap on Text: "Edit"
[[10:24:06]] [SUCCESS] Screenshot refreshed successfully
[[10:24:06]] [SUCCESS] Screenshot refreshed successfully
[[10:24:06]] [SUCCESS] Screenshot refreshed
[[10:24:06]] [INFO] Refreshing screenshot...
[[10:24:06]] [INFO] letbbewlnA=pass
[[10:24:02]] [INFO] letbbewlnA=running
[[10:24:02]] [INFO] Executing action 422/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:24:02]] [SUCCESS] Screenshot refreshed successfully
[[10:24:02]] [SUCCESS] Screenshot refreshed successfully
[[10:24:02]] [SUCCESS] Screenshot refreshed
[[10:24:02]] [INFO] Refreshing screenshot...
[[10:24:02]] [INFO] trBISwJ8eZ=pass
[[10:23:58]] [INFO] trBISwJ8eZ=running
[[10:23:58]] [INFO] Executing action 421/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:23:58]] [SUCCESS] Screenshot refreshed successfully
[[10:23:58]] [SUCCESS] Screenshot refreshed successfully
[[10:23:57]] [SUCCESS] Screenshot refreshed
[[10:23:57]] [INFO] Refreshing screenshot...
[[10:23:57]] [INFO] foVGMl9wvu=pass
[[10:23:54]] [INFO] foVGMl9wvu=running
[[10:23:54]] [INFO] Executing action 420/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:23:54]] [SUCCESS] Screenshot refreshed successfully
[[10:23:54]] [SUCCESS] Screenshot refreshed successfully
[[10:23:53]] [SUCCESS] Screenshot refreshed
[[10:23:53]] [INFO] Refreshing screenshot...
[[10:23:53]] [INFO] 73NABkfWyY=pass
[[10:23:39]] [INFO] 73NABkfWyY=running
[[10:23:39]] [INFO] Executing action 419/643: Check if element with text="Tarneit" exists
[[10:23:39]] [SUCCESS] Screenshot refreshed successfully
[[10:23:39]] [SUCCESS] Screenshot refreshed successfully
[[10:23:38]] [SUCCESS] Screenshot refreshed
[[10:23:38]] [INFO] Refreshing screenshot...
[[10:23:38]] [INFO] pKjXoj4mNg=pass
[[10:23:34]] [INFO] pKjXoj4mNg=running
[[10:23:34]] [INFO] Executing action 418/643: Tap on Text: "Save"
[[10:23:34]] [SUCCESS] Screenshot refreshed successfully
[[10:23:34]] [SUCCESS] Screenshot refreshed successfully
[[10:23:34]] [SUCCESS] Screenshot refreshed
[[10:23:34]] [INFO] Refreshing screenshot...
[[10:23:34]] [INFO] M3dXqigqRv=pass
[[10:23:29]] [INFO] M3dXqigqRv=running
[[10:23:29]] [INFO] Executing action 417/643: Wait till accessibility_id=btnSaveOrContinue
[[10:23:29]] [SUCCESS] Screenshot refreshed successfully
[[10:23:29]] [SUCCESS] Screenshot refreshed successfully
[[10:23:28]] [SUCCESS] Screenshot refreshed
[[10:23:28]] [INFO] Refreshing screenshot...
[[10:23:28]] [INFO] GYRHQr7TWx=pass
[[10:23:24]] [INFO] GYRHQr7TWx=running
[[10:23:24]] [INFO] Executing action 416/643: Tap on Text: "current"
[[10:23:24]] [SUCCESS] Screenshot refreshed successfully
[[10:23:24]] [SUCCESS] Screenshot refreshed successfully
[[10:23:24]] [SUCCESS] Screenshot refreshed
[[10:23:24]] [INFO] Refreshing screenshot...
[[10:23:24]] [INFO] kiM0WyWE9I=pass
[[10:23:19]] [INFO] kiM0WyWE9I=running
[[10:23:19]] [INFO] Executing action 415/643: Wait till accessibility_id=btnCurrentLocationButton
[[10:23:19]] [SUCCESS] Screenshot refreshed successfully
[[10:23:19]] [SUCCESS] Screenshot refreshed successfully
[[10:23:19]] [SUCCESS] Screenshot refreshed
[[10:23:19]] [INFO] Refreshing screenshot...
[[10:23:19]] [INFO] VkUKQbf1Qt=pass
[[10:23:14]] [INFO] VkUKQbf1Qt=running
[[10:23:14]] [INFO] Executing action 414/643: Tap on Text: "Edit"
[[10:23:14]] [SUCCESS] Screenshot refreshed successfully
[[10:23:14]] [SUCCESS] Screenshot refreshed successfully
[[10:23:13]] [SUCCESS] Screenshot refreshed
[[10:23:13]] [INFO] Refreshing screenshot...
[[10:23:13]] [INFO] C6JHhLdWTv=pass
[[10:23:10]] [INFO] C6JHhLdWTv=running
[[10:23:10]] [INFO] Executing action 413/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[10:23:10]] [SUCCESS] Screenshot refreshed successfully
[[10:23:10]] [SUCCESS] Screenshot refreshed successfully
[[10:23:09]] [SUCCESS] Screenshot refreshed
[[10:23:09]] [INFO] Refreshing screenshot...
[[10:23:09]] [INFO] IupxLP2Jsr=pass
[[10:23:06]] [INFO] IupxLP2Jsr=running
[[10:23:06]] [INFO] Executing action 412/643: iOS Function: text - Text: "Uno card"
[[10:23:05]] [SUCCESS] Screenshot refreshed successfully
[[10:23:05]] [SUCCESS] Screenshot refreshed successfully
[[10:23:05]] [SUCCESS] Screenshot refreshed
[[10:23:05]] [INFO] Refreshing screenshot...
[[10:23:05]] [INFO] 70iOOakiG7=pass
[[10:23:00]] [INFO] 70iOOakiG7=running
[[10:23:00]] [INFO] Executing action 411/643: Tap on Text: "Find"
[[10:23:00]] [SUCCESS] Screenshot refreshed successfully
[[10:23:00]] [SUCCESS] Screenshot refreshed successfully
[[10:23:00]] [SUCCESS] Screenshot refreshed
[[10:23:00]] [INFO] Refreshing screenshot...
[[10:23:00]] [INFO] vL26X6PBjc=pass
[[10:22:53]] [INFO] vL26X6PBjc=running
[[10:22:53]] [INFO] Executing action 410/643: Tap if locator exists: accessibility_id="btnUpdate"
[[10:22:53]] [SUCCESS] Screenshot refreshed successfully
[[10:22:53]] [SUCCESS] Screenshot refreshed successfully
[[10:22:52]] [SUCCESS] Screenshot refreshed
[[10:22:52]] [INFO] Refreshing screenshot...
[[10:22:52]] [INFO] E2jpN7BioW=pass
[[10:22:48]] [INFO] E2jpN7BioW=running
[[10:22:48]] [INFO] Executing action 409/643: Tap on Text: "Save"
[[10:22:48]] [SUCCESS] Screenshot refreshed successfully
[[10:22:48]] [SUCCESS] Screenshot refreshed successfully
[[10:22:48]] [SUCCESS] Screenshot refreshed
[[10:22:48]] [INFO] Refreshing screenshot...
[[10:22:48]] [INFO] Sl6eiqZkRm=pass
[[10:22:43]] [INFO] Sl6eiqZkRm=running
[[10:22:43]] [INFO] Executing action 408/643: Wait till accessibility_id=btnSaveOrContinue
[[10:22:43]] [SUCCESS] Screenshot refreshed successfully
[[10:22:43]] [SUCCESS] Screenshot refreshed successfully
[[10:22:43]] [SUCCESS] Screenshot refreshed
[[10:22:43]] [INFO] Refreshing screenshot...
[[10:22:43]] [INFO] mw9GQ4mzRE=pass
[[10:22:38]] [INFO] mw9GQ4mzRE=running
[[10:22:38]] [INFO] Executing action 407/643: Tap on Text: "2000"
[[10:22:38]] [SUCCESS] Screenshot refreshed successfully
[[10:22:38]] [SUCCESS] Screenshot refreshed successfully
[[10:22:38]] [SUCCESS] Screenshot refreshed
[[10:22:38]] [INFO] Refreshing screenshot...
[[10:22:38]] [INFO] kbdEPCPYod=pass
[[10:22:33]] [INFO] kbdEPCPYod=running
[[10:22:33]] [INFO] Executing action 406/643: textClear action
[[10:22:33]] [SUCCESS] Screenshot refreshed successfully
[[10:22:33]] [SUCCESS] Screenshot refreshed successfully
[[10:22:33]] [SUCCESS] Screenshot refreshed
[[10:22:33]] [INFO] Refreshing screenshot...
[[10:22:33]] [INFO] 8WCusTZ8q9=pass
[[10:22:27]] [INFO] 8WCusTZ8q9=running
[[10:22:27]] [INFO] Executing action 405/643: Tap on element with accessibility_id: Search suburb or postcode
[[10:22:27]] [SUCCESS] Screenshot refreshed successfully
[[10:22:27]] [SUCCESS] Screenshot refreshed successfully
[[10:22:27]] [SUCCESS] Screenshot refreshed
[[10:22:27]] [INFO] Refreshing screenshot...
[[10:22:27]] [INFO] QMXBlswP6H=pass
[[10:22:23]] [INFO] QMXBlswP6H=running
[[10:22:23]] [INFO] Executing action 404/643: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[10:22:23]] [SUCCESS] Screenshot refreshed successfully
[[10:22:23]] [SUCCESS] Screenshot refreshed successfully
[[10:22:22]] [SUCCESS] Screenshot refreshed
[[10:22:22]] [INFO] Refreshing screenshot...
[[10:22:22]] [INFO] m0956RsrdM=pass
[[10:22:19]] [SUCCESS] Screenshot refreshed successfully
[[10:22:19]] [SUCCESS] Screenshot refreshed successfully
[[10:22:19]] [INFO] m0956RsrdM=running
[[10:22:19]] [INFO] Executing action 403/643: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[10:22:19]] [SUCCESS] Screenshot refreshed
[[10:22:19]] [INFO] Refreshing screenshot...
[[10:22:18]] [SUCCESS] Screenshot refreshed successfully
[[10:22:18]] [SUCCESS] Screenshot refreshed successfully
[[10:22:18]] [SUCCESS] Screenshot refreshed
[[10:22:18]] [INFO] Refreshing screenshot...
[[10:22:14]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:22:14]] [SUCCESS] Screenshot refreshed successfully
[[10:22:14]] [SUCCESS] Screenshot refreshed successfully
[[10:22:13]] [SUCCESS] Screenshot refreshed
[[10:22:13]] [INFO] Refreshing screenshot...
[[10:22:09]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:22:09]] [SUCCESS] Screenshot refreshed successfully
[[10:22:09]] [SUCCESS] Screenshot refreshed successfully
[[10:22:09]] [SUCCESS] Screenshot refreshed
[[10:22:09]] [INFO] Refreshing screenshot...
[[10:22:05]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:22:04]] [SUCCESS] Screenshot refreshed successfully
[[10:22:04]] [SUCCESS] Screenshot refreshed successfully
[[10:22:04]] [SUCCESS] Screenshot refreshed
[[10:22:04]] [INFO] Refreshing screenshot...
[[10:22:00]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:22:00]] [SUCCESS] Screenshot refreshed successfully
[[10:22:00]] [SUCCESS] Screenshot refreshed successfully
[[10:22:00]] [SUCCESS] Screenshot refreshed
[[10:22:00]] [INFO] Refreshing screenshot...
[[10:21:54]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:21:54]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[10:21:54]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[10:21:54]] [INFO] HlWGryBWT9=running
[[10:21:54]] [INFO] Executing action 402/643: Execute Test Case: Kmart-Signin (5 steps)
[[10:21:54]] [SUCCESS] Screenshot refreshed successfully
[[10:21:54]] [SUCCESS] Screenshot refreshed successfully
[[10:21:54]] [SUCCESS] Screenshot refreshed
[[10:21:54]] [INFO] Refreshing screenshot...
[[10:21:54]] [INFO] Azb1flbIJJ=pass
[[10:21:50]] [INFO] Azb1flbIJJ=running
[[10:21:50]] [INFO] Executing action 401/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:21:50]] [SUCCESS] Screenshot refreshed successfully
[[10:21:50]] [SUCCESS] Screenshot refreshed successfully
[[10:21:50]] [SUCCESS] Screenshot refreshed
[[10:21:50]] [INFO] Refreshing screenshot...
[[10:21:50]] [INFO] 2xC5fLfLe8=pass
[[10:21:47]] [INFO] 2xC5fLfLe8=running
[[10:21:47]] [INFO] Executing action 400/643: iOS Function: alert_accept
[[10:21:47]] [SUCCESS] Screenshot refreshed successfully
[[10:21:47]] [SUCCESS] Screenshot refreshed successfully
[[10:21:47]] [SUCCESS] Screenshot refreshed
[[10:21:47]] [INFO] Refreshing screenshot...
[[10:21:47]] [INFO] Y8vz7AJD1i=pass
[[10:21:40]] [INFO] Y8vz7AJD1i=running
[[10:21:40]] [INFO] Executing action 399/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:21:40]] [SUCCESS] Screenshot refreshed successfully
[[10:21:40]] [SUCCESS] Screenshot refreshed successfully
[[10:21:39]] [SUCCESS] Screenshot refreshed
[[10:21:39]] [INFO] Refreshing screenshot...
[[10:21:39]] [INFO] H9fy9qcFbZ=pass
[[10:21:26]] [SUCCESS] Screenshot refreshed successfully
[[10:21:26]] [SUCCESS] Screenshot refreshed successfully
[[10:21:26]] [INFO] H9fy9qcFbZ=running
[[10:21:26]] [INFO] Executing action 398/643: Restart app: env[appid]
[[10:21:26]] [SUCCESS] Screenshot refreshed
[[10:21:26]] [INFO] Refreshing screenshot...
[[10:21:25]] [SUCCESS] Screenshot refreshed successfully
[[10:21:25]] [SUCCESS] Screenshot refreshed successfully
[[10:21:25]] [SUCCESS] Screenshot refreshed
[[10:21:25]] [INFO] Refreshing screenshot...
[[10:21:23]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:21:22]] [SUCCESS] Screenshot refreshed successfully
[[10:21:22]] [SUCCESS] Screenshot refreshed successfully
[[10:21:22]] [SUCCESS] Screenshot refreshed
[[10:21:22]] [INFO] Refreshing screenshot...
[[10:21:10]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:21:09]] [SUCCESS] Screenshot refreshed successfully
[[10:21:09]] [SUCCESS] Screenshot refreshed successfully
[[10:21:09]] [SUCCESS] Screenshot refreshed
[[10:21:09]] [INFO] Refreshing screenshot...
[[10:21:06]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:21:05]] [SUCCESS] Screenshot refreshed successfully
[[10:21:05]] [SUCCESS] Screenshot refreshed successfully
[[10:21:05]] [SUCCESS] Screenshot refreshed
[[10:21:05]] [INFO] Refreshing screenshot...
[[10:21:02]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:21:01]] [SUCCESS] Screenshot refreshed successfully
[[10:21:01]] [SUCCESS] Screenshot refreshed successfully
[[10:21:01]] [SUCCESS] Screenshot refreshed
[[10:21:01]] [INFO] Refreshing screenshot...
[[10:20:55]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:20:55]] [SUCCESS] Screenshot refreshed successfully
[[10:20:55]] [SUCCESS] Screenshot refreshed successfully
[[10:20:54]] [SUCCESS] Screenshot refreshed
[[10:20:54]] [INFO] Refreshing screenshot...
[[10:20:49]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:20:49]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:20:48]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:20:48]] [INFO] OMgc2gHHyq=running
[[10:20:48]] [INFO] Executing action 397/643: cleanupSteps action
[[10:20:48]] [SUCCESS] Screenshot refreshed successfully
[[10:20:48]] [SUCCESS] Screenshot refreshed successfully
[[10:20:48]] [SUCCESS] Screenshot refreshed
[[10:20:48]] [INFO] Refreshing screenshot...
[[10:20:48]] [INFO] x4yLCZHaCR=pass
[[10:20:45]] [INFO] x4yLCZHaCR=running
[[10:20:45]] [INFO] Executing action 396/643: Terminate app: env[appid]
[[10:20:45]] [SUCCESS] Screenshot refreshed successfully
[[10:20:45]] [SUCCESS] Screenshot refreshed successfully
[[10:20:45]] [SUCCESS] Screenshot refreshed
[[10:20:45]] [INFO] Refreshing screenshot...
[[10:20:45]] [INFO] 2p13JoJbbA=pass
[[10:20:41]] [INFO] 2p13JoJbbA=running
[[10:20:41]] [INFO] Executing action 395/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:20:41]] [SUCCESS] Screenshot refreshed successfully
[[10:20:41]] [SUCCESS] Screenshot refreshed successfully
[[10:20:41]] [SUCCESS] Screenshot refreshed
[[10:20:41]] [INFO] Refreshing screenshot...
[[10:20:41]] [INFO] qHdMgerbTE=pass
[[10:20:37]] [INFO] qHdMgerbTE=running
[[10:20:37]] [INFO] Executing action 394/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:20:36]] [SUCCESS] Screenshot refreshed successfully
[[10:20:36]] [SUCCESS] Screenshot refreshed successfully
[[10:20:36]] [SUCCESS] Screenshot refreshed
[[10:20:36]] [INFO] Refreshing screenshot...
[[10:20:36]] [INFO] F4NGh9HrLw=pass
[[10:20:32]] [INFO] F4NGh9HrLw=running
[[10:20:32]] [INFO] Executing action 393/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:20:32]] [SUCCESS] Screenshot refreshed successfully
[[10:20:32]] [SUCCESS] Screenshot refreshed successfully
[[10:20:32]] [SUCCESS] Screenshot refreshed
[[10:20:32]] [INFO] Refreshing screenshot...
[[10:20:32]] [INFO] 7mnBGa2GCk=pass
[[10:20:20]] [INFO] 7mnBGa2GCk=running
[[10:20:20]] [INFO] Executing action 392/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Save my location"]"
[[10:20:20]] [SUCCESS] Screenshot refreshed successfully
[[10:20:20]] [SUCCESS] Screenshot refreshed successfully
[[10:20:19]] [SUCCESS] Screenshot refreshed
[[10:20:19]] [INFO] Refreshing screenshot...
[[10:20:19]] [INFO] tWq2Qzn22D=pass
[[10:20:16]] [INFO] tWq2Qzn22D=running
[[10:20:16]] [INFO] Executing action 391/643: Tap on image: env[device-back-img]
[[10:20:15]] [SUCCESS] Screenshot refreshed successfully
[[10:20:15]] [SUCCESS] Screenshot refreshed successfully
[[10:20:15]] [SUCCESS] Screenshot refreshed
[[10:20:15]] [INFO] Refreshing screenshot...
[[10:20:15]] [INFO] ysJIY9A3gq=pass
[[10:20:03]] [INFO] ysJIY9A3gq=running
[[10:20:03]] [INFO] Executing action 390/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]"
[[10:20:02]] [SUCCESS] Screenshot refreshed successfully
[[10:20:02]] [SUCCESS] Screenshot refreshed successfully
[[10:20:02]] [SUCCESS] Screenshot refreshed
[[10:20:02]] [INFO] Refreshing screenshot...
[[10:20:02]] [INFO] jmKjclMUWT=pass
[[10:19:58]] [INFO] jmKjclMUWT=running
[[10:19:58]] [INFO] Executing action 389/643: Tap on Text: "current"
[[10:19:58]] [SUCCESS] Screenshot refreshed successfully
[[10:19:58]] [SUCCESS] Screenshot refreshed successfully
[[10:19:58]] [SUCCESS] Screenshot refreshed
[[10:19:58]] [INFO] Refreshing screenshot...
[[10:19:58]] [INFO] UoH0wdtcLk=pass
[[10:19:53]] [INFO] UoH0wdtcLk=running
[[10:19:53]] [INFO] Executing action 388/643: Tap on Text: "Edit"
[[10:19:53]] [SUCCESS] Screenshot refreshed successfully
[[10:19:53]] [SUCCESS] Screenshot refreshed successfully
[[10:19:52]] [SUCCESS] Screenshot refreshed
[[10:19:52]] [INFO] Refreshing screenshot...
[[10:19:52]] [INFO] U48qCNydwd=pass
[[10:19:48]] [INFO] U48qCNydwd=running
[[10:19:48]] [INFO] Executing action 387/643: Restart app: env[appid]
[[10:19:47]] [SUCCESS] Screenshot refreshed successfully
[[10:19:47]] [SUCCESS] Screenshot refreshed successfully
[[10:19:47]] [SUCCESS] Screenshot refreshed
[[10:19:47]] [INFO] Refreshing screenshot...
[[10:19:47]] [INFO] XjclKOaCTh=pass
[[10:19:43]] [INFO] XjclKOaCTh=running
[[10:19:43]] [INFO] Executing action 386/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[10:19:42]] [SUCCESS] Screenshot refreshed successfully
[[10:19:42]] [SUCCESS] Screenshot refreshed successfully
[[10:19:42]] [SUCCESS] Screenshot refreshed
[[10:19:42]] [INFO] Refreshing screenshot...
[[10:19:42]] [INFO] q6cKxgMAIn=pass
[[10:19:39]] [INFO] q6cKxgMAIn=running
[[10:19:39]] [INFO] Executing action 385/643: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[10:19:39]] [SUCCESS] Screenshot refreshed successfully
[[10:19:39]] [SUCCESS] Screenshot refreshed successfully
[[10:19:38]] [SUCCESS] Screenshot refreshed
[[10:19:38]] [INFO] Refreshing screenshot...
[[10:19:38]] [INFO] zdh8hKYC1a=pass
[[10:19:35]] [INFO] zdh8hKYC1a=running
[[10:19:35]] [INFO] Executing action 384/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[10:19:34]] [SUCCESS] Screenshot refreshed successfully
[[10:19:34]] [SUCCESS] Screenshot refreshed successfully
[[10:19:34]] [SUCCESS] Screenshot refreshed
[[10:19:34]] [INFO] Refreshing screenshot...
[[10:19:34]] [INFO] P4b2BITpCf=pass
[[10:19:31]] [INFO] P4b2BITpCf=running
[[10:19:31]] [INFO] Executing action 383/643: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[10:19:31]] [SUCCESS] Screenshot refreshed successfully
[[10:19:31]] [SUCCESS] Screenshot refreshed successfully
[[10:19:31]] [SUCCESS] Screenshot refreshed
[[10:19:31]] [INFO] Refreshing screenshot...
[[10:19:31]] [INFO] inrxgdWzXr=pass
[[10:19:27]] [INFO] inrxgdWzXr=running
[[10:19:27]] [INFO] Executing action 382/643: Tap on Text: "Store"
[[10:19:26]] [SUCCESS] Screenshot refreshed successfully
[[10:19:26]] [SUCCESS] Screenshot refreshed successfully
[[10:19:26]] [SUCCESS] Screenshot refreshed
[[10:19:26]] [INFO] Refreshing screenshot...
[[10:19:26]] [INFO] inrxgdWzXr=pass
[[10:19:22]] [INFO] inrxgdWzXr=running
[[10:19:22]] [INFO] Executing action 381/643: Tap on Text: "receipts"
[[10:19:22]] [SUCCESS] Screenshot refreshed successfully
[[10:19:22]] [SUCCESS] Screenshot refreshed successfully
[[10:19:22]] [SUCCESS] Screenshot refreshed
[[10:19:22]] [INFO] Refreshing screenshot...
[[10:19:22]] [INFO] GEMv6goQtW=pass
[[10:19:18]] [INFO] GEMv6goQtW=running
[[10:19:18]] [INFO] Executing action 380/643: Tap on image: env[device-back-img]
[[10:19:18]] [SUCCESS] Screenshot refreshed successfully
[[10:19:18]] [SUCCESS] Screenshot refreshed successfully
[[10:19:18]] [SUCCESS] Screenshot refreshed
[[10:19:18]] [INFO] Refreshing screenshot...
[[10:19:18]] [INFO] DhWa2PCBXE=pass
[[10:19:15]] [INFO] DhWa2PCBXE=running
[[10:19:15]] [INFO] Executing action 379/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[10:19:14]] [SUCCESS] Screenshot refreshed successfully
[[10:19:14]] [SUCCESS] Screenshot refreshed successfully
[[10:19:14]] [SUCCESS] Screenshot refreshed
[[10:19:14]] [INFO] Refreshing screenshot...
[[10:19:14]] [INFO] pk2DLZFBmx=pass
[[10:19:11]] [INFO] pk2DLZFBmx=running
[[10:19:11]] [INFO] Executing action 378/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[10:19:10]] [SUCCESS] Screenshot refreshed successfully
[[10:19:10]] [SUCCESS] Screenshot refreshed successfully
[[10:19:10]] [SUCCESS] Screenshot refreshed
[[10:19:10]] [INFO] Refreshing screenshot...
[[10:19:10]] [INFO] ShJSdXvmVL=pass
[[10:19:06]] [INFO] ShJSdXvmVL=running
[[10:19:06]] [INFO] Executing action 377/643: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[10:19:06]] [SUCCESS] Screenshot refreshed successfully
[[10:19:06]] [SUCCESS] Screenshot refreshed successfully
[[10:19:05]] [SUCCESS] Screenshot refreshed
[[10:19:05]] [INFO] Refreshing screenshot...
[[10:19:05]] [INFO] A57bC3QuEM=pass
[[10:19:01]] [INFO] A57bC3QuEM=running
[[10:19:01]] [INFO] Executing action 376/643: iOS Function: text - Text: "Wonderbaby@5"
[[10:19:01]] [SUCCESS] Screenshot refreshed successfully
[[10:19:01]] [SUCCESS] Screenshot refreshed successfully
[[10:19:01]] [SUCCESS] Screenshot refreshed
[[10:19:01]] [INFO] Refreshing screenshot...
[[10:19:01]] [INFO] d6vTfR4Y0D=pass
[[10:18:57]] [INFO] d6vTfR4Y0D=running
[[10:18:57]] [INFO] Executing action 375/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:18:56]] [SUCCESS] Screenshot refreshed successfully
[[10:18:56]] [SUCCESS] Screenshot refreshed successfully
[[10:18:56]] [SUCCESS] Screenshot refreshed
[[10:18:56]] [INFO] Refreshing screenshot...
[[10:18:56]] [INFO] g2CqCO1Kr6=pass
[[10:18:52]] [INFO] g2CqCO1Kr6=running
[[10:18:52]] [INFO] Executing action 374/643: iOS Function: text - Text: "<EMAIL>"
[[10:18:51]] [SUCCESS] Screenshot refreshed successfully
[[10:18:51]] [SUCCESS] Screenshot refreshed successfully
[[10:18:51]] [SUCCESS] Screenshot refreshed
[[10:18:51]] [INFO] Refreshing screenshot...
[[10:18:51]] [INFO] u928vFzSni=pass
[[10:18:47]] [INFO] u928vFzSni=running
[[10:18:47]] [INFO] Executing action 373/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:18:47]] [SUCCESS] Screenshot refreshed successfully
[[10:18:47]] [SUCCESS] Screenshot refreshed successfully
[[10:18:47]] [SUCCESS] Screenshot refreshed
[[10:18:47]] [INFO] Refreshing screenshot...
[[10:18:47]] [INFO] s0WyiD1w0B=pass
[[10:18:44]] [INFO] s0WyiD1w0B=running
[[10:18:44]] [INFO] Executing action 372/643: iOS Function: alert_accept
[[10:18:44]] [SUCCESS] Screenshot refreshed successfully
[[10:18:44]] [SUCCESS] Screenshot refreshed successfully
[[10:18:44]] [SUCCESS] Screenshot refreshed
[[10:18:44]] [INFO] Refreshing screenshot...
[[10:18:44]] [INFO] gekNSY5O2E=pass
[[10:18:40]] [INFO] gekNSY5O2E=running
[[10:18:40]] [INFO] Executing action 371/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[10:18:39]] [SUCCESS] Screenshot refreshed successfully
[[10:18:39]] [SUCCESS] Screenshot refreshed successfully
[[10:18:39]] [SUCCESS] Screenshot refreshed
[[10:18:39]] [INFO] Refreshing screenshot...
[[10:18:39]] [INFO] VJJ3EXXotU=pass
[[10:18:36]] [INFO] VJJ3EXXotU=running
[[10:18:36]] [INFO] Executing action 370/643: Tap on image: env[device-back-img]
[[10:18:35]] [SUCCESS] Screenshot refreshed successfully
[[10:18:35]] [SUCCESS] Screenshot refreshed successfully
[[10:18:35]] [SUCCESS] Screenshot refreshed
[[10:18:35]] [INFO] Refreshing screenshot...
[[10:18:35]] [INFO] 83tV9A4NOn=pass
[[10:18:32]] [INFO] 83tV9A4NOn=running
[[10:18:32]] [INFO] Executing action 369/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[10:18:32]] [SUCCESS] Screenshot refreshed successfully
[[10:18:32]] [SUCCESS] Screenshot refreshed successfully
[[10:18:32]] [SUCCESS] Screenshot refreshed
[[10:18:32]] [INFO] Refreshing screenshot...
[[10:18:32]] [INFO] aNN0yYFLEd=pass
[[10:18:28]] [INFO] aNN0yYFLEd=running
[[10:18:28]] [INFO] Executing action 368/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[10:18:28]] [SUCCESS] Screenshot refreshed successfully
[[10:18:28]] [SUCCESS] Screenshot refreshed successfully
[[10:18:28]] [SUCCESS] Screenshot refreshed
[[10:18:28]] [INFO] Refreshing screenshot...
[[10:18:28]] [INFO] XJv08Gkucs=pass
[[10:18:24]] [INFO] XJv08Gkucs=running
[[10:18:24]] [INFO] Executing action 367/643: Input text: "<EMAIL>"
[[10:18:24]] [SUCCESS] Screenshot refreshed successfully
[[10:18:24]] [SUCCESS] Screenshot refreshed successfully
[[10:18:24]] [SUCCESS] Screenshot refreshed
[[10:18:24]] [INFO] Refreshing screenshot...
[[10:18:24]] [INFO] kAQ1yIIw3h=pass
[[10:18:20]] [INFO] kAQ1yIIw3h=running
[[10:18:20]] [INFO] Executing action 366/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[10:18:20]] [SUCCESS] Screenshot refreshed successfully
[[10:18:20]] [SUCCESS] Screenshot refreshed successfully
[[10:18:20]] [SUCCESS] Screenshot refreshed
[[10:18:20]] [INFO] Refreshing screenshot...
[[10:18:20]] [INFO] 7YbjwQH1Jc=pass
[[10:18:17]] [INFO] 7YbjwQH1Jc=running
[[10:18:17]] [INFO] Executing action 365/643: Input text: "env[searchorder]"
[[10:18:16]] [SUCCESS] Screenshot refreshed successfully
[[10:18:16]] [SUCCESS] Screenshot refreshed successfully
[[10:18:16]] [SUCCESS] Screenshot refreshed
[[10:18:16]] [INFO] Refreshing screenshot...
[[10:18:16]] [INFO] OmKfD9iBjD=pass
[[10:18:13]] [INFO] OmKfD9iBjD=running
[[10:18:13]] [INFO] Executing action 364/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[10:18:12]] [SUCCESS] Screenshot refreshed successfully
[[10:18:12]] [SUCCESS] Screenshot refreshed successfully
[[10:18:12]] [SUCCESS] Screenshot refreshed
[[10:18:12]] [INFO] Refreshing screenshot...
[[10:18:12]] [INFO] eHLWiRoqqS=pass
[[10:18:08]] [INFO] eHLWiRoqqS=running
[[10:18:08]] [INFO] Executing action 363/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[10:18:08]] [SUCCESS] Screenshot refreshed successfully
[[10:18:08]] [SUCCESS] Screenshot refreshed successfully
[[10:18:08]] [SUCCESS] Screenshot refreshed
[[10:18:08]] [INFO] Refreshing screenshot...
[[10:18:08]] [INFO] F4NGh9HrLw=pass
[[10:18:04]] [INFO] F4NGh9HrLw=running
[[10:18:04]] [INFO] Executing action 362/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:18:04]] [SUCCESS] Screenshot refreshed successfully
[[10:18:04]] [SUCCESS] Screenshot refreshed successfully
[[10:18:04]] [SUCCESS] Screenshot refreshed
[[10:18:04]] [INFO] Refreshing screenshot...
[[10:18:04]] [INFO] 74XW7x54ad=pass
[[10:18:00]] [INFO] 74XW7x54ad=running
[[10:18:00]] [INFO] Executing action 361/643: Tap on image: env[device-back-img]
[[10:17:59]] [SUCCESS] Screenshot refreshed successfully
[[10:17:59]] [SUCCESS] Screenshot refreshed successfully
[[10:17:59]] [SUCCESS] Screenshot refreshed
[[10:17:59]] [INFO] Refreshing screenshot...
[[10:17:59]] [INFO] xUbWFa8Ok2=pass
[[10:17:56]] [INFO] xUbWFa8Ok2=running
[[10:17:56]] [INFO] Executing action 360/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[10:17:56]] [SUCCESS] Screenshot refreshed successfully
[[10:17:56]] [SUCCESS] Screenshot refreshed successfully
[[10:17:56]] [SUCCESS] Screenshot refreshed
[[10:17:56]] [INFO] Refreshing screenshot...
[[10:17:56]] [INFO] RbNtEW6N9T=pass
[[10:17:53]] [INFO] RbNtEW6N9T=running
[[10:17:53]] [INFO] Executing action 359/643: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[10:17:53]] [SUCCESS] Screenshot refreshed successfully
[[10:17:53]] [SUCCESS] Screenshot refreshed successfully
[[10:17:52]] [SUCCESS] Screenshot refreshed
[[10:17:52]] [INFO] Refreshing screenshot...
[[10:17:52]] [INFO] F4NGh9HrLw=pass
[[10:17:50]] [INFO] F4NGh9HrLw=running
[[10:17:50]] [INFO] Executing action 358/643: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[10:17:49]] [SUCCESS] Screenshot refreshed successfully
[[10:17:49]] [SUCCESS] Screenshot refreshed successfully
[[10:17:49]] [SUCCESS] Screenshot refreshed
[[10:17:49]] [INFO] Refreshing screenshot...
[[10:17:49]] [INFO] RlDZFks4Lc=pass
[[10:17:47]] [INFO] RlDZFks4Lc=running
[[10:17:47]] [INFO] Executing action 357/643: iOS Function: alert_accept
[[10:17:46]] [SUCCESS] Screenshot refreshed successfully
[[10:17:46]] [SUCCESS] Screenshot refreshed successfully
[[10:17:46]] [SUCCESS] Screenshot refreshed
[[10:17:46]] [INFO] Refreshing screenshot...
[[10:17:46]] [INFO] Dzn2Q7JTe0=pass
[[10:17:42]] [INFO] Dzn2Q7JTe0=running
[[10:17:42]] [INFO] Executing action 356/643: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[10:17:42]] [SUCCESS] Screenshot refreshed successfully
[[10:17:42]] [SUCCESS] Screenshot refreshed successfully
[[10:17:42]] [SUCCESS] Screenshot refreshed
[[10:17:42]] [INFO] Refreshing screenshot...
[[10:17:42]] [INFO] H9fy9qcFbZ=pass
[[10:17:28]] [SUCCESS] Screenshot refreshed successfully
[[10:17:28]] [SUCCESS] Screenshot refreshed successfully
[[10:17:28]] [INFO] H9fy9qcFbZ=running
[[10:17:28]] [INFO] Executing action 355/643: Restart app: env[appid]
[[10:17:28]] [SUCCESS] Screenshot refreshed
[[10:17:28]] [INFO] Refreshing screenshot...
[[10:17:28]] [SUCCESS] Screenshot refreshed successfully
[[10:17:28]] [SUCCESS] Screenshot refreshed successfully
[[10:17:28]] [SUCCESS] Screenshot refreshed
[[10:17:28]] [INFO] Refreshing screenshot...
[[10:17:25]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:17:25]] [SUCCESS] Screenshot refreshed successfully
[[10:17:25]] [SUCCESS] Screenshot refreshed successfully
[[10:17:25]] [SUCCESS] Screenshot refreshed
[[10:17:25]] [INFO] Refreshing screenshot...
[[10:17:12]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:17:12]] [SUCCESS] Screenshot refreshed successfully
[[10:17:12]] [SUCCESS] Screenshot refreshed successfully
[[10:17:12]] [SUCCESS] Screenshot refreshed
[[10:17:12]] [INFO] Refreshing screenshot...
[[10:17:08]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:17:08]] [SUCCESS] Screenshot refreshed successfully
[[10:17:08]] [SUCCESS] Screenshot refreshed successfully
[[10:17:08]] [SUCCESS] Screenshot refreshed
[[10:17:08]] [INFO] Refreshing screenshot...
[[10:17:04]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:17:04]] [SUCCESS] Screenshot refreshed successfully
[[10:17:04]] [SUCCESS] Screenshot refreshed successfully
[[10:17:03]] [SUCCESS] Screenshot refreshed
[[10:17:03]] [INFO] Refreshing screenshot...
[[10:16:57]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:16:57]] [SUCCESS] Screenshot refreshed successfully
[[10:16:57]] [SUCCESS] Screenshot refreshed successfully
[[10:16:57]] [SUCCESS] Screenshot refreshed
[[10:16:57]] [INFO] Refreshing screenshot...
[[10:16:50]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:16:50]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:16:50]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:16:50]] [INFO] AeQaElnzUN=running
[[10:16:50]] [INFO] Executing action 354/643: cleanupSteps action
[[10:16:50]] [SUCCESS] Screenshot refreshed successfully
[[10:16:50]] [SUCCESS] Screenshot refreshed successfully
[[10:16:49]] [SUCCESS] Screenshot refreshed
[[10:16:49]] [INFO] Refreshing screenshot...
[[10:16:49]] [INFO] BracBsfa3Y=pass
[[10:16:45]] [INFO] BracBsfa3Y=running
[[10:16:45]] [INFO] Executing action 353/643: Tap on Text: "out"
[[10:16:45]] [SUCCESS] Screenshot refreshed successfully
[[10:16:45]] [SUCCESS] Screenshot refreshed successfully
[[10:16:45]] [SUCCESS] Screenshot refreshed
[[10:16:45]] [INFO] Refreshing screenshot...
[[10:16:45]] [INFO] s6tWdQ5URW=pass
[[10:16:38]] [INFO] s6tWdQ5URW=running
[[10:16:38]] [INFO] Executing action 352/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:16:38]] [SUCCESS] Screenshot refreshed successfully
[[10:16:38]] [SUCCESS] Screenshot refreshed successfully
[[10:16:38]] [SUCCESS] Screenshot refreshed
[[10:16:38]] [INFO] Refreshing screenshot...
[[10:16:38]] [INFO] wNGRrfUjpK=pass
[[10:16:34]] [INFO] wNGRrfUjpK=running
[[10:16:34]] [INFO] Executing action 351/643: Tap on image: env[device-back-img]
[[10:16:34]] [SUCCESS] Screenshot refreshed successfully
[[10:16:34]] [SUCCESS] Screenshot refreshed successfully
[[10:16:34]] [SUCCESS] Screenshot refreshed
[[10:16:34]] [INFO] Refreshing screenshot...
[[10:16:34]] [INFO] BracBsfa3Y=pass
[[10:16:30]] [INFO] BracBsfa3Y=running
[[10:16:30]] [INFO] Executing action 350/643: Tap on Text: "Customer"
[[10:16:29]] [SUCCESS] Screenshot refreshed successfully
[[10:16:29]] [SUCCESS] Screenshot refreshed successfully
[[10:16:29]] [SUCCESS] Screenshot refreshed
[[10:16:29]] [INFO] Refreshing screenshot...
[[10:16:29]] [INFO] H4WfwVU8YP=pass
[[10:16:25]] [INFO] H4WfwVU8YP=running
[[10:16:25]] [INFO] Executing action 349/643: Tap on image: banner-close-updated.png
[[10:16:25]] [SUCCESS] Screenshot refreshed successfully
[[10:16:25]] [SUCCESS] Screenshot refreshed successfully
[[10:16:25]] [SUCCESS] Screenshot refreshed
[[10:16:25]] [INFO] Refreshing screenshot...
[[10:16:25]] [INFO] ePyaYpttQA=pass
[[10:16:21]] [INFO] ePyaYpttQA=running
[[10:16:21]] [INFO] Executing action 348/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[10:16:21]] [SUCCESS] Screenshot refreshed successfully
[[10:16:21]] [SUCCESS] Screenshot refreshed successfully
[[10:16:21]] [SUCCESS] Screenshot refreshed
[[10:16:21]] [INFO] Refreshing screenshot...
[[10:16:21]] [INFO] BracBsfa3Y=pass
[[10:16:17]] [INFO] BracBsfa3Y=running
[[10:16:17]] [INFO] Executing action 347/643: Tap on Text: "Invite"
[[10:16:17]] [SUCCESS] Screenshot refreshed successfully
[[10:16:17]] [SUCCESS] Screenshot refreshed successfully
[[10:16:16]] [SUCCESS] Screenshot refreshed
[[10:16:16]] [INFO] Refreshing screenshot...
[[10:16:16]] [INFO] xVbCNStsOP=pass
[[10:16:13]] [INFO] xVbCNStsOP=running
[[10:16:13]] [INFO] Executing action 346/643: Tap on image: env[device-back-img]
[[10:16:12]] [SUCCESS] Screenshot refreshed successfully
[[10:16:12]] [SUCCESS] Screenshot refreshed successfully
[[10:16:12]] [SUCCESS] Screenshot refreshed
[[10:16:12]] [INFO] Refreshing screenshot...
[[10:16:12]] [INFO] 8kQkC2FGyZ=pass
[[10:16:09]] [INFO] 8kQkC2FGyZ=running
[[10:16:09]] [INFO] Executing action 345/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[10:16:09]] [SUCCESS] Screenshot refreshed successfully
[[10:16:09]] [SUCCESS] Screenshot refreshed successfully
[[10:16:09]] [SUCCESS] Screenshot refreshed
[[10:16:09]] [INFO] Refreshing screenshot...
[[10:16:09]] [INFO] PgjJCrKFYo=pass
[[10:16:04]] [INFO] PgjJCrKFYo=running
[[10:16:04]] [INFO] Executing action 344/643: Tap on Text: "VIC"
[[10:16:04]] [SUCCESS] Screenshot refreshed successfully
[[10:16:04]] [SUCCESS] Screenshot refreshed successfully
[[10:16:04]] [SUCCESS] Screenshot refreshed
[[10:16:04]] [INFO] Refreshing screenshot...
[[10:16:04]] [INFO] 3Si0csRNaw=pass
[[10:15:57]] [INFO] 3Si0csRNaw=running
[[10:15:57]] [INFO] Executing action 343/643: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[10:15:57]] [SUCCESS] Screenshot refreshed successfully
[[10:15:57]] [SUCCESS] Screenshot refreshed successfully
[[10:15:57]] [SUCCESS] Screenshot refreshed
[[10:15:57]] [INFO] Refreshing screenshot...
[[10:15:57]] [INFO] BracBsfa3Y=pass
[[10:15:52]] [INFO] BracBsfa3Y=running
[[10:15:52]] [INFO] Executing action 342/643: Tap on Text: "Nearby"
[[10:15:52]] [SUCCESS] Screenshot refreshed successfully
[[10:15:52]] [SUCCESS] Screenshot refreshed successfully
[[10:15:52]] [SUCCESS] Screenshot refreshed
[[10:15:52]] [INFO] Refreshing screenshot...
[[10:15:52]] [INFO] BracBsfa3Y=pass
[[10:15:48]] [INFO] BracBsfa3Y=running
[[10:15:48]] [INFO] Executing action 341/643: Tap on Text: "locator"
[[10:15:47]] [SUCCESS] Screenshot refreshed successfully
[[10:15:47]] [SUCCESS] Screenshot refreshed successfully
[[10:15:47]] [SUCCESS] Screenshot refreshed
[[10:15:47]] [INFO] Refreshing screenshot...
[[10:15:47]] [INFO] s6tWdQ5URW=pass
[[10:15:40]] [INFO] s6tWdQ5URW=running
[[10:15:40]] [INFO] Executing action 340/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:15:40]] [SUCCESS] Screenshot refreshed successfully
[[10:15:40]] [SUCCESS] Screenshot refreshed successfully
[[10:15:40]] [SUCCESS] Screenshot refreshed
[[10:15:40]] [INFO] Refreshing screenshot...
[[10:15:40]] [INFO] 2M0KHOVecv=pass
[[10:15:37]] [INFO] 2M0KHOVecv=running
[[10:15:37]] [INFO] Executing action 339/643: Check if element with accessibility_id="txtMy Flybuys card" exists
[[10:15:36]] [SUCCESS] Screenshot refreshed successfully
[[10:15:36]] [SUCCESS] Screenshot refreshed successfully
[[10:15:36]] [SUCCESS] Screenshot refreshed
[[10:15:36]] [INFO] Refreshing screenshot...
[[10:15:36]] [INFO] LBgsj3oLcu=pass
[[10:15:32]] [INFO] LBgsj3oLcu=running
[[10:15:32]] [INFO] Executing action 338/643: Tap on image: env[device-back-img]
[[10:15:32]] [SUCCESS] Screenshot refreshed successfully
[[10:15:32]] [SUCCESS] Screenshot refreshed successfully
[[10:15:32]] [SUCCESS] Screenshot refreshed
[[10:15:32]] [INFO] Refreshing screenshot...
[[10:15:32]] [INFO] biRyWs3nSs=pass
[[10:15:26]] [INFO] biRyWs3nSs=running
[[10:15:26]] [INFO] Executing action 337/643: Tap on element with accessibility_id: btnSaveFlybuysCard
[[10:15:26]] [SUCCESS] Screenshot refreshed successfully
[[10:15:26]] [SUCCESS] Screenshot refreshed successfully
[[10:15:26]] [SUCCESS] Screenshot refreshed
[[10:15:26]] [INFO] Refreshing screenshot...
[[10:15:26]] [INFO] 8cFGh3GD68=pass
[[10:15:20]] [INFO] 8cFGh3GD68=running
[[10:15:20]] [INFO] Executing action 336/643: Tap on element with accessibility_id: Done
[[10:15:20]] [SUCCESS] Screenshot refreshed successfully
[[10:15:20]] [SUCCESS] Screenshot refreshed successfully
[[10:15:20]] [SUCCESS] Screenshot refreshed
[[10:15:20]] [INFO] Refreshing screenshot...
[[10:15:20]] [INFO] sLe0Wurhgm=pass
[[10:15:17]] [INFO] sLe0Wurhgm=running
[[10:15:17]] [INFO] Executing action 335/643: Input text: "2791234567890"
[[10:15:17]] [SUCCESS] Screenshot refreshed successfully
[[10:15:17]] [SUCCESS] Screenshot refreshed successfully
[[10:15:16]] [SUCCESS] Screenshot refreshed
[[10:15:16]] [INFO] Refreshing screenshot...
[[10:15:16]] [INFO] Ey86YRVRzU=pass
[[10:15:11]] [INFO] Ey86YRVRzU=running
[[10:15:11]] [INFO] Executing action 334/643: Tap on element with accessibility_id: Flybuys barcode number
[[10:15:11]] [SUCCESS] Screenshot refreshed successfully
[[10:15:11]] [SUCCESS] Screenshot refreshed successfully
[[10:15:10]] [SUCCESS] Screenshot refreshed
[[10:15:10]] [INFO] Refreshing screenshot...
[[10:15:10]] [INFO] Gxhf3XGc6e=pass
[[10:15:05]] [INFO] Gxhf3XGc6e=running
[[10:15:05]] [INFO] Executing action 333/643: Tap on element with accessibility_id: btnLinkFlyBuys
[[10:15:05]] [SUCCESS] Screenshot refreshed successfully
[[10:15:05]] [SUCCESS] Screenshot refreshed successfully
[[10:15:04]] [SUCCESS] Screenshot refreshed
[[10:15:04]] [INFO] Refreshing screenshot...
[[10:15:04]] [INFO] BracBsfa3Y=pass
[[10:15:00]] [INFO] BracBsfa3Y=running
[[10:15:00]] [INFO] Executing action 332/643: Tap on Text: "Flybuys"
[[10:15:00]] [SUCCESS] Screenshot refreshed successfully
[[10:15:00]] [SUCCESS] Screenshot refreshed successfully
[[10:15:00]] [SUCCESS] Screenshot refreshed
[[10:15:00]] [INFO] Refreshing screenshot...
[[10:15:00]] [INFO] Ds5GfNVb3x=pass
[[10:14:54]] [INFO] Ds5GfNVb3x=running
[[10:14:54]] [INFO] Executing action 331/643: Tap on element with accessibility_id: btnRemove
[[10:14:54]] [SUCCESS] Screenshot refreshed successfully
[[10:14:54]] [SUCCESS] Screenshot refreshed successfully
[[10:14:54]] [SUCCESS] Screenshot refreshed
[[10:14:54]] [INFO] Refreshing screenshot...
[[10:14:54]] [INFO] 3ZFgwFaiXp=pass
[[10:14:48]] [INFO] 3ZFgwFaiXp=running
[[10:14:48]] [INFO] Executing action 330/643: Tap on element with accessibility_id: Remove card
[[10:14:48]] [SUCCESS] Screenshot refreshed successfully
[[10:14:48]] [SUCCESS] Screenshot refreshed successfully
[[10:14:48]] [SUCCESS] Screenshot refreshed
[[10:14:48]] [INFO] Refreshing screenshot...
[[10:14:48]] [INFO] 40hnWPsQ9P=pass
[[10:14:43]] [INFO] 40hnWPsQ9P=running
[[10:14:43]] [INFO] Executing action 329/643: Tap on element with accessibility_id: btneditFlybuysCard
[[10:14:42]] [SUCCESS] Screenshot refreshed successfully
[[10:14:42]] [SUCCESS] Screenshot refreshed successfully
[[10:14:42]] [SUCCESS] Screenshot refreshed
[[10:14:42]] [INFO] Refreshing screenshot...
[[10:14:42]] [INFO] 40hnWPsQ9P=pass
[[10:14:37]] [INFO] 40hnWPsQ9P=running
[[10:14:37]] [INFO] Executing action 328/643: Wait till accessibility_id=btneditFlybuysCard
[[10:14:37]] [SUCCESS] Screenshot refreshed successfully
[[10:14:37]] [SUCCESS] Screenshot refreshed successfully
[[10:14:37]] [SUCCESS] Screenshot refreshed
[[10:14:37]] [INFO] Refreshing screenshot...
[[10:14:37]] [INFO] BracBsfa3Y=pass
[[10:14:33]] [INFO] BracBsfa3Y=running
[[10:14:33]] [INFO] Executing action 327/643: Tap on Text: "Flybuys"
[[10:14:33]] [SUCCESS] Screenshot refreshed successfully
[[10:14:33]] [SUCCESS] Screenshot refreshed successfully
[[10:14:32]] [SUCCESS] Screenshot refreshed
[[10:14:32]] [INFO] Refreshing screenshot...
[[10:14:32]] [INFO] MkTFxfzubv=pass
[[10:14:29]] [INFO] MkTFxfzubv=running
[[10:14:29]] [INFO] Executing action 326/643: Tap on image: env[device-back-img]
[[10:14:29]] [SUCCESS] Screenshot refreshed successfully
[[10:14:29]] [SUCCESS] Screenshot refreshed successfully
[[10:14:28]] [SUCCESS] Screenshot refreshed
[[10:14:28]] [INFO] Refreshing screenshot...
[[10:14:28]] [INFO] EO3cMmdUyM=pass
[[10:14:25]] [INFO] EO3cMmdUyM=running
[[10:14:25]] [INFO] Executing action 325/643: Tap on image: env[device-back-img]
[[10:14:25]] [SUCCESS] Screenshot refreshed successfully
[[10:14:25]] [SUCCESS] Screenshot refreshed successfully
[[10:14:24]] [SUCCESS] Screenshot refreshed
[[10:14:24]] [INFO] Refreshing screenshot...
[[10:14:24]] [INFO] napKDohf3Z=pass
[[10:14:20]] [INFO] napKDohf3Z=running
[[10:14:20]] [INFO] Executing action 324/643: Tap on Text: "payment"
[[10:14:20]] [SUCCESS] Screenshot refreshed successfully
[[10:14:20]] [SUCCESS] Screenshot refreshed successfully
[[10:14:20]] [SUCCESS] Screenshot refreshed
[[10:14:20]] [INFO] Refreshing screenshot...
[[10:14:20]] [INFO] ekqt95ZRol=pass
[[10:14:16]] [INFO] ekqt95ZRol=running
[[10:14:16]] [INFO] Executing action 323/643: Tap on image: env[device-back-img]
[[10:14:16]] [SUCCESS] Screenshot refreshed successfully
[[10:14:16]] [SUCCESS] Screenshot refreshed successfully
[[10:14:16]] [SUCCESS] Screenshot refreshed
[[10:14:16]] [INFO] Refreshing screenshot...
[[10:14:16]] [INFO] 20qUCJgpE9=pass
[[10:14:12]] [INFO] 20qUCJgpE9=running
[[10:14:12]] [INFO] Executing action 322/643: Tap on Text: "address"
[[10:14:12]] [SUCCESS] Screenshot refreshed successfully
[[10:14:12]] [SUCCESS] Screenshot refreshed successfully
[[10:14:11]] [SUCCESS] Screenshot refreshed
[[10:14:11]] [INFO] Refreshing screenshot...
[[10:14:11]] [INFO] 6HR2weiXoT=pass
[[10:14:08]] [INFO] 6HR2weiXoT=running
[[10:14:08]] [INFO] Executing action 321/643: Tap on image: env[device-back-img]
[[10:14:07]] [SUCCESS] Screenshot refreshed successfully
[[10:14:07]] [SUCCESS] Screenshot refreshed successfully
[[10:14:07]] [SUCCESS] Screenshot refreshed
[[10:14:07]] [INFO] Refreshing screenshot...
[[10:14:07]] [INFO] 3hOTINBVMf=pass
[[10:14:03]] [INFO] 3hOTINBVMf=running
[[10:14:03]] [INFO] Executing action 320/643: Tap on Text: "details"
[[10:14:03]] [SUCCESS] Screenshot refreshed successfully
[[10:14:03]] [SUCCESS] Screenshot refreshed successfully
[[10:14:03]] [SUCCESS] Screenshot refreshed
[[10:14:03]] [INFO] Refreshing screenshot...
[[10:14:03]] [INFO] yJi0WxnERj=pass
[[10:13:59]] [INFO] yJi0WxnERj=running
[[10:13:59]] [INFO] Executing action 319/643: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[10:13:59]] [SUCCESS] Screenshot refreshed successfully
[[10:13:59]] [SUCCESS] Screenshot refreshed successfully
[[10:13:59]] [SUCCESS] Screenshot refreshed
[[10:13:59]] [INFO] Refreshing screenshot...
[[10:13:59]] [INFO] PbfHAtFQPP=pass
[[10:13:55]] [INFO] PbfHAtFQPP=running
[[10:13:55]] [INFO] Executing action 318/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:13:54]] [SUCCESS] Screenshot refreshed successfully
[[10:13:54]] [SUCCESS] Screenshot refreshed successfully
[[10:13:54]] [SUCCESS] Screenshot refreshed
[[10:13:54]] [INFO] Refreshing screenshot...
[[10:13:54]] [INFO] 6qZnk86hGg=pass
[[10:13:50]] [INFO] 6qZnk86hGg=running
[[10:13:50]] [INFO] Executing action 317/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[10:13:50]] [SUCCESS] Screenshot refreshed successfully
[[10:13:50]] [SUCCESS] Screenshot refreshed successfully
[[10:13:50]] [SUCCESS] Screenshot refreshed
[[10:13:50]] [INFO] Refreshing screenshot...
[[10:13:50]] [INFO] FAvQgIuHc1=pass
[[10:13:46]] [INFO] FAvQgIuHc1=running
[[10:13:46]] [INFO] Executing action 316/643: Tap on Text: "Return"
[[10:13:45]] [SUCCESS] Screenshot refreshed successfully
[[10:13:45]] [SUCCESS] Screenshot refreshed successfully
[[10:13:45]] [SUCCESS] Screenshot refreshed
[[10:13:45]] [INFO] Refreshing screenshot...
[[10:13:45]] [INFO] vmc01sHkbr=pass
[[10:13:39]] [INFO] vmc01sHkbr=running
[[10:13:39]] [INFO] Executing action 315/643: Wait for 5 ms
[[10:13:38]] [SUCCESS] Screenshot refreshed successfully
[[10:13:38]] [SUCCESS] Screenshot refreshed successfully
[[10:13:38]] [SUCCESS] Screenshot refreshed
[[10:13:38]] [INFO] Refreshing screenshot...
[[10:13:38]] [INFO] zeu0wd1vcF=pass
[[10:13:26]] [INFO] zeu0wd1vcF=running
[[10:13:26]] [INFO] Executing action 314/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:13:25]] [SUCCESS] Screenshot refreshed successfully
[[10:13:25]] [SUCCESS] Screenshot refreshed successfully
[[10:13:25]] [SUCCESS] Screenshot refreshed
[[10:13:25]] [INFO] Refreshing screenshot...
[[10:13:25]] [INFO] OwWeZes4aT=pass
[[10:13:22]] [INFO] OwWeZes4aT=running
[[10:13:22]] [INFO] Executing action 313/643: Tap on image: env[device-back-img]
[[10:13:21]] [SUCCESS] Screenshot refreshed successfully
[[10:13:21]] [SUCCESS] Screenshot refreshed successfully
[[10:13:21]] [SUCCESS] Screenshot refreshed
[[10:13:21]] [INFO] Refreshing screenshot...
[[10:13:21]] [INFO] aAaTtUE92h=pass
[[10:13:18]] [INFO] aAaTtUE92h=running
[[10:13:18]] [INFO] Executing action 312/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[10:13:18]] [SUCCESS] Screenshot refreshed successfully
[[10:13:18]] [SUCCESS] Screenshot refreshed successfully
[[10:13:18]] [SUCCESS] Screenshot refreshed
[[10:13:18]] [INFO] Refreshing screenshot...
[[10:13:18]] [INFO] 9iOZGMqAZK=pass
[[10:13:14]] [INFO] 9iOZGMqAZK=running
[[10:13:14]] [INFO] Executing action 311/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[10:13:14]] [SUCCESS] Screenshot refreshed successfully
[[10:13:14]] [SUCCESS] Screenshot refreshed successfully
[[10:13:14]] [SUCCESS] Screenshot refreshed
[[10:13:14]] [INFO] Refreshing screenshot...
[[10:13:14]] [INFO] mRTYzOFRRw=pass
[[10:13:11]] [INFO] mRTYzOFRRw=running
[[10:13:11]] [INFO] Executing action 310/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[10:13:10]] [SUCCESS] Screenshot refreshed successfully
[[10:13:10]] [SUCCESS] Screenshot refreshed successfully
[[10:13:10]] [SUCCESS] Screenshot refreshed
[[10:13:10]] [INFO] Refreshing screenshot...
[[10:13:10]] [INFO] 7g6MFJSGIO=pass
[[10:13:06]] [INFO] 7g6MFJSGIO=running
[[10:13:06]] [INFO] Executing action 309/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[10:13:06]] [SUCCESS] Screenshot refreshed successfully
[[10:13:06]] [SUCCESS] Screenshot refreshed successfully
[[10:13:06]] [SUCCESS] Screenshot refreshed
[[10:13:06]] [INFO] Refreshing screenshot...
[[10:13:06]] [INFO] zNwyPagPE1=pass
[[10:13:00]] [INFO] zNwyPagPE1=running
[[10:13:00]] [INFO] Executing action 308/643: Wait for 5 ms
[[10:12:59]] [SUCCESS] Screenshot refreshed successfully
[[10:12:59]] [SUCCESS] Screenshot refreshed successfully
[[10:12:59]] [SUCCESS] Screenshot refreshed
[[10:12:59]] [INFO] Refreshing screenshot...
[[10:12:59]] [INFO] qXsL3wzg6J=pass
[[10:12:56]] [INFO] qXsL3wzg6J=running
[[10:12:56]] [INFO] Executing action 307/643: Tap on image: env[device-back-img]
[[10:12:55]] [SUCCESS] Screenshot refreshed successfully
[[10:12:55]] [SUCCESS] Screenshot refreshed successfully
[[10:12:55]] [SUCCESS] Screenshot refreshed
[[10:12:55]] [INFO] Refreshing screenshot...
[[10:12:55]] [INFO] YuuQe2KupX=pass
[[10:12:51]] [INFO] YuuQe2KupX=running
[[10:12:51]] [INFO] Executing action 306/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[10:12:50]] [SUCCESS] Screenshot refreshed successfully
[[10:12:50]] [SUCCESS] Screenshot refreshed successfully
[[10:12:50]] [SUCCESS] Screenshot refreshed
[[10:12:50]] [INFO] Refreshing screenshot...
[[10:12:50]] [INFO] g0PE7Mofye=pass
[[10:12:45]] [INFO] g0PE7Mofye=running
[[10:12:45]] [INFO] Executing action 305/643: Tap on element with accessibility_id: Print order details
[[10:12:44]] [SUCCESS] Screenshot refreshed successfully
[[10:12:44]] [SUCCESS] Screenshot refreshed successfully
[[10:12:44]] [SUCCESS] Screenshot refreshed
[[10:12:44]] [INFO] Refreshing screenshot...
[[10:12:44]] [INFO] GgQaBLWYkb=pass
[[10:12:40]] [INFO] GgQaBLWYkb=running
[[10:12:40]] [INFO] Executing action 304/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[10:12:40]] [SUCCESS] Screenshot refreshed successfully
[[10:12:40]] [SUCCESS] Screenshot refreshed successfully
[[10:12:40]] [SUCCESS] Screenshot refreshed
[[10:12:40]] [INFO] Refreshing screenshot...
[[10:12:40]] [INFO] f3OrHHzTFN=pass
[[10:12:24]] [INFO] f3OrHHzTFN=running
[[10:12:24]] [INFO] Executing action 303/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[10:12:24]] [SUCCESS] Screenshot refreshed successfully
[[10:12:24]] [SUCCESS] Screenshot refreshed successfully
[[10:12:24]] [SUCCESS] Screenshot refreshed
[[10:12:24]] [INFO] Refreshing screenshot...
[[10:12:24]] [INFO] 7g6MFJSGIO=pass
[[10:12:20]] [INFO] 7g6MFJSGIO=running
[[10:12:20]] [INFO] Executing action 302/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[10:12:19]] [SUCCESS] Screenshot refreshed successfully
[[10:12:19]] [SUCCESS] Screenshot refreshed successfully
[[10:12:19]] [SUCCESS] Screenshot refreshed
[[10:12:19]] [INFO] Refreshing screenshot...
[[10:12:19]] [INFO] Z6g3sGuHTp=pass
[[10:12:13]] [INFO] Z6g3sGuHTp=running
[[10:12:13]] [INFO] Executing action 301/643: Wait for 5 ms
[[10:12:13]] [SUCCESS] Screenshot refreshed successfully
[[10:12:13]] [SUCCESS] Screenshot refreshed successfully
[[10:12:12]] [SUCCESS] Screenshot refreshed
[[10:12:12]] [INFO] Refreshing screenshot...
[[10:12:12]] [INFO] pFlYwTS53v=pass
[[10:12:08]] [INFO] pFlYwTS53v=running
[[10:12:08]] [INFO] Executing action 300/643: Tap on Text: "receipts"
[[10:12:08]] [SUCCESS] Screenshot refreshed successfully
[[10:12:08]] [SUCCESS] Screenshot refreshed successfully
[[10:12:08]] [SUCCESS] Screenshot refreshed
[[10:12:08]] [INFO] Refreshing screenshot...
[[10:12:08]] [INFO] V59u3l1wkM=pass
[[10:12:05]] [INFO] V59u3l1wkM=running
[[10:12:05]] [INFO] Executing action 299/643: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[10:12:04]] [SUCCESS] Screenshot refreshed successfully
[[10:12:04]] [SUCCESS] Screenshot refreshed successfully
[[10:12:04]] [SUCCESS] Screenshot refreshed
[[10:12:04]] [INFO] Refreshing screenshot...
[[10:12:04]] [INFO] sl3Wk1gK8X=pass
[[10:11:58]] [SUCCESS] Screenshot refreshed successfully
[[10:11:58]] [SUCCESS] Screenshot refreshed successfully
[[10:11:58]] [INFO] sl3Wk1gK8X=running
[[10:11:58]] [INFO] Executing action 298/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:11:58]] [SUCCESS] Screenshot refreshed
[[10:11:58]] [INFO] Refreshing screenshot...
[[10:11:58]] [SUCCESS] Screenshot refreshed successfully
[[10:11:58]] [SUCCESS] Screenshot refreshed successfully
[[10:11:57]] [SUCCESS] Screenshot refreshed
[[10:11:57]] [INFO] Refreshing screenshot...
[[10:11:53]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:11:53]] [SUCCESS] Screenshot refreshed successfully
[[10:11:53]] [SUCCESS] Screenshot refreshed successfully
[[10:11:52]] [SUCCESS] Screenshot refreshed
[[10:11:52]] [INFO] Refreshing screenshot...
[[10:11:49]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:11:48]] [SUCCESS] Screenshot refreshed successfully
[[10:11:48]] [SUCCESS] Screenshot refreshed successfully
[[10:11:48]] [SUCCESS] Screenshot refreshed
[[10:11:48]] [INFO] Refreshing screenshot...
[[10:11:44]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:11:43]] [SUCCESS] Screenshot refreshed successfully
[[10:11:43]] [SUCCESS] Screenshot refreshed successfully
[[10:11:43]] [SUCCESS] Screenshot refreshed
[[10:11:43]] [INFO] Refreshing screenshot...
[[10:11:39]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:11:39]] [SUCCESS] Screenshot refreshed successfully
[[10:11:39]] [SUCCESS] Screenshot refreshed successfully
[[10:11:39]] [SUCCESS] Screenshot refreshed
[[10:11:39]] [INFO] Refreshing screenshot...
[[10:11:33]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:11:33]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[10:11:33]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[10:11:33]] [INFO] vjK6GqOF3r=running
[[10:11:33]] [INFO] Executing action 297/643: Execute Test Case: Kmart-Signin (8 steps)
[[10:11:33]] [SUCCESS] Screenshot refreshed successfully
[[10:11:33]] [SUCCESS] Screenshot refreshed successfully
[[10:11:33]] [SUCCESS] Screenshot refreshed
[[10:11:33]] [INFO] Refreshing screenshot...
[[10:11:33]] [INFO] ly2oT3zqmf=pass
[[10:11:30]] [INFO] ly2oT3zqmf=running
[[10:11:30]] [INFO] Executing action 296/643: iOS Function: alert_accept
[[10:11:30]] [SUCCESS] Screenshot refreshed successfully
[[10:11:30]] [SUCCESS] Screenshot refreshed successfully
[[10:11:30]] [SUCCESS] Screenshot refreshed
[[10:11:30]] [INFO] Refreshing screenshot...
[[10:11:30]] [INFO] xAPeBnVHrT=pass
[[10:11:23]] [INFO] xAPeBnVHrT=running
[[10:11:23]] [INFO] Executing action 295/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:11:22]] [SUCCESS] Screenshot refreshed successfully
[[10:11:22]] [SUCCESS] Screenshot refreshed successfully
[[10:11:22]] [SUCCESS] Screenshot refreshed
[[10:11:22]] [INFO] Refreshing screenshot...
[[10:11:22]] [INFO] u6bRYZZFAv=pass
[[10:11:16]] [INFO] u6bRYZZFAv=running
[[10:11:16]] [INFO] Executing action 294/643: Wait for 5 ms
[[10:11:16]] [SUCCESS] Screenshot refreshed successfully
[[10:11:16]] [SUCCESS] Screenshot refreshed successfully
[[10:11:15]] [SUCCESS] Screenshot refreshed
[[10:11:15]] [INFO] Refreshing screenshot...
[[10:11:15]] [INFO] pjFNt3w5Fr=pass
[[10:11:02]] [SUCCESS] Screenshot refreshed successfully
[[10:11:02]] [SUCCESS] Screenshot refreshed successfully
[[10:11:02]] [INFO] pjFNt3w5Fr=running
[[10:11:02]] [INFO] Executing action 293/643: Restart app: env[appid]
[[10:11:02]] [SUCCESS] Screenshot refreshed
[[10:11:02]] [INFO] Refreshing screenshot...
[[10:11:01]] [SUCCESS] Screenshot refreshed successfully
[[10:11:01]] [SUCCESS] Screenshot refreshed successfully
[[10:11:01]] [SUCCESS] Screenshot refreshed
[[10:11:01]] [INFO] Refreshing screenshot...
[[10:10:59]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:10:58]] [SUCCESS] Screenshot refreshed successfully
[[10:10:58]] [SUCCESS] Screenshot refreshed successfully
[[10:10:58]] [SUCCESS] Screenshot refreshed
[[10:10:58]] [INFO] Refreshing screenshot...
[[10:10:46]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:10:45]] [SUCCESS] Screenshot refreshed successfully
[[10:10:45]] [SUCCESS] Screenshot refreshed successfully
[[10:10:45]] [SUCCESS] Screenshot refreshed
[[10:10:45]] [INFO] Refreshing screenshot...
[[10:10:42]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:10:41]] [SUCCESS] Screenshot refreshed successfully
[[10:10:41]] [SUCCESS] Screenshot refreshed successfully
[[10:10:41]] [SUCCESS] Screenshot refreshed
[[10:10:41]] [INFO] Refreshing screenshot...
[[10:10:37]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:10:37]] [SUCCESS] Screenshot refreshed successfully
[[10:10:37]] [SUCCESS] Screenshot refreshed successfully
[[10:10:37]] [SUCCESS] Screenshot refreshed
[[10:10:37]] [INFO] Refreshing screenshot...
[[10:10:31]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:10:31]] [SUCCESS] Screenshot refreshed successfully
[[10:10:31]] [SUCCESS] Screenshot refreshed successfully
[[10:10:30]] [SUCCESS] Screenshot refreshed
[[10:10:30]] [INFO] Refreshing screenshot...
[[10:10:24]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:10:24]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:10:24]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:10:24]] [INFO] PGvsG6rpU4=running
[[10:10:24]] [INFO] Executing action 292/643: cleanupSteps action
[[10:10:24]] [SUCCESS] Screenshot refreshed successfully
[[10:10:24]] [SUCCESS] Screenshot refreshed successfully
[[10:10:24]] [SUCCESS] Screenshot refreshed
[[10:10:24]] [INFO] Refreshing screenshot...
[[10:10:24]] [INFO] LzGkAcsQyE=pass
[[10:10:21]] [INFO] LzGkAcsQyE=running
[[10:10:21]] [INFO] Executing action 291/643: Terminate app: env[appid]
[[10:10:21]] [SUCCESS] Screenshot refreshed successfully
[[10:10:21]] [SUCCESS] Screenshot refreshed successfully
[[10:10:21]] [SUCCESS] Screenshot refreshed
[[10:10:21]] [INFO] Refreshing screenshot...
[[10:10:21]] [INFO] Bdhe5AoUlM=pass
[[10:10:17]] [INFO] Bdhe5AoUlM=running
[[10:10:17]] [INFO] Executing action 290/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:10:17]] [SUCCESS] Screenshot refreshed successfully
[[10:10:17]] [SUCCESS] Screenshot refreshed successfully
[[10:10:17]] [SUCCESS] Screenshot refreshed
[[10:10:17]] [INFO] Refreshing screenshot...
[[10:10:17]] [INFO] FciJcOsMsB=pass
[[10:10:10]] [INFO] FciJcOsMsB=running
[[10:10:10]] [INFO] Executing action 289/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:10:09]] [SUCCESS] Screenshot refreshed successfully
[[10:10:09]] [SUCCESS] Screenshot refreshed successfully
[[10:10:09]] [SUCCESS] Screenshot refreshed
[[10:10:09]] [INFO] Refreshing screenshot...
[[10:10:09]] [INFO] FARWZvOj0x=pass
[[10:10:06]] [INFO] FARWZvOj0x=running
[[10:10:06]] [INFO] Executing action 288/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:10:05]] [SUCCESS] Screenshot refreshed successfully
[[10:10:05]] [SUCCESS] Screenshot refreshed successfully
[[10:10:05]] [SUCCESS] Screenshot refreshed
[[10:10:05]] [INFO] Refreshing screenshot...
[[10:10:05]] [INFO] bZCkx4U9Gk=pass
[[10:09:59]] [INFO] bZCkx4U9Gk=running
[[10:09:59]] [INFO] Executing action 287/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:09:59]] [SUCCESS] Screenshot refreshed successfully
[[10:09:59]] [SUCCESS] Screenshot refreshed successfully
[[10:09:59]] [SUCCESS] Screenshot refreshed
[[10:09:59]] [INFO] Refreshing screenshot...
[[10:09:59]] [INFO] vwFwkK6ydQ=pass
[[10:09:55]] [INFO] vwFwkK6ydQ=running
[[10:09:55]] [INFO] Executing action 286/643: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[10:09:54]] [SUCCESS] Screenshot refreshed successfully
[[10:09:54]] [SUCCESS] Screenshot refreshed successfully
[[10:09:54]] [SUCCESS] Screenshot refreshed
[[10:09:54]] [INFO] Refreshing screenshot...
[[10:09:54]] [INFO] xLGm9FefWE=pass
[[10:09:50]] [INFO] xLGm9FefWE=running
[[10:09:50]] [INFO] Executing action 285/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[10:09:50]] [SUCCESS] Screenshot refreshed successfully
[[10:09:50]] [SUCCESS] Screenshot refreshed successfully
[[10:09:50]] [SUCCESS] Screenshot refreshed
[[10:09:50]] [INFO] Refreshing screenshot...
[[10:09:50]] [INFO] UtVRXwa86e=pass
[[10:09:43]] [INFO] UtVRXwa86e=running
[[10:09:43]] [INFO] Executing action 284/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[10:09:43]] [SUCCESS] Screenshot refreshed successfully
[[10:09:43]] [SUCCESS] Screenshot refreshed successfully
[[10:09:43]] [SUCCESS] Screenshot refreshed
[[10:09:43]] [INFO] Refreshing screenshot...
[[10:09:43]] [INFO] SDtskxyVpg=pass
[[10:09:39]] [INFO] SDtskxyVpg=running
[[10:09:39]] [INFO] Executing action 283/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:09:39]] [SUCCESS] Screenshot refreshed successfully
[[10:09:39]] [SUCCESS] Screenshot refreshed successfully
[[10:09:39]] [SUCCESS] Screenshot refreshed
[[10:09:39]] [INFO] Refreshing screenshot...
[[10:09:39]] [INFO] 6HhScBaqQp=pass
[[10:09:36]] [INFO] 6HhScBaqQp=running
[[10:09:36]] [INFO] Executing action 282/643: iOS Function: alert_accept
[[10:09:36]] [SUCCESS] Screenshot refreshed successfully
[[10:09:36]] [SUCCESS] Screenshot refreshed successfully
[[10:09:36]] [SUCCESS] Screenshot refreshed
[[10:09:36]] [INFO] Refreshing screenshot...
[[10:09:36]] [INFO] quzlwPw42x=pass
[[10:09:30]] [INFO] quzlwPw42x=running
[[10:09:30]] [INFO] Executing action 281/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:09:30]] [SUCCESS] Screenshot refreshed successfully
[[10:09:30]] [SUCCESS] Screenshot refreshed successfully
[[10:09:30]] [SUCCESS] Screenshot refreshed
[[10:09:30]] [INFO] Refreshing screenshot...
[[10:09:30]] [INFO] jQYHQIvQ8l=pass
[[10:09:26]] [INFO] jQYHQIvQ8l=running
[[10:09:26]] [INFO] Executing action 280/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[10:09:26]] [SUCCESS] Screenshot refreshed successfully
[[10:09:26]] [SUCCESS] Screenshot refreshed successfully
[[10:09:25]] [SUCCESS] Screenshot refreshed
[[10:09:25]] [INFO] Refreshing screenshot...
[[10:09:25]] [INFO] ts3qyFxyMf=pass
[[10:09:22]] [INFO] ts3qyFxyMf=running
[[10:09:22]] [INFO] Executing action 279/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:09:21]] [SUCCESS] Screenshot refreshed successfully
[[10:09:21]] [SUCCESS] Screenshot refreshed successfully
[[10:09:21]] [SUCCESS] Screenshot refreshed
[[10:09:21]] [INFO] Refreshing screenshot...
[[10:09:21]] [INFO] FciJcOsMsB=pass
[[10:09:14]] [INFO] FciJcOsMsB=running
[[10:09:14]] [INFO] Executing action 278/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:09:14]] [SUCCESS] Screenshot refreshed successfully
[[10:09:14]] [SUCCESS] Screenshot refreshed successfully
[[10:09:14]] [SUCCESS] Screenshot refreshed
[[10:09:14]] [INFO] Refreshing screenshot...
[[10:09:14]] [INFO] CWkqGp5ndO=pass
[[10:09:10]] [INFO] CWkqGp5ndO=running
[[10:09:10]] [INFO] Executing action 277/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:09:10]] [SUCCESS] Screenshot refreshed successfully
[[10:09:10]] [SUCCESS] Screenshot refreshed successfully
[[10:09:09]] [SUCCESS] Screenshot refreshed
[[10:09:09]] [INFO] Refreshing screenshot...
[[10:09:09]] [INFO] KfMHchi8cx=pass
[[10:09:03]] [INFO] KfMHchi8cx=running
[[10:09:03]] [INFO] Executing action 276/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:09:02]] [SUCCESS] Screenshot refreshed successfully
[[10:09:02]] [SUCCESS] Screenshot refreshed successfully
[[10:09:02]] [SUCCESS] Screenshot refreshed
[[10:09:02]] [INFO] Refreshing screenshot...
[[10:09:02]] [INFO] zsVeGHiIgX=pass
[[10:08:59]] [INFO] zsVeGHiIgX=running
[[10:08:59]] [INFO] Executing action 275/643: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[10:08:59]] [SUCCESS] Screenshot refreshed successfully
[[10:08:59]] [SUCCESS] Screenshot refreshed successfully
[[10:08:59]] [SUCCESS] Screenshot refreshed
[[10:08:59]] [INFO] Refreshing screenshot...
[[10:08:59]] [INFO] 5nsUXQ5L7u=pass
[[10:08:56]] [INFO] 5nsUXQ5L7u=running
[[10:08:56]] [INFO] Executing action 274/643: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[10:08:56]] [SUCCESS] Screenshot refreshed successfully
[[10:08:56]] [SUCCESS] Screenshot refreshed successfully
[[10:08:55]] [SUCCESS] Screenshot refreshed
[[10:08:55]] [INFO] Refreshing screenshot...
[[10:08:55]] [INFO] iSckENpXrN=pass
[[10:08:53]] [INFO] iSckENpXrN=running
[[10:08:53]] [INFO] Executing action 273/643: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[10:08:52]] [SUCCESS] Screenshot refreshed successfully
[[10:08:52]] [SUCCESS] Screenshot refreshed successfully
[[10:08:52]] [SUCCESS] Screenshot refreshed
[[10:08:52]] [INFO] Refreshing screenshot...
[[10:08:52]] [INFO] J7BPGVnRJI=pass
[[10:08:49]] [INFO] J7BPGVnRJI=running
[[10:08:49]] [INFO] Executing action 272/643: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[10:08:49]] [SUCCESS] Screenshot refreshed successfully
[[10:08:49]] [SUCCESS] Screenshot refreshed successfully
[[10:08:49]] [SUCCESS] Screenshot refreshed
[[10:08:49]] [INFO] Refreshing screenshot...
[[10:08:49]] [INFO] 0pwZCYAtOv=pass
[[10:08:46]] [INFO] 0pwZCYAtOv=running
[[10:08:46]] [INFO] Executing action 271/643: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[10:08:45]] [SUCCESS] Screenshot refreshed successfully
[[10:08:45]] [SUCCESS] Screenshot refreshed successfully
[[10:08:45]] [SUCCESS] Screenshot refreshed
[[10:08:45]] [INFO] Refreshing screenshot...
[[10:08:45]] [INFO] soKM0KayFJ=pass
[[10:08:42]] [INFO] soKM0KayFJ=running
[[10:08:42]] [INFO] Executing action 270/643: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[10:08:42]] [SUCCESS] Screenshot refreshed successfully
[[10:08:42]] [SUCCESS] Screenshot refreshed successfully
[[10:08:42]] [SUCCESS] Screenshot refreshed
[[10:08:42]] [INFO] Refreshing screenshot...
[[10:08:42]] [INFO] hnH3ayslCh=pass
[[10:08:39]] [INFO] hnH3ayslCh=running
[[10:08:39]] [INFO] Executing action 269/643: Tap on Text: "Passcode"
[[10:08:39]] [SUCCESS] Screenshot refreshed successfully
[[10:08:39]] [SUCCESS] Screenshot refreshed successfully
[[10:08:38]] [SUCCESS] Screenshot refreshed
[[10:08:38]] [INFO] Refreshing screenshot...
[[10:08:38]] [INFO] CzVeOTdAX9=pass
[[10:08:27]] [INFO] CzVeOTdAX9=running
[[10:08:27]] [INFO] Executing action 268/643: Wait for 10 ms
[[10:08:27]] [SUCCESS] Screenshot refreshed successfully
[[10:08:27]] [SUCCESS] Screenshot refreshed successfully
[[10:08:27]] [SUCCESS] Screenshot refreshed
[[10:08:27]] [INFO] Refreshing screenshot...
[[10:08:27]] [INFO] NL2gtj6qIu=pass
[[10:08:22]] [INFO] NL2gtj6qIu=running
[[10:08:22]] [INFO] Executing action 267/643: Tap on Text: "Apple"
[[10:08:22]] [SUCCESS] Screenshot refreshed successfully
[[10:08:22]] [SUCCESS] Screenshot refreshed successfully
[[10:08:22]] [SUCCESS] Screenshot refreshed
[[10:08:22]] [INFO] Refreshing screenshot...
[[10:08:22]] [INFO] VsSlyhXuVD=pass
[[10:08:17]] [INFO] VsSlyhXuVD=running
[[10:08:17]] [INFO] Executing action 266/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:08:17]] [SUCCESS] Screenshot refreshed successfully
[[10:08:17]] [SUCCESS] Screenshot refreshed successfully
[[10:08:17]] [SUCCESS] Screenshot refreshed
[[10:08:17]] [INFO] Refreshing screenshot...
[[10:08:17]] [INFO] CJ88OgjKXp=pass
[[10:08:14]] [INFO] CJ88OgjKXp=running
[[10:08:14]] [INFO] Executing action 265/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:08:13]] [SUCCESS] Screenshot refreshed successfully
[[10:08:13]] [SUCCESS] Screenshot refreshed successfully
[[10:08:13]] [SUCCESS] Screenshot refreshed
[[10:08:13]] [INFO] Refreshing screenshot...
[[10:08:13]] [INFO] AYiwFSLTBD=pass
[[10:08:11]] [INFO] AYiwFSLTBD=running
[[10:08:11]] [INFO] Executing action 264/643: iOS Function: alert_accept
[[10:08:10]] [SUCCESS] Screenshot refreshed successfully
[[10:08:10]] [SUCCESS] Screenshot refreshed successfully
[[10:08:10]] [SUCCESS] Screenshot refreshed
[[10:08:10]] [INFO] Refreshing screenshot...
[[10:08:10]] [INFO] HJzOYZNnGr=pass
[[10:08:04]] [INFO] HJzOYZNnGr=running
[[10:08:04]] [INFO] Executing action 263/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:08:04]] [SUCCESS] Screenshot refreshed successfully
[[10:08:04]] [SUCCESS] Screenshot refreshed successfully
[[10:08:04]] [SUCCESS] Screenshot refreshed
[[10:08:04]] [INFO] Refreshing screenshot...
[[10:08:04]] [INFO] taf19mtrUT=pass
[[10:08:00]] [INFO] taf19mtrUT=running
[[10:08:00]] [INFO] Executing action 262/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[10:08:00]] [SUCCESS] Screenshot refreshed successfully
[[10:08:00]] [SUCCESS] Screenshot refreshed successfully
[[10:08:00]] [SUCCESS] Screenshot refreshed
[[10:08:00]] [INFO] Refreshing screenshot...
[[10:08:00]] [INFO] oiPcknTonJ=pass
[[10:07:56]] [INFO] oiPcknTonJ=running
[[10:07:56]] [INFO] Executing action 261/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:07:56]] [SUCCESS] Screenshot refreshed successfully
[[10:07:56]] [SUCCESS] Screenshot refreshed successfully
[[10:07:55]] [SUCCESS] Screenshot refreshed
[[10:07:55]] [INFO] Refreshing screenshot...
[[10:07:55]] [INFO] FciJcOsMsB=pass
[[10:07:50]] [INFO] FciJcOsMsB=running
[[10:07:50]] [INFO] Executing action 260/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:07:50]] [SUCCESS] Screenshot refreshed successfully
[[10:07:50]] [SUCCESS] Screenshot refreshed successfully
[[10:07:49]] [SUCCESS] Screenshot refreshed
[[10:07:49]] [INFO] Refreshing screenshot...
[[10:07:49]] [INFO] 2qOXZcEmK8=pass
[[10:07:46]] [INFO] 2qOXZcEmK8=running
[[10:07:46]] [INFO] Executing action 259/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:07:45]] [SUCCESS] Screenshot refreshed successfully
[[10:07:45]] [SUCCESS] Screenshot refreshed successfully
[[10:07:45]] [SUCCESS] Screenshot refreshed
[[10:07:45]] [INFO] Refreshing screenshot...
[[10:07:45]] [INFO] M6HdLxu76S=pass
[[10:07:41]] [INFO] M6HdLxu76S=running
[[10:07:41]] [INFO] Executing action 258/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:07:41]] [SUCCESS] Screenshot refreshed successfully
[[10:07:41]] [SUCCESS] Screenshot refreshed successfully
[[10:07:40]] [SUCCESS] Screenshot refreshed
[[10:07:40]] [INFO] Refreshing screenshot...
[[10:07:40]] [INFO] 6wYIn0igez=pass
[[10:07:36]] [INFO] 6wYIn0igez=running
[[10:07:36]] [INFO] Executing action 257/643: iOS Function: text - Text: "Wonderbaby@6"
[[10:07:36]] [SUCCESS] Screenshot refreshed successfully
[[10:07:36]] [SUCCESS] Screenshot refreshed successfully
[[10:07:36]] [SUCCESS] Screenshot refreshed
[[10:07:36]] [INFO] Refreshing screenshot...
[[10:07:36]] [INFO] DaVBARRwft=pass
[[10:07:31]] [INFO] DaVBARRwft=running
[[10:07:31]] [INFO] Executing action 256/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[10:07:31]] [SUCCESS] Screenshot refreshed successfully
[[10:07:31]] [SUCCESS] Screenshot refreshed successfully
[[10:07:31]] [SUCCESS] Screenshot refreshed
[[10:07:31]] [INFO] Refreshing screenshot...
[[10:07:31]] [INFO] 2fmkjdQPtJ=pass
[[10:07:27]] [INFO] 2fmkjdQPtJ=running
[[10:07:27]] [INFO] Executing action 255/643: Tap on image: captha-chkbox-op-ios.png
[[10:07:27]] [SUCCESS] Screenshot refreshed successfully
[[10:07:27]] [SUCCESS] Screenshot refreshed successfully
[[10:07:27]] [SUCCESS] Screenshot refreshed
[[10:07:27]] [INFO] Refreshing screenshot...
[[10:07:27]] [INFO] Gb2Do6AtSN=pass
[[10:07:22]] [INFO] Gb2Do6AtSN=running
[[10:07:22]] [INFO] Executing action 254/643: iOS Function: text - Text: "<EMAIL>"
[[10:07:22]] [SUCCESS] Screenshot refreshed successfully
[[10:07:22]] [SUCCESS] Screenshot refreshed successfully
[[10:07:22]] [SUCCESS] Screenshot refreshed
[[10:07:22]] [INFO] Refreshing screenshot...
[[10:07:22]] [INFO] y8ZMTkG38M=pass
[[10:07:18]] [INFO] y8ZMTkG38M=running
[[10:07:18]] [INFO] Executing action 253/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[10:07:17]] [SUCCESS] Screenshot refreshed successfully
[[10:07:17]] [SUCCESS] Screenshot refreshed successfully
[[10:07:17]] [SUCCESS] Screenshot refreshed
[[10:07:17]] [INFO] Refreshing screenshot...
[[10:07:17]] [INFO] UUhQjmzfO2=pass
[[10:07:13]] [INFO] UUhQjmzfO2=running
[[10:07:13]] [INFO] Executing action 252/643: Tap on Text: "OnePass"
[[10:07:12]] [SUCCESS] Screenshot refreshed successfully
[[10:07:12]] [SUCCESS] Screenshot refreshed successfully
[[10:07:12]] [SUCCESS] Screenshot refreshed
[[10:07:12]] [INFO] Refreshing screenshot...
[[10:07:12]] [INFO] NCyuT8W5Xz=pass
[[10:07:09]] [INFO] NCyuT8W5Xz=running
[[10:07:09]] [INFO] Executing action 251/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:07:09]] [SUCCESS] Screenshot refreshed successfully
[[10:07:09]] [SUCCESS] Screenshot refreshed successfully
[[10:07:08]] [SUCCESS] Screenshot refreshed
[[10:07:08]] [INFO] Refreshing screenshot...
[[10:07:08]] [INFO] 2kwu2VBmuZ=pass
[[10:07:06]] [INFO] 2kwu2VBmuZ=running
[[10:07:06]] [INFO] Executing action 250/643: iOS Function: alert_accept
[[10:07:06]] [SUCCESS] Screenshot refreshed successfully
[[10:07:06]] [SUCCESS] Screenshot refreshed successfully
[[10:07:05]] [SUCCESS] Screenshot refreshed
[[10:07:05]] [INFO] Refreshing screenshot...
[[10:07:05]] [INFO] cJDpd7aK3d=pass
[[10:07:00]] [INFO] cJDpd7aK3d=running
[[10:07:00]] [INFO] Executing action 249/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:06:59]] [SUCCESS] Screenshot refreshed successfully
[[10:06:59]] [SUCCESS] Screenshot refreshed successfully
[[10:06:59]] [SUCCESS] Screenshot refreshed
[[10:06:59]] [INFO] Refreshing screenshot...
[[10:06:59]] [INFO] FlEukNkjlS=pass
[[10:06:56]] [INFO] FlEukNkjlS=running
[[10:06:56]] [INFO] Executing action 248/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[10:06:55]] [SUCCESS] Screenshot refreshed successfully
[[10:06:55]] [SUCCESS] Screenshot refreshed successfully
[[10:06:55]] [SUCCESS] Screenshot refreshed
[[10:06:55]] [INFO] Refreshing screenshot...
[[10:06:55]] [INFO] LlRfimKPrn=pass
[[10:06:51]] [INFO] LlRfimKPrn=running
[[10:06:51]] [INFO] Executing action 247/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:06:51]] [SUCCESS] Screenshot refreshed successfully
[[10:06:51]] [SUCCESS] Screenshot refreshed successfully
[[10:06:51]] [SUCCESS] Screenshot refreshed
[[10:06:51]] [INFO] Refreshing screenshot...
[[10:06:51]] [INFO] FciJcOsMsB=pass
[[10:06:44]] [INFO] FciJcOsMsB=running
[[10:06:44]] [INFO] Executing action 246/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:06:44]] [SUCCESS] Screenshot refreshed successfully
[[10:06:44]] [SUCCESS] Screenshot refreshed successfully
[[10:06:44]] [SUCCESS] Screenshot refreshed
[[10:06:44]] [INFO] Refreshing screenshot...
[[10:06:44]] [INFO] 08NzsvhQXK=pass
[[10:06:40]] [INFO] 08NzsvhQXK=running
[[10:06:40]] [INFO] Executing action 245/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:06:40]] [SUCCESS] Screenshot refreshed successfully
[[10:06:40]] [SUCCESS] Screenshot refreshed successfully
[[10:06:39]] [SUCCESS] Screenshot refreshed
[[10:06:39]] [INFO] Refreshing screenshot...
[[10:06:39]] [INFO] IsGWxLFpIn=pass
[[10:06:35]] [INFO] IsGWxLFpIn=running
[[10:06:35]] [INFO] Executing action 244/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:06:35]] [SUCCESS] Screenshot refreshed successfully
[[10:06:35]] [SUCCESS] Screenshot refreshed successfully
[[10:06:35]] [SUCCESS] Screenshot refreshed
[[10:06:35]] [INFO] Refreshing screenshot...
[[10:06:35]] [INFO] dyECdbRifp=pass
[[10:06:30]] [INFO] dyECdbRifp=running
[[10:06:30]] [INFO] Executing action 243/643: iOS Function: text - Text: "Wonderbaby@5"
[[10:06:30]] [SUCCESS] Screenshot refreshed successfully
[[10:06:30]] [SUCCESS] Screenshot refreshed successfully
[[10:06:30]] [SUCCESS] Screenshot refreshed
[[10:06:30]] [INFO] Refreshing screenshot...
[[10:06:30]] [INFO] I5bRbYY1hD=pass
[[10:06:26]] [INFO] I5bRbYY1hD=running
[[10:06:26]] [INFO] Executing action 242/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:06:25]] [SUCCESS] Screenshot refreshed successfully
[[10:06:25]] [SUCCESS] Screenshot refreshed successfully
[[10:06:25]] [SUCCESS] Screenshot refreshed
[[10:06:25]] [INFO] Refreshing screenshot...
[[10:06:25]] [INFO] WMl5g82CCq=pass
[[10:06:21]] [INFO] WMl5g82CCq=running
[[10:06:21]] [INFO] Executing action 241/643: iOS Function: text - Text: "<EMAIL>"
[[10:06:20]] [SUCCESS] Screenshot refreshed successfully
[[10:06:20]] [SUCCESS] Screenshot refreshed successfully
[[10:06:20]] [SUCCESS] Screenshot refreshed
[[10:06:20]] [INFO] Refreshing screenshot...
[[10:06:20]] [INFO] 8OsQmoVYqW=pass
[[10:06:16]] [INFO] 8OsQmoVYqW=running
[[10:06:16]] [INFO] Executing action 240/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:06:16]] [SUCCESS] Screenshot refreshed successfully
[[10:06:16]] [SUCCESS] Screenshot refreshed successfully
[[10:06:16]] [SUCCESS] Screenshot refreshed
[[10:06:16]] [INFO] Refreshing screenshot...
[[10:06:16]] [INFO] ImienLpJEN=pass
[[10:06:12]] [INFO] ImienLpJEN=running
[[10:06:12]] [INFO] Executing action 239/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:06:12]] [SUCCESS] Screenshot refreshed successfully
[[10:06:12]] [SUCCESS] Screenshot refreshed successfully
[[10:06:12]] [SUCCESS] Screenshot refreshed
[[10:06:12]] [INFO] Refreshing screenshot...
[[10:06:12]] [INFO] q4hPXCBtx4=pass
[[10:06:09]] [INFO] q4hPXCBtx4=running
[[10:06:09]] [INFO] Executing action 238/643: iOS Function: alert_accept
[[10:06:09]] [SUCCESS] Screenshot refreshed successfully
[[10:06:09]] [SUCCESS] Screenshot refreshed successfully
[[10:06:09]] [SUCCESS] Screenshot refreshed
[[10:06:09]] [INFO] Refreshing screenshot...
[[10:06:09]] [INFO] 2cTZvK1psn=pass
[[10:06:02]] [INFO] 2cTZvK1psn=running
[[10:06:02]] [INFO] Executing action 237/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:06:02]] [SUCCESS] Screenshot refreshed successfully
[[10:06:02]] [SUCCESS] Screenshot refreshed successfully
[[10:06:02]] [SUCCESS] Screenshot refreshed
[[10:06:02]] [INFO] Refreshing screenshot...
[[10:06:02]] [INFO] Vxt7QOYeDD=pass
[[10:05:48]] [SUCCESS] Screenshot refreshed successfully
[[10:05:48]] [SUCCESS] Screenshot refreshed successfully
[[10:05:48]] [INFO] Vxt7QOYeDD=running
[[10:05:48]] [INFO] Executing action 236/643: Restart app: env[appid]
[[10:05:48]] [SUCCESS] Screenshot refreshed
[[10:05:48]] [INFO] Refreshing screenshot...
[[10:05:48]] [SUCCESS] Screenshot refreshed successfully
[[10:05:48]] [SUCCESS] Screenshot refreshed successfully
[[10:05:47]] [SUCCESS] Screenshot refreshed
[[10:05:47]] [INFO] Refreshing screenshot...
[[10:05:45]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:05:45]] [SUCCESS] Screenshot refreshed successfully
[[10:05:45]] [SUCCESS] Screenshot refreshed successfully
[[10:05:45]] [SUCCESS] Screenshot refreshed
[[10:05:45]] [INFO] Refreshing screenshot...
[[10:05:40]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:05:39]] [SUCCESS] Screenshot refreshed successfully
[[10:05:39]] [SUCCESS] Screenshot refreshed successfully
[[10:05:39]] [SUCCESS] Screenshot refreshed
[[10:05:39]] [INFO] Refreshing screenshot...
[[10:05:36]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:05:35]] [SUCCESS] Screenshot refreshed successfully
[[10:05:35]] [SUCCESS] Screenshot refreshed successfully
[[10:05:35]] [SUCCESS] Screenshot refreshed
[[10:05:35]] [INFO] Refreshing screenshot...
[[10:05:31]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:05:31]] [SUCCESS] Screenshot refreshed successfully
[[10:05:31]] [SUCCESS] Screenshot refreshed successfully
[[10:05:31]] [SUCCESS] Screenshot refreshed
[[10:05:31]] [INFO] Refreshing screenshot...
[[10:05:24]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:05:24]] [SUCCESS] Screenshot refreshed successfully
[[10:05:24]] [SUCCESS] Screenshot refreshed successfully
[[10:05:24]] [SUCCESS] Screenshot refreshed
[[10:05:24]] [INFO] Refreshing screenshot...
[[10:05:17]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:05:17]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:05:17]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:05:17]] [INFO] DYWpUY7xB6=running
[[10:05:17]] [INFO] Executing action 235/643: cleanupSteps action
[[10:05:17]] [INFO] Skipping remaining steps in failed test case (moving from action 187 to 234), but preserving cleanup steps
[[10:05:17]] [INFO] rkL0oz4kiL=fail
[[10:05:17]] [ERROR] Action 187 failed: Element not found or not tappable: accessibility_id='txtHomeAccountCtaSignIn'
[[10:05:04]] [INFO] rkL0oz4kiL=running
[[10:05:04]] [INFO] Executing action 187/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:05:04]] [SUCCESS] Screenshot refreshed successfully
[[10:05:04]] [SUCCESS] Screenshot refreshed successfully
[[10:05:03]] [SUCCESS] Screenshot refreshed
[[10:05:03]] [INFO] Refreshing screenshot...
[[10:05:03]] [INFO] HotUJOd6oB=pass
[[10:04:59]] [INFO] HotUJOd6oB=running
[[10:04:59]] [INFO] Executing action 186/643: Restart app: env[appid]
[[10:04:57]] [INFO] === RETRYING TEST CASE: WishList_20250510110236.json (Attempt 3 of 3) ===
[[10:04:57]] [INFO] rkL0oz4kiL=fail
[[10:04:57]] [ERROR] Action 187 failed: Element not found or not tappable: accessibility_id='txtHomeAccountCtaSignIn'
[[10:04:43]] [INFO] rkL0oz4kiL=running
[[10:04:43]] [INFO] Executing action 187/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:04:43]] [SUCCESS] Screenshot refreshed successfully
[[10:04:43]] [SUCCESS] Screenshot refreshed successfully
[[10:04:42]] [SUCCESS] Screenshot refreshed
[[10:04:42]] [INFO] Refreshing screenshot...
[[10:04:42]] [INFO] HotUJOd6oB=pass
[[10:04:38]] [INFO] HotUJOd6oB=running
[[10:04:38]] [INFO] Executing action 186/643: Restart app: env[appid]
[[10:04:36]] [INFO] === RETRYING TEST CASE: WishList_20250510110236.json (Attempt 2 of 3) ===
[[10:04:36]] [INFO] eLxHVWKeDQ=fail
[[10:04:36]] [ERROR] Action 202 failed: Element not found or not tappable: xpath='(//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]'
[[10:04:22]] [INFO] eLxHVWKeDQ=running
[[10:04:22]] [INFO] Executing action 202/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[10:04:22]] [SUCCESS] Screenshot refreshed successfully
[[10:04:22]] [SUCCESS] Screenshot refreshed successfully
[[10:04:21]] [SUCCESS] Screenshot refreshed
[[10:04:21]] [INFO] Refreshing screenshot...
[[10:04:21]] [INFO] WbxRVpWtjw=pass
[[10:04:17]] [INFO] WbxRVpWtjw=running
[[10:04:17]] [INFO] Executing action 201/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[10:04:17]] [SUCCESS] Screenshot refreshed successfully
[[10:04:17]] [SUCCESS] Screenshot refreshed successfully
[[10:04:17]] [SUCCESS] Screenshot refreshed
[[10:04:17]] [INFO] Refreshing screenshot...
[[10:04:17]] [INFO] H3IAmq3r3i=pass
[[10:04:10]] [INFO] H3IAmq3r3i=running
[[10:04:10]] [INFO] Executing action 200/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[10:04:10]] [SUCCESS] Screenshot refreshed successfully
[[10:04:10]] [SUCCESS] Screenshot refreshed successfully
[[10:04:09]] [SUCCESS] Screenshot refreshed
[[10:04:09]] [INFO] Refreshing screenshot...
[[10:04:09]] [INFO] ITHvSyXXmu=pass
[[10:04:06]] [INFO] ITHvSyXXmu=running
[[10:04:06]] [INFO] Executing action 199/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[10:04:06]] [SUCCESS] Screenshot refreshed successfully
[[10:04:06]] [SUCCESS] Screenshot refreshed successfully
[[10:04:05]] [SUCCESS] Screenshot refreshed
[[10:04:05]] [INFO] Refreshing screenshot...
[[10:04:05]] [INFO] eLxHVWKeDQ=pass
[[10:04:01]] [INFO] eLxHVWKeDQ=running
[[10:04:01]] [INFO] Executing action 198/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[10:04:01]] [SUCCESS] Screenshot refreshed successfully
[[10:04:01]] [SUCCESS] Screenshot refreshed successfully
[[10:04:01]] [SUCCESS] Screenshot refreshed
[[10:04:01]] [INFO] Refreshing screenshot...
[[10:04:01]] [INFO] nAB6Q8LAdv=pass
[[10:03:57]] [INFO] nAB6Q8LAdv=running
[[10:03:57]] [INFO] Executing action 197/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[10:03:57]] [SUCCESS] Screenshot refreshed successfully
[[10:03:57]] [SUCCESS] Screenshot refreshed successfully
[[10:03:57]] [SUCCESS] Screenshot refreshed
[[10:03:57]] [INFO] Refreshing screenshot...
[[10:03:57]] [INFO] sc2KH9bG6H=pass
[[10:03:53]] [INFO] sc2KH9bG6H=running
[[10:03:53]] [INFO] Executing action 196/643: iOS Function: text - Text: "Uno card"
[[10:03:52]] [SUCCESS] Screenshot refreshed successfully
[[10:03:52]] [SUCCESS] Screenshot refreshed successfully
[[10:03:52]] [SUCCESS] Screenshot refreshed
[[10:03:52]] [INFO] Refreshing screenshot...
[[10:03:52]] [INFO] rqLJpAP0mA=pass
[[10:03:47]] [INFO] rqLJpAP0mA=running
[[10:03:47]] [INFO] Executing action 195/643: Tap on Text: "Find"
[[10:03:47]] [SUCCESS] Screenshot refreshed successfully
[[10:03:47]] [SUCCESS] Screenshot refreshed successfully
[[10:03:47]] [SUCCESS] Screenshot refreshed
[[10:03:47]] [INFO] Refreshing screenshot...
[[10:03:47]] [INFO] yiKyF5FJwN=pass
[[10:03:44]] [INFO] yiKyF5FJwN=running
[[10:03:44]] [INFO] Executing action 194/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[10:03:44]] [SUCCESS] Screenshot refreshed successfully
[[10:03:44]] [SUCCESS] Screenshot refreshed successfully
[[10:03:43]] [SUCCESS] Screenshot refreshed
[[10:03:43]] [INFO] Refreshing screenshot...
[[10:03:43]] [INFO] sTtseHOKfa=pass
[[10:03:39]] [INFO] sTtseHOKfa=running
[[10:03:39]] [INFO] Executing action 193/643: iOS Function: text - Text: "Wonderbaby@5"
[[10:03:39]] [SUCCESS] Screenshot refreshed successfully
[[10:03:39]] [SUCCESS] Screenshot refreshed successfully
[[10:03:38]] [SUCCESS] Screenshot refreshed
[[10:03:38]] [INFO] Refreshing screenshot...
[[10:03:38]] [INFO] T3MmUw30SF=pass
[[10:03:34]] [INFO] T3MmUw30SF=running
[[10:03:34]] [INFO] Executing action 192/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:03:34]] [SUCCESS] Screenshot refreshed successfully
[[10:03:34]] [SUCCESS] Screenshot refreshed successfully
[[10:03:34]] [SUCCESS] Screenshot refreshed
[[10:03:34]] [INFO] Refreshing screenshot...
[[10:03:34]] [INFO] PPIBJbaXNx=pass
[[10:03:29]] [INFO] PPIBJbaXNx=running
[[10:03:29]] [INFO] Executing action 191/643: iOS Function: text - Text: "<EMAIL>"
[[10:03:29]] [SUCCESS] Screenshot refreshed successfully
[[10:03:29]] [SUCCESS] Screenshot refreshed successfully
[[10:03:29]] [SUCCESS] Screenshot refreshed
[[10:03:29]] [INFO] Refreshing screenshot...
[[10:03:29]] [INFO] LDkFLWks00=pass
[[10:03:25]] [INFO] LDkFLWks00=running
[[10:03:25]] [INFO] Executing action 190/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:03:25]] [SUCCESS] Screenshot refreshed successfully
[[10:03:25]] [SUCCESS] Screenshot refreshed successfully
[[10:03:24]] [SUCCESS] Screenshot refreshed
[[10:03:24]] [INFO] Refreshing screenshot...
[[10:03:24]] [INFO] 3caMBvQX7k=pass
[[10:03:21]] [INFO] 3caMBvQX7k=running
[[10:03:21]] [INFO] Executing action 189/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:03:21]] [SUCCESS] Screenshot refreshed successfully
[[10:03:21]] [SUCCESS] Screenshot refreshed successfully
[[10:03:21]] [SUCCESS] Screenshot refreshed
[[10:03:21]] [INFO] Refreshing screenshot...
[[10:03:21]] [INFO] yUJyVO5Wev=pass
[[10:03:18]] [INFO] yUJyVO5Wev=running
[[10:03:18]] [INFO] Executing action 188/643: iOS Function: alert_accept
[[10:03:18]] [SUCCESS] Screenshot refreshed successfully
[[10:03:18]] [SUCCESS] Screenshot refreshed successfully
[[10:03:17]] [SUCCESS] Screenshot refreshed
[[10:03:17]] [INFO] Refreshing screenshot...
[[10:03:17]] [INFO] rkL0oz4kiL=pass
[[10:03:11]] [INFO] rkL0oz4kiL=running
[[10:03:11]] [INFO] Executing action 187/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[10:03:11]] [SUCCESS] Screenshot refreshed successfully
[[10:03:11]] [SUCCESS] Screenshot refreshed successfully
[[10:03:10]] [SUCCESS] Screenshot refreshed
[[10:03:10]] [INFO] Refreshing screenshot...
[[10:03:10]] [INFO] HotUJOd6oB=pass
[[10:02:57]] [SUCCESS] Screenshot refreshed successfully
[[10:02:57]] [SUCCESS] Screenshot refreshed successfully
[[10:02:57]] [INFO] HotUJOd6oB=running
[[10:02:57]] [INFO] Executing action 186/643: Restart app: env[appid]
[[10:02:57]] [SUCCESS] Screenshot refreshed
[[10:02:57]] [INFO] Refreshing screenshot...
[[10:02:56]] [SUCCESS] Screenshot refreshed successfully
[[10:02:56]] [SUCCESS] Screenshot refreshed successfully
[[10:02:56]] [SUCCESS] Screenshot refreshed
[[10:02:56]] [INFO] Refreshing screenshot...
[[10:02:54]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[10:02:53]] [SUCCESS] Screenshot refreshed successfully
[[10:02:53]] [SUCCESS] Screenshot refreshed successfully
[[10:02:53]] [SUCCESS] Screenshot refreshed
[[10:02:53]] [INFO] Refreshing screenshot...
[[10:02:41]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[10:02:40]] [SUCCESS] Screenshot refreshed successfully
[[10:02:40]] [SUCCESS] Screenshot refreshed successfully
[[10:02:40]] [SUCCESS] Screenshot refreshed
[[10:02:40]] [INFO] Refreshing screenshot...
[[10:02:37]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[10:02:36]] [SUCCESS] Screenshot refreshed successfully
[[10:02:36]] [SUCCESS] Screenshot refreshed successfully
[[10:02:36]] [SUCCESS] Screenshot refreshed
[[10:02:36]] [INFO] Refreshing screenshot...
[[10:02:32]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:02:32]] [SUCCESS] Screenshot refreshed successfully
[[10:02:32]] [SUCCESS] Screenshot refreshed successfully
[[10:02:32]] [SUCCESS] Screenshot refreshed
[[10:02:32]] [INFO] Refreshing screenshot...
[[10:02:25]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[10:02:25]] [SUCCESS] Screenshot refreshed successfully
[[10:02:25]] [SUCCESS] Screenshot refreshed successfully
[[10:02:25]] [SUCCESS] Screenshot refreshed
[[10:02:25]] [INFO] Refreshing screenshot...
[[10:02:18]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[10:02:18]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[10:02:18]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[10:02:18]] [INFO] IR7wnjW7C8=running
[[10:02:18]] [INFO] Executing action 185/643: cleanupSteps action
[[10:02:18]] [SUCCESS] Screenshot refreshed successfully
[[10:02:18]] [SUCCESS] Screenshot refreshed successfully
[[10:02:18]] [SUCCESS] Screenshot refreshed
[[10:02:18]] [INFO] Refreshing screenshot...
[[10:02:18]] [INFO] 7WYExJTqjp=pass
[[10:02:14]] [INFO] 7WYExJTqjp=running
[[10:02:14]] [INFO] Executing action 184/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[10:02:14]] [SUCCESS] Screenshot refreshed successfully
[[10:02:14]] [SUCCESS] Screenshot refreshed successfully
[[10:02:14]] [SUCCESS] Screenshot refreshed
[[10:02:14]] [INFO] Refreshing screenshot...
[[10:02:14]] [INFO] 4WfPFN961S=pass
[[10:02:07]] [INFO] 4WfPFN961S=running
[[10:02:07]] [INFO] Executing action 183/643: Swipe from (50%, 70%) to (50%, 30%)
[[10:02:07]] [SUCCESS] Screenshot refreshed successfully
[[10:02:07]] [SUCCESS] Screenshot refreshed successfully
[[10:02:06]] [SUCCESS] Screenshot refreshed
[[10:02:06]] [INFO] Refreshing screenshot...
[[10:02:06]] [INFO] NurQsFoMkE=pass
[[10:02:02]] [INFO] NurQsFoMkE=running
[[10:02:02]] [INFO] Executing action 182/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[10:02:02]] [SUCCESS] Screenshot refreshed successfully
[[10:02:02]] [SUCCESS] Screenshot refreshed successfully
[[10:02:02]] [SUCCESS] Screenshot refreshed
[[10:02:02]] [INFO] Refreshing screenshot...
[[10:02:02]] [INFO] CkfAScJNq8=pass
[[10:01:58]] [INFO] CkfAScJNq8=running
[[10:01:58]] [INFO] Executing action 181/643: Tap on image: env[closebtnimage]
[[10:01:58]] [SUCCESS] Screenshot refreshed successfully
[[10:01:58]] [SUCCESS] Screenshot refreshed successfully
[[10:01:58]] [SUCCESS] Screenshot refreshed
[[10:01:58]] [INFO] Refreshing screenshot...
[[10:01:58]] [INFO] 1NWfFsDiTQ=pass
[[10:01:43]] [INFO] 1NWfFsDiTQ=running
[[10:01:43]] [INFO] Executing action 180/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[10:01:42]] [SUCCESS] Screenshot refreshed successfully
[[10:01:42]] [SUCCESS] Screenshot refreshed successfully
[[10:01:42]] [SUCCESS] Screenshot refreshed
[[10:01:42]] [INFO] Refreshing screenshot...
[[10:01:42]] [INFO] tufIibCj03=pass
[[10:01:39]] [INFO] tufIibCj03=running
[[10:01:39]] [INFO] Executing action 179/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[10:01:38]] [SUCCESS] Screenshot refreshed successfully
[[10:01:38]] [SUCCESS] Screenshot refreshed successfully
[[10:01:38]] [SUCCESS] Screenshot refreshed
[[10:01:38]] [INFO] Refreshing screenshot...
[[10:01:38]] [INFO] uNbKV4slh0=pass
[[10:01:26]] [SUCCESS] Screenshot refreshed successfully
[[10:01:26]] [SUCCESS] Screenshot refreshed successfully
[[10:01:26]] [INFO] uNbKV4slh0=running
[[10:01:26]] [INFO] Executing action 178/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[10:01:26]] [SUCCESS] Screenshot refreshed
[[10:01:26]] [INFO] Refreshing screenshot...
[[10:01:26]] [SUCCESS] Screenshot refreshed successfully
[[10:01:26]] [SUCCESS] Screenshot refreshed successfully
[[10:01:26]] [SUCCESS] Screenshot refreshed
[[10:01:26]] [INFO] Refreshing screenshot...
[[10:01:21]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[10:01:21]] [SUCCESS] Screenshot refreshed successfully
[[10:01:21]] [SUCCESS] Screenshot refreshed successfully
[[10:01:21]] [SUCCESS] Screenshot refreshed
[[10:01:21]] [INFO] Refreshing screenshot...
[[10:01:17]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[10:01:17]] [SUCCESS] Screenshot refreshed successfully
[[10:01:17]] [SUCCESS] Screenshot refreshed successfully
[[10:01:16]] [SUCCESS] Screenshot refreshed
[[10:01:16]] [INFO] Refreshing screenshot...
[[10:01:12]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[10:01:12]] [SUCCESS] Screenshot refreshed successfully
[[10:01:12]] [SUCCESS] Screenshot refreshed successfully
[[10:01:11]] [SUCCESS] Screenshot refreshed
[[10:01:11]] [INFO] Refreshing screenshot...
[[10:01:07]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[10:01:07]] [SUCCESS] Screenshot refreshed successfully
[[10:01:07]] [SUCCESS] Screenshot refreshed successfully
[[10:01:07]] [SUCCESS] Screenshot refreshed
[[10:01:07]] [INFO] Refreshing screenshot...
[[10:01:02]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[10:01:02]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[10:01:02]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[10:01:02]] [INFO] L5CIZqzpQK=running
[[10:01:02]] [INFO] Executing action 177/643: Execute Test Case: Kmart-Signin (5 steps)
[[10:01:01]] [SUCCESS] Screenshot refreshed successfully
[[10:01:01]] [SUCCESS] Screenshot refreshed successfully
[[10:01:01]] [SUCCESS] Screenshot refreshed
[[10:01:01]] [INFO] Refreshing screenshot...
[[10:01:01]] [INFO] q9ZiyYoE5B=pass
[[10:00:59]] [INFO] q9ZiyYoE5B=running
[[10:00:59]] [INFO] Executing action 176/643: iOS Function: alert_accept
[[10:00:58]] [SUCCESS] Screenshot refreshed successfully
[[10:00:58]] [SUCCESS] Screenshot refreshed successfully
[[10:00:58]] [SUCCESS] Screenshot refreshed
[[10:00:58]] [INFO] Refreshing screenshot...
[[10:00:58]] [INFO] STEdg5jOU8=pass
[[10:00:54]] [INFO] STEdg5jOU8=running
[[10:00:54]] [INFO] Executing action 175/643: Tap on Text: "in"
[[10:00:54]] [SUCCESS] Screenshot refreshed successfully
[[10:00:54]] [SUCCESS] Screenshot refreshed successfully
[[10:00:53]] [SUCCESS] Screenshot refreshed
[[10:00:53]] [INFO] Refreshing screenshot...
[[10:00:53]] [INFO] LDH2hlTZT9=pass
[[10:00:47]] [INFO] LDH2hlTZT9=running
[[10:00:47]] [INFO] Executing action 174/643: Wait for 5 ms
[[10:00:47]] [SUCCESS] Screenshot refreshed successfully
[[10:00:47]] [SUCCESS] Screenshot refreshed successfully
[[10:00:47]] [SUCCESS] Screenshot refreshed
[[10:00:47]] [INFO] Refreshing screenshot...
[[10:00:47]] [INFO] 5Dk9h5bQWl=pass
[[10:00:41]] [INFO] 5Dk9h5bQWl=running
[[10:00:41]] [INFO] Executing action 173/643: Tap on element with accessibility_id: Continue to details
[[10:00:40]] [SUCCESS] Screenshot refreshed successfully
[[10:00:40]] [SUCCESS] Screenshot refreshed successfully
[[10:00:40]] [SUCCESS] Screenshot refreshed
[[10:00:40]] [INFO] Refreshing screenshot...
[[10:00:40]] [INFO] VMzFZ2uTwl=pass
[[10:00:26]] [SUCCESS] Screenshot refreshed successfully
[[10:00:26]] [SUCCESS] Screenshot refreshed successfully
[[10:00:26]] [INFO] VMzFZ2uTwl=running
[[10:00:26]] [INFO] Executing action 172/643: Swipe up till element accessibilityid: "Continue to details" is visible
[[10:00:26]] [SUCCESS] Screenshot refreshed
[[10:00:26]] [INFO] Refreshing screenshot...
[[10:00:26]] [SUCCESS] Screenshot refreshed successfully
[[10:00:26]] [SUCCESS] Screenshot refreshed successfully
[[10:00:26]] [SUCCESS] Screenshot refreshed
[[10:00:26]] [INFO] Refreshing screenshot...
[[10:00:22]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[10:00:22]] [SUCCESS] Screenshot refreshed successfully
[[10:00:22]] [SUCCESS] Screenshot refreshed successfully
[[10:00:22]] [SUCCESS] Screenshot refreshed
[[10:00:22]] [INFO] Refreshing screenshot...
[[10:00:10]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[10:00:10]] [SUCCESS] Screenshot refreshed successfully
[[10:00:10]] [SUCCESS] Screenshot refreshed successfully
[[10:00:10]] [SUCCESS] Screenshot refreshed
[[10:00:10]] [INFO] Refreshing screenshot...
[[10:00:04]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[10:00:03]] [SUCCESS] Screenshot refreshed successfully
[[10:00:03]] [SUCCESS] Screenshot refreshed successfully
[[10:00:03]] [SUCCESS] Screenshot refreshed
[[10:00:03]] [INFO] Refreshing screenshot...
[[09:59:59]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:59:59]] [SUCCESS] Screenshot refreshed successfully
[[09:59:59]] [SUCCESS] Screenshot refreshed successfully
[[09:59:59]] [SUCCESS] Screenshot refreshed
[[09:59:59]] [INFO] Refreshing screenshot...
[[09:59:55]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:59:54]] [SUCCESS] Screenshot refreshed successfully
[[09:59:54]] [SUCCESS] Screenshot refreshed successfully
[[09:59:54]] [SUCCESS] Screenshot refreshed
[[09:59:54]] [INFO] Refreshing screenshot...
[[09:59:51]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:59:51]] [SUCCESS] Screenshot refreshed successfully
[[09:59:51]] [SUCCESS] Screenshot refreshed successfully
[[09:59:50]] [SUCCESS] Screenshot refreshed
[[09:59:50]] [INFO] Refreshing screenshot...
[[09:59:47]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[09:59:46]] [SUCCESS] Screenshot refreshed successfully
[[09:59:46]] [SUCCESS] Screenshot refreshed successfully
[[09:59:46]] [SUCCESS] Screenshot refreshed
[[09:59:46]] [INFO] Refreshing screenshot...
[[09:59:39]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[09:59:39]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[09:59:39]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[09:59:39]] [INFO] 1XUKKmBanM=running
[[09:59:39]] [INFO] Executing action 171/643: Execute Test Case: Search and Add (Notebooks) (8 steps)
[[09:59:39]] [SUCCESS] Screenshot refreshed successfully
[[09:59:39]] [SUCCESS] Screenshot refreshed successfully
[[09:59:39]] [SUCCESS] Screenshot refreshed
[[09:59:39]] [INFO] Refreshing screenshot...
[[09:59:39]] [INFO] NurQsFoMkE=pass
[[09:59:35]] [INFO] NurQsFoMkE=running
[[09:59:35]] [INFO] Executing action 170/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:59:35]] [SUCCESS] Screenshot refreshed successfully
[[09:59:35]] [SUCCESS] Screenshot refreshed successfully
[[09:59:34]] [SUCCESS] Screenshot refreshed
[[09:59:34]] [INFO] Refreshing screenshot...
[[09:59:34]] [INFO] 7QpmNS6hif=pass
[[09:59:30]] [INFO] 7QpmNS6hif=running
[[09:59:30]] [INFO] Executing action 169/643: Restart app: env[appid]
[[09:59:29]] [SUCCESS] Screenshot refreshed successfully
[[09:59:29]] [SUCCESS] Screenshot refreshed successfully
[[09:59:29]] [SUCCESS] Screenshot refreshed
[[09:59:29]] [INFO] Refreshing screenshot...
[[09:59:29]] [INFO] 7WYExJTqjp=pass
[[09:59:25]] [INFO] 7WYExJTqjp=running
[[09:59:25]] [INFO] Executing action 168/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:59:25]] [SUCCESS] Screenshot refreshed successfully
[[09:59:25]] [SUCCESS] Screenshot refreshed successfully
[[09:59:25]] [SUCCESS] Screenshot refreshed
[[09:59:25]] [INFO] Refreshing screenshot...
[[09:59:25]] [INFO] 4WfPFN961S=pass
[[09:59:18]] [INFO] 4WfPFN961S=running
[[09:59:18]] [INFO] Executing action 167/643: Swipe from (50%, 70%) to (50%, 30%)
[[09:59:18]] [SUCCESS] Screenshot refreshed successfully
[[09:59:18]] [SUCCESS] Screenshot refreshed successfully
[[09:59:17]] [SUCCESS] Screenshot refreshed
[[09:59:17]] [INFO] Refreshing screenshot...
[[09:59:17]] [INFO] NurQsFoMkE=pass
[[09:59:13]] [INFO] NurQsFoMkE=running
[[09:59:13]] [INFO] Executing action 166/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:59:13]] [SUCCESS] Screenshot refreshed successfully
[[09:59:13]] [SUCCESS] Screenshot refreshed successfully
[[09:59:13]] [SUCCESS] Screenshot refreshed
[[09:59:13]] [INFO] Refreshing screenshot...
[[09:59:13]] [INFO] CkfAScJNq8=pass
[[09:59:09]] [INFO] CkfAScJNq8=running
[[09:59:09]] [INFO] Executing action 165/643: Tap on image: env[closebtnimage]
[[09:59:09]] [SUCCESS] Screenshot refreshed successfully
[[09:59:09]] [SUCCESS] Screenshot refreshed successfully
[[09:59:09]] [SUCCESS] Screenshot refreshed
[[09:59:09]] [INFO] Refreshing screenshot...
[[09:59:09]] [INFO] 1NWfFsDiTQ=pass
[[09:58:54]] [INFO] 1NWfFsDiTQ=running
[[09:58:54]] [INFO] Executing action 164/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:58:53]] [SUCCESS] Screenshot refreshed successfully
[[09:58:53]] [SUCCESS] Screenshot refreshed successfully
[[09:58:53]] [SUCCESS] Screenshot refreshed
[[09:58:53]] [INFO] Refreshing screenshot...
[[09:58:53]] [INFO] tufIibCj03=pass
[[09:58:49]] [INFO] tufIibCj03=running
[[09:58:49]] [INFO] Executing action 163/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[09:58:49]] [SUCCESS] Screenshot refreshed successfully
[[09:58:49]] [SUCCESS] Screenshot refreshed successfully
[[09:58:49]] [SUCCESS] Screenshot refreshed
[[09:58:49]] [INFO] Refreshing screenshot...
[[09:58:49]] [INFO] g8u66qfKkX=pass
[[09:58:46]] [INFO] g8u66qfKkX=running
[[09:58:46]] [INFO] Executing action 162/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[09:58:45]] [SUCCESS] Screenshot refreshed successfully
[[09:58:45]] [SUCCESS] Screenshot refreshed successfully
[[09:58:45]] [SUCCESS] Screenshot refreshed
[[09:58:45]] [INFO] Refreshing screenshot...
[[09:58:45]] [INFO] mg4S62Rdtq=pass
[[09:58:34]] [INFO] mg4S62Rdtq=running
[[09:58:34]] [INFO] Executing action 161/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[09:58:33]] [SUCCESS] Screenshot refreshed successfully
[[09:58:33]] [SUCCESS] Screenshot refreshed successfully
[[09:58:33]] [SUCCESS] Screenshot refreshed
[[09:58:33]] [INFO] Refreshing screenshot...
[[09:58:33]] [INFO] pCPTAtSZbf=pass
[[09:58:29]] [INFO] pCPTAtSZbf=running
[[09:58:29]] [INFO] Executing action 160/643: iOS Function: text - Text: "Wonderbaby@5"
[[09:58:29]] [SUCCESS] Screenshot refreshed successfully
[[09:58:29]] [SUCCESS] Screenshot refreshed successfully
[[09:58:28]] [SUCCESS] Screenshot refreshed
[[09:58:28]] [INFO] Refreshing screenshot...
[[09:58:28]] [INFO] DaVBARRwft=pass
[[09:58:24]] [INFO] DaVBARRwft=running
[[09:58:24]] [INFO] Executing action 159/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[09:58:24]] [SUCCESS] Screenshot refreshed successfully
[[09:58:24]] [SUCCESS] Screenshot refreshed successfully
[[09:58:24]] [SUCCESS] Screenshot refreshed
[[09:58:24]] [INFO] Refreshing screenshot...
[[09:58:24]] [INFO] e1RoZWCZJb=pass
[[09:58:19]] [INFO] e1RoZWCZJb=running
[[09:58:19]] [INFO] Executing action 158/643: iOS Function: text - Text: "<EMAIL>"
[[09:58:19]] [SUCCESS] Screenshot refreshed successfully
[[09:58:19]] [SUCCESS] Screenshot refreshed successfully
[[09:58:19]] [SUCCESS] Screenshot refreshed
[[09:58:19]] [INFO] Refreshing screenshot...
[[09:58:19]] [INFO] 50Z2jrodNd=pass
[[09:58:15]] [INFO] 50Z2jrodNd=running
[[09:58:15]] [INFO] Executing action 157/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:58:15]] [SUCCESS] Screenshot refreshed successfully
[[09:58:15]] [SUCCESS] Screenshot refreshed successfully
[[09:58:14]] [SUCCESS] Screenshot refreshed
[[09:58:14]] [INFO] Refreshing screenshot...
[[09:58:14]] [INFO] q9ZiyYoE5B=pass
[[09:58:12]] [INFO] q9ZiyYoE5B=running
[[09:58:12]] [INFO] Executing action 156/643: iOS Function: alert_accept
[[09:58:12]] [SUCCESS] Screenshot refreshed successfully
[[09:58:12]] [SUCCESS] Screenshot refreshed successfully
[[09:58:11]] [SUCCESS] Screenshot refreshed
[[09:58:11]] [INFO] Refreshing screenshot...
[[09:58:11]] [INFO] 6PL8P3rT57=pass
[[09:58:07]] [INFO] 6PL8P3rT57=running
[[09:58:07]] [INFO] Executing action 155/643: Tap on Text: "Sign"
[[09:58:07]] [SUCCESS] Screenshot refreshed successfully
[[09:58:07]] [SUCCESS] Screenshot refreshed successfully
[[09:58:07]] [SUCCESS] Screenshot refreshed
[[09:58:07]] [INFO] Refreshing screenshot...
[[09:58:07]] [INFO] 2YGctqXNED=pass
[[09:58:01]] [INFO] 2YGctqXNED=running
[[09:58:01]] [INFO] Executing action 154/643: Tap on element with accessibility_id: Continue to details
[[09:58:01]] [SUCCESS] Screenshot refreshed successfully
[[09:58:01]] [SUCCESS] Screenshot refreshed successfully
[[09:58:01]] [SUCCESS] Screenshot refreshed
[[09:58:01]] [INFO] Refreshing screenshot...
[[09:58:01]] [INFO] 2YGctqXNED=pass
[[09:57:52]] [INFO] 2YGctqXNED=running
[[09:57:52]] [INFO] Executing action 153/643: Swipe up till element accessibilityid: "Continue to details" is visible
[[09:57:52]] [SUCCESS] Screenshot refreshed successfully
[[09:57:52]] [SUCCESS] Screenshot refreshed successfully
[[09:57:52]] [SUCCESS] Screenshot refreshed
[[09:57:52]] [INFO] Refreshing screenshot...
[[09:57:52]] [INFO] tufIibCj03=pass
[[09:57:48]] [INFO] tufIibCj03=running
[[09:57:48]] [INFO] Executing action 152/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[09:57:48]] [SUCCESS] Screenshot refreshed successfully
[[09:57:48]] [SUCCESS] Screenshot refreshed successfully
[[09:57:48]] [SUCCESS] Screenshot refreshed
[[09:57:48]] [INFO] Refreshing screenshot...
[[09:57:48]] [INFO] g8u66qfKkX=pass
[[09:57:44]] [INFO] g8u66qfKkX=running
[[09:57:44]] [INFO] Executing action 151/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[09:57:44]] [SUCCESS] Screenshot refreshed successfully
[[09:57:44]] [SUCCESS] Screenshot refreshed successfully
[[09:57:44]] [SUCCESS] Screenshot refreshed
[[09:57:44]] [INFO] Refreshing screenshot...
[[09:57:44]] [INFO] ZBXuV4sJUR=pass
[[09:57:32]] [INFO] ZBXuV4sJUR=running
[[09:57:32]] [INFO] Executing action 150/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[09:57:32]] [SUCCESS] Screenshot refreshed successfully
[[09:57:32]] [SUCCESS] Screenshot refreshed successfully
[[09:57:32]] [SUCCESS] Screenshot refreshed
[[09:57:32]] [INFO] Refreshing screenshot...
[[09:57:32]] [INFO] XryN8qR1DX=pass
[[09:57:28]] [INFO] XryN8qR1DX=running
[[09:57:28]] [INFO] Executing action 149/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:57:28]] [SUCCESS] Screenshot refreshed successfully
[[09:57:28]] [SUCCESS] Screenshot refreshed successfully
[[09:57:27]] [SUCCESS] Screenshot refreshed
[[09:57:27]] [INFO] Refreshing screenshot...
[[09:57:27]] [INFO] XcWXIMtv1E=pass
[[09:57:21]] [INFO] XcWXIMtv1E=running
[[09:57:21]] [INFO] Executing action 148/643: Wait for 5 ms
[[09:57:21]] [SUCCESS] Screenshot refreshed successfully
[[09:57:21]] [SUCCESS] Screenshot refreshed successfully
[[09:57:20]] [SUCCESS] Screenshot refreshed
[[09:57:20]] [INFO] Refreshing screenshot...
[[09:57:20]] [INFO] S1cQQxksEj=pass
[[09:57:14]] [INFO] S1cQQxksEj=running
[[09:57:14]] [INFO] Executing action 147/643: Tap on element with accessibility_id: Add to bag
[[09:57:14]] [SUCCESS] Screenshot refreshed successfully
[[09:57:14]] [SUCCESS] Screenshot refreshed successfully
[[09:57:13]] [SUCCESS] Screenshot refreshed
[[09:57:13]] [INFO] Refreshing screenshot...
[[09:57:13]] [INFO] K2w9XUGwnb=pass
[[09:57:06]] [INFO] K2w9XUGwnb=running
[[09:57:06]] [INFO] Executing action 146/643: Swipe up till element accessibility_id: "Add to bag" is visible
[[09:57:05]] [SUCCESS] Screenshot refreshed successfully
[[09:57:05]] [SUCCESS] Screenshot refreshed successfully
[[09:57:05]] [SUCCESS] Screenshot refreshed
[[09:57:05]] [INFO] Refreshing screenshot...
[[09:57:05]] [INFO] BTYxjEaZEk=pass
[[09:57:01]] [INFO] BTYxjEaZEk=running
[[09:57:01]] [INFO] Executing action 145/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:57:01]] [SUCCESS] Screenshot refreshed successfully
[[09:57:01]] [SUCCESS] Screenshot refreshed successfully
[[09:57:01]] [SUCCESS] Screenshot refreshed
[[09:57:01]] [INFO] Refreshing screenshot...
[[09:57:01]] [INFO] YC6bBrKQgq=pass
[[09:56:57]] [INFO] YC6bBrKQgq=running
[[09:56:57]] [INFO] Executing action 144/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:56:57]] [SUCCESS] Screenshot refreshed successfully
[[09:56:57]] [SUCCESS] Screenshot refreshed successfully
[[09:56:57]] [SUCCESS] Screenshot refreshed
[[09:56:57]] [INFO] Refreshing screenshot...
[[09:56:57]] [INFO] aRgHcQcLDP=pass
[[09:56:53]] [INFO] aRgHcQcLDP=running
[[09:56:53]] [INFO] Executing action 143/643: iOS Function: text - Text: "uno card"
[[09:56:52]] [SUCCESS] Screenshot refreshed successfully
[[09:56:52]] [SUCCESS] Screenshot refreshed successfully
[[09:56:52]] [SUCCESS] Screenshot refreshed
[[09:56:52]] [INFO] Refreshing screenshot...
[[09:56:52]] [INFO] 4PZC1vVWJW=pass
[[09:56:47]] [INFO] 4PZC1vVWJW=running
[[09:56:47]] [INFO] Executing action 142/643: Tap on Text: "Find"
[[09:56:47]] [SUCCESS] Screenshot refreshed successfully
[[09:56:47]] [SUCCESS] Screenshot refreshed successfully
[[09:56:47]] [SUCCESS] Screenshot refreshed
[[09:56:47]] [INFO] Refreshing screenshot...
[[09:56:47]] [INFO] XryN8qR1DX=pass
[[09:56:43]] [INFO] XryN8qR1DX=running
[[09:56:43]] [INFO] Executing action 141/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:56:43]] [SUCCESS] Screenshot refreshed successfully
[[09:56:43]] [SUCCESS] Screenshot refreshed successfully
[[09:56:42]] [SUCCESS] Screenshot refreshed
[[09:56:42]] [INFO] Refreshing screenshot...
[[09:56:42]] [INFO] 7WYExJTqjp=pass
[[09:56:39]] [INFO] 7WYExJTqjp=running
[[09:56:39]] [INFO] Executing action 140/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:56:38]] [SUCCESS] Screenshot refreshed successfully
[[09:56:38]] [SUCCESS] Screenshot refreshed successfully
[[09:56:38]] [SUCCESS] Screenshot refreshed
[[09:56:38]] [INFO] Refreshing screenshot...
[[09:56:38]] [INFO] 4WfPFN961S=pass
[[09:56:31]] [INFO] 4WfPFN961S=running
[[09:56:31]] [INFO] Executing action 139/643: Swipe from (50%, 70%) to (50%, 30%)
[[09:56:31]] [SUCCESS] Screenshot refreshed successfully
[[09:56:31]] [SUCCESS] Screenshot refreshed successfully
[[09:56:31]] [SUCCESS] Screenshot refreshed
[[09:56:31]] [INFO] Refreshing screenshot...
[[09:56:31]] [INFO] NurQsFoMkE=pass
[[09:56:26]] [SUCCESS] Screenshot refreshed successfully
[[09:56:26]] [SUCCESS] Screenshot refreshed successfully
[[09:56:26]] [INFO] NurQsFoMkE=running
[[09:56:26]] [INFO] Executing action 138/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:56:26]] [SUCCESS] Screenshot refreshed
[[09:56:26]] [INFO] Refreshing screenshot...
[[09:56:25]] [SUCCESS] Screenshot refreshed successfully
[[09:56:25]] [SUCCESS] Screenshot refreshed successfully
[[09:56:25]] [SUCCESS] Screenshot refreshed
[[09:56:25]] [INFO] Refreshing screenshot...
[[09:56:21]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[09:56:21]] [SUCCESS] Screenshot refreshed successfully
[[09:56:21]] [SUCCESS] Screenshot refreshed successfully
[[09:56:21]] [SUCCESS] Screenshot refreshed
[[09:56:21]] [INFO] Refreshing screenshot...
[[09:56:17]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:56:16]] [SUCCESS] Screenshot refreshed successfully
[[09:56:16]] [SUCCESS] Screenshot refreshed successfully
[[09:56:16]] [SUCCESS] Screenshot refreshed
[[09:56:16]] [INFO] Refreshing screenshot...
[[09:56:12]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[09:56:11]] [SUCCESS] Screenshot refreshed successfully
[[09:56:11]] [SUCCESS] Screenshot refreshed successfully
[[09:56:11]] [SUCCESS] Screenshot refreshed
[[09:56:11]] [INFO] Refreshing screenshot...
[[09:56:07]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:56:07]] [SUCCESS] Screenshot refreshed successfully
[[09:56:07]] [SUCCESS] Screenshot refreshed successfully
[[09:56:07]] [SUCCESS] Screenshot refreshed
[[09:56:07]] [INFO] Refreshing screenshot...
[[09:56:01]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:56:01]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[09:56:01]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[09:56:01]] [INFO] APqAlKbucp=running
[[09:56:01]] [INFO] Executing action 137/643: Execute Test Case: Kmart-Signin (5 steps)
[[09:56:00]] [SUCCESS] Screenshot refreshed successfully
[[09:56:00]] [SUCCESS] Screenshot refreshed successfully
[[09:56:00]] [SUCCESS] Screenshot refreshed
[[09:56:00]] [INFO] Refreshing screenshot...
[[09:56:00]] [INFO] byEe7qbCpq=pass
[[09:55:57]] [INFO] byEe7qbCpq=running
[[09:55:57]] [INFO] Executing action 136/643: iOS Function: alert_accept
[[09:55:51]] [SUCCESS] Screenshot refreshed successfully
[[09:55:51]] [SUCCESS] Screenshot refreshed successfully
[[09:55:51]] [SUCCESS] Screenshot refreshed
[[09:55:51]] [INFO] Refreshing screenshot...
[[09:55:51]] [INFO] L6wTorOX8B=pass
[[09:55:47]] [INFO] L6wTorOX8B=running
[[09:55:47]] [INFO] Executing action 135/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[09:55:45]] [SUCCESS] Screenshot refreshed successfully
[[09:55:45]] [SUCCESS] Screenshot refreshed successfully
[[09:55:44]] [SUCCESS] Screenshot refreshed
[[09:55:44]] [INFO] Refreshing screenshot...
[[09:55:44]] [INFO] XryN8qR1DX=pass
[[09:55:41]] [INFO] XryN8qR1DX=running
[[09:55:41]] [INFO] Executing action 134/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:55:37]] [SUCCESS] Screenshot refreshed successfully
[[09:55:37]] [SUCCESS] Screenshot refreshed successfully
[[09:55:36]] [SUCCESS] Screenshot refreshed
[[09:55:36]] [INFO] Refreshing screenshot...
[[09:55:36]] [INFO] lCSewtjn1z=pass
[[09:55:32]] [INFO] lCSewtjn1z=running
[[09:55:32]] [INFO] Executing action 133/643: Restart app: env[appid]
[[09:55:12]] [SUCCESS] Screenshot refreshed successfully
[[09:55:12]] [SUCCESS] Screenshot refreshed successfully
[[09:55:11]] [SUCCESS] Screenshot refreshed
[[09:55:11]] [INFO] Refreshing screenshot...
[[09:55:11]] [INFO] IJh702cxG0=pass
[[09:55:08]] [INFO] IJh702cxG0=running
[[09:55:08]] [INFO] Executing action 132/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:54:47]] [SUCCESS] Screenshot refreshed successfully
[[09:54:47]] [SUCCESS] Screenshot refreshed successfully
[[09:54:47]] [SUCCESS] Screenshot refreshed
[[09:54:47]] [INFO] Refreshing screenshot...
[[09:54:47]] [INFO] 4WfPFN961S=pass
[[09:54:41]] [INFO] 4WfPFN961S=running
[[09:54:41]] [INFO] Executing action 131/643: Swipe from (50%, 70%) to (50%, 30%)
[[09:54:33]] [SUCCESS] Screenshot refreshed successfully
[[09:54:33]] [SUCCESS] Screenshot refreshed successfully
[[09:54:33]] [SUCCESS] Screenshot refreshed
[[09:54:33]] [INFO] Refreshing screenshot...
[[09:54:33]] [INFO] AOcOOSuOsB=pass
[[09:54:29]] [INFO] AOcOOSuOsB=running
[[09:54:29]] [INFO] Executing action 130/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:54:09]] [SUCCESS] Screenshot refreshed successfully
[[09:54:09]] [SUCCESS] Screenshot refreshed successfully
[[09:54:09]] [SUCCESS] Screenshot refreshed
[[09:54:09]] [INFO] Refreshing screenshot...
[[09:54:09]] [INFO] AOcOOSuOsB=pass
[[09:54:05]] [INFO] AOcOOSuOsB=running
[[09:54:05]] [INFO] Executing action 129/643: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:53:45]] [SUCCESS] Screenshot refreshed successfully
[[09:53:45]] [SUCCESS] Screenshot refreshed successfully
[[09:53:45]] [SUCCESS] Screenshot refreshed
[[09:53:45]] [INFO] Refreshing screenshot...
[[09:53:45]] [INFO] N2yjynioko=pass
[[09:53:41]] [INFO] N2yjynioko=running
[[09:53:41]] [INFO] Executing action 128/643: iOS Function: text - Text: "Wonderbaby@5"
[[09:53:33]] [SUCCESS] Screenshot refreshed successfully
[[09:53:33]] [SUCCESS] Screenshot refreshed successfully
[[09:53:33]] [SUCCESS] Screenshot refreshed
[[09:53:33]] [INFO] Refreshing screenshot...
[[09:53:33]] [INFO] SHaIduBnay=pass
[[09:53:29]] [INFO] SHaIduBnay=running
[[09:53:29]] [INFO] Executing action 127/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[09:53:09]] [SUCCESS] Screenshot refreshed successfully
[[09:53:09]] [SUCCESS] Screenshot refreshed successfully
[[09:53:09]] [SUCCESS] Screenshot refreshed
[[09:53:09]] [INFO] Refreshing screenshot...
[[09:53:09]] [INFO] 3XIsWUF1Nj=pass
[[09:53:06]] [INFO] 3XIsWUF1Nj=running
[[09:53:06]] [INFO] Executing action 126/643: Tap on image: captha-chkbox-op-ios.png
[[09:52:45]] [SUCCESS] Screenshot refreshed successfully
[[09:52:45]] [SUCCESS] Screenshot refreshed successfully
[[09:52:45]] [SUCCESS] Screenshot refreshed
[[09:52:45]] [INFO] Refreshing screenshot...
[[09:52:45]] [INFO] wuIMlAwYVA=pass
[[09:52:41]] [INFO] wuIMlAwYVA=running
[[09:52:41]] [INFO] Executing action 125/643: iOS Function: text - Text: "env[uname1]"
[[09:52:32]] [SUCCESS] Screenshot refreshed successfully
[[09:52:32]] [SUCCESS] Screenshot refreshed successfully
[[09:52:31]] [SUCCESS] Screenshot refreshed
[[09:52:31]] [INFO] Refreshing screenshot...
[[09:52:31]] [INFO] 50Z2jrodNd=pass
[[09:52:27]] [INFO] 50Z2jrodNd=running
[[09:52:27]] [INFO] Executing action 124/643: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[09:52:07]] [SUCCESS] Screenshot refreshed successfully
[[09:52:07]] [SUCCESS] Screenshot refreshed successfully
[[09:52:07]] [SUCCESS] Screenshot refreshed
[[09:52:07]] [INFO] Refreshing screenshot...
[[09:52:07]] [INFO] VK2oI6mXSB=pass
[[09:52:03]] [INFO] VK2oI6mXSB=running
[[09:52:03]] [INFO] Executing action 123/643: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[09:51:43]] [SUCCESS] Screenshot refreshed successfully
[[09:51:43]] [SUCCESS] Screenshot refreshed successfully
[[09:51:43]] [SUCCESS] Screenshot refreshed
[[09:51:43]] [INFO] Refreshing screenshot...
[[09:51:43]] [INFO] q9ZiyYoE5B=pass
[[09:51:41]] [INFO] q9ZiyYoE5B=running
[[09:51:41]] [INFO] Executing action 122/643: iOS Function: alert_accept
[[09:51:21]] [SUCCESS] Screenshot refreshed successfully
[[09:51:21]] [SUCCESS] Screenshot refreshed successfully
[[09:51:21]] [SUCCESS] Screenshot refreshed
[[09:51:21]] [INFO] Refreshing screenshot...
[[09:51:21]] [INFO] 4PZC1vVWJW=pass
[[09:51:16]] [INFO] 4PZC1vVWJW=running
[[09:51:16]] [INFO] Executing action 121/643: Tap on Text: "Sign"
[[09:50:56]] [SUCCESS] Screenshot refreshed successfully
[[09:50:56]] [SUCCESS] Screenshot refreshed successfully
[[09:50:56]] [SUCCESS] Screenshot refreshed
[[09:50:56]] [INFO] Refreshing screenshot...
[[09:50:56]] [INFO] mcscWdhpn2=pass
[[09:50:41]] [INFO] mcscWdhpn2=running
[[09:50:41]] [INFO] Executing action 120/643: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[09:50:32]] [SUCCESS] Screenshot refreshed successfully
[[09:50:32]] [SUCCESS] Screenshot refreshed successfully
[[09:50:32]] [SUCCESS] Screenshot refreshed
[[09:50:32]] [INFO] Refreshing screenshot...
[[09:50:32]] [INFO] 6zUBxjSFym=pass
[[09:50:29]] [INFO] 6zUBxjSFym=running
[[09:50:29]] [INFO] Executing action 119/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:50:09]] [SUCCESS] Screenshot refreshed successfully
[[09:50:09]] [SUCCESS] Screenshot refreshed successfully
[[09:50:08]] [SUCCESS] Screenshot refreshed
[[09:50:08]] [INFO] Refreshing screenshot...
[[09:50:08]] [INFO] BTYxjEaZEk=pass
[[09:50:05]] [INFO] BTYxjEaZEk=running
[[09:50:05]] [INFO] Executing action 118/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:49:44]] [SUCCESS] Screenshot refreshed successfully
[[09:49:44]] [SUCCESS] Screenshot refreshed successfully
[[09:49:44]] [SUCCESS] Screenshot refreshed
[[09:49:44]] [INFO] Refreshing screenshot...
[[09:49:44]] [INFO] YC6bBrKQgq=pass
[[09:49:41]] [INFO] YC6bBrKQgq=running
[[09:49:41]] [INFO] Executing action 117/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:49:35]] [SUCCESS] Screenshot refreshed successfully
[[09:49:35]] [SUCCESS] Screenshot refreshed successfully
[[09:49:35]] [SUCCESS] Screenshot refreshed
[[09:49:35]] [INFO] Refreshing screenshot...
[[09:49:35]] [INFO] aRgHcQcLDP=pass
[[09:49:31]] [INFO] aRgHcQcLDP=running
[[09:49:31]] [INFO] Executing action 116/643: iOS Function: text - Text: "uno card"
[[09:49:11]] [SUCCESS] Screenshot refreshed successfully
[[09:49:11]] [SUCCESS] Screenshot refreshed successfully
[[09:49:11]] [SUCCESS] Screenshot refreshed
[[09:49:11]] [INFO] Refreshing screenshot...
[[09:49:11]] [INFO] 4PZC1vVWJW=pass
[[09:49:06]] [INFO] 4PZC1vVWJW=running
[[09:49:06]] [INFO] Executing action 115/643: Tap on Text: "Find"
[[09:48:46]] [SUCCESS] Screenshot refreshed successfully
[[09:48:46]] [SUCCESS] Screenshot refreshed successfully
[[09:48:45]] [SUCCESS] Screenshot refreshed
[[09:48:45]] [INFO] Refreshing screenshot...
[[09:48:45]] [INFO] lCSewtjn1z=pass
[[09:48:41]] [INFO] lCSewtjn1z=running
[[09:48:41]] [INFO] Executing action 114/643: Restart app: env[appid]
[[09:48:36]] [SUCCESS] Screenshot refreshed successfully
[[09:48:36]] [SUCCESS] Screenshot refreshed successfully
[[09:48:36]] [SUCCESS] Screenshot refreshed
[[09:48:36]] [INFO] Refreshing screenshot...
[[09:48:36]] [INFO] A1Wz7p1iVG=pass
[[09:48:32]] [INFO] A1Wz7p1iVG=running
[[09:48:32]] [INFO] Executing action 113/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:48:12]] [SUCCESS] Screenshot refreshed successfully
[[09:48:12]] [SUCCESS] Screenshot refreshed successfully
[[09:48:11]] [SUCCESS] Screenshot refreshed
[[09:48:11]] [INFO] Refreshing screenshot...
[[09:48:11]] [INFO] ehyLmdZWP2=pass
[[09:48:05]] [INFO] ehyLmdZWP2=running
[[09:48:05]] [INFO] Executing action 112/643: Swipe from (50%, 70%) to (50%, 30%)
[[09:47:45]] [SUCCESS] Screenshot refreshed successfully
[[09:47:45]] [SUCCESS] Screenshot refreshed successfully
[[09:47:44]] [SUCCESS] Screenshot refreshed
[[09:47:44]] [INFO] Refreshing screenshot...
[[09:47:44]] [INFO] ydRnBBO1vR=pass
[[09:47:41]] [INFO] ydRnBBO1vR=running
[[09:47:41]] [INFO] Executing action 111/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:47:33]] [SUCCESS] Screenshot refreshed successfully
[[09:47:33]] [SUCCESS] Screenshot refreshed successfully
[[09:47:33]] [SUCCESS] Screenshot refreshed
[[09:47:33]] [INFO] Refreshing screenshot...
[[09:47:33]] [INFO] quZwUwj3a8=pass
[[09:47:29]] [INFO] quZwUwj3a8=running
[[09:47:29]] [INFO] Executing action 110/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[09:47:09]] [SUCCESS] Screenshot refreshed successfully
[[09:47:09]] [SUCCESS] Screenshot refreshed successfully
[[09:47:09]] [SUCCESS] Screenshot refreshed
[[09:47:09]] [INFO] Refreshing screenshot...
[[09:47:09]] [INFO] FHRlQXe58T=pass
[[09:47:05]] [INFO] FHRlQXe58T=running
[[09:47:05]] [INFO] Executing action 109/643: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:46:45]] [SUCCESS] Screenshot refreshed successfully
[[09:46:45]] [SUCCESS] Screenshot refreshed successfully
[[09:46:45]] [SUCCESS] Screenshot refreshed
[[09:46:45]] [INFO] Refreshing screenshot...
[[09:46:45]] [INFO] 8uojw2klHA=pass
[[09:46:41]] [INFO] 8uojw2klHA=running
[[09:46:41]] [INFO] Executing action 108/643: iOS Function: text - Text: "env[pwd]"
[[09:46:34]] [SUCCESS] Screenshot refreshed successfully
[[09:46:34]] [SUCCESS] Screenshot refreshed successfully
[[09:46:34]] [SUCCESS] Screenshot refreshed
[[09:46:34]] [INFO] Refreshing screenshot...
[[09:46:34]] [INFO] SHaIduBnay=pass
[[09:46:30]] [INFO] SHaIduBnay=running
[[09:46:30]] [INFO] Executing action 107/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:46:10]] [SUCCESS] Screenshot refreshed successfully
[[09:46:10]] [SUCCESS] Screenshot refreshed successfully
[[09:46:09]] [SUCCESS] Screenshot refreshed
[[09:46:09]] [INFO] Refreshing screenshot...
[[09:46:09]] [INFO] TGoXyeQtB7=pass
[[09:46:05]] [INFO] TGoXyeQtB7=running
[[09:46:05]] [INFO] Executing action 106/643: iOS Function: text - Text: "env[uname]"
[[09:45:45]] [SUCCESS] Screenshot refreshed successfully
[[09:45:45]] [SUCCESS] Screenshot refreshed successfully
[[09:45:45]] [SUCCESS] Screenshot refreshed
[[09:45:45]] [INFO] Refreshing screenshot...
[[09:45:45]] [INFO] rLCI6NVxSc=pass
[[09:45:41]] [INFO] rLCI6NVxSc=running
[[09:45:41]] [INFO] Executing action 105/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:45:31]] [SUCCESS] Screenshot refreshed successfully
[[09:45:31]] [SUCCESS] Screenshot refreshed successfully
[[09:45:31]] [SUCCESS] Screenshot refreshed
[[09:45:31]] [INFO] Refreshing screenshot...
[[09:45:31]] [INFO] 6mHVWI3j5e=pass
[[09:45:27]] [INFO] 6mHVWI3j5e=running
[[09:45:27]] [INFO] Executing action 104/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:45:07]] [SUCCESS] Screenshot refreshed successfully
[[09:45:07]] [SUCCESS] Screenshot refreshed successfully
[[09:45:07]] [SUCCESS] Screenshot refreshed
[[09:45:07]] [INFO] Refreshing screenshot...
[[09:45:07]] [INFO] rJVGLpLWM3=pass
[[09:45:04]] [INFO] rJVGLpLWM3=running
[[09:45:04]] [INFO] Executing action 103/643: iOS Function: alert_accept
[[09:44:44]] [SUCCESS] Screenshot refreshed successfully
[[09:44:44]] [SUCCESS] Screenshot refreshed successfully
[[09:44:44]] [SUCCESS] Screenshot refreshed
[[09:44:44]] [INFO] Refreshing screenshot...
[[09:44:44]] [INFO] WlISsMf9QA=pass
[[09:44:41]] [INFO] WlISsMf9QA=running
[[09:44:41]] [INFO] Executing action 102/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[09:44:36]] [SUCCESS] Screenshot refreshed successfully
[[09:44:36]] [SUCCESS] Screenshot refreshed successfully
[[09:44:36]] [SUCCESS] Screenshot refreshed
[[09:44:36]] [INFO] Refreshing screenshot...
[[09:44:36]] [INFO] IvqPpScAJa=pass
[[09:44:32]] [INFO] IvqPpScAJa=running
[[09:44:32]] [INFO] Executing action 101/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[09:44:12]] [SUCCESS] Screenshot refreshed successfully
[[09:44:12]] [SUCCESS] Screenshot refreshed successfully
[[09:44:11]] [SUCCESS] Screenshot refreshed
[[09:44:11]] [INFO] Refreshing screenshot...
[[09:44:11]] [INFO] bGo3feCwBQ=pass
[[09:44:08]] [INFO] bGo3feCwBQ=running
[[09:44:08]] [INFO] Executing action 100/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:43:48]] [SUCCESS] Screenshot refreshed successfully
[[09:43:48]] [SUCCESS] Screenshot refreshed successfully
[[09:43:47]] [SUCCESS] Screenshot refreshed
[[09:43:47]] [INFO] Refreshing screenshot...
[[09:43:47]] [INFO] 4WfPFN961S=pass
[[09:43:41]] [INFO] 4WfPFN961S=running
[[09:43:41]] [INFO] Executing action 99/643: Swipe from (50%, 70%) to (50%, 30%)
[[09:43:33]] [SUCCESS] Screenshot refreshed successfully
[[09:43:33]] [SUCCESS] Screenshot refreshed successfully
[[09:43:33]] [SUCCESS] Screenshot refreshed
[[09:43:33]] [INFO] Refreshing screenshot...
[[09:43:33]] [INFO] F0gZF1jEnT=pass
[[09:43:29]] [INFO] F0gZF1jEnT=running
[[09:43:29]] [INFO] Executing action 98/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:43:09]] [SUCCESS] Screenshot refreshed successfully
[[09:43:09]] [SUCCESS] Screenshot refreshed successfully
[[09:43:09]] [SUCCESS] Screenshot refreshed
[[09:43:09]] [INFO] Refreshing screenshot...
[[09:43:09]] [INFO] EDHl0X27Wi=pass
[[09:43:05]] [INFO] EDHl0X27Wi=running
[[09:43:05]] [INFO] Executing action 97/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[09:42:45]] [SUCCESS] Screenshot refreshed successfully
[[09:42:45]] [SUCCESS] Screenshot refreshed successfully
[[09:42:45]] [SUCCESS] Screenshot refreshed
[[09:42:45]] [INFO] Refreshing screenshot...
[[09:42:45]] [INFO] j8NXU87gV3=pass
[[09:42:41]] [INFO] j8NXU87gV3=running
[[09:42:41]] [INFO] Executing action 96/643: iOS Function: text - Text: "env[pwd]"
[[09:42:34]] [SUCCESS] Screenshot refreshed successfully
[[09:42:34]] [SUCCESS] Screenshot refreshed successfully
[[09:42:34]] [SUCCESS] Screenshot refreshed
[[09:42:34]] [INFO] Refreshing screenshot...
[[09:42:34]] [INFO] dpVaKL19uc=pass
[[09:42:30]] [INFO] dpVaKL19uc=running
[[09:42:30]] [INFO] Executing action 95/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:42:10]] [SUCCESS] Screenshot refreshed successfully
[[09:42:10]] [SUCCESS] Screenshot refreshed successfully
[[09:42:10]] [SUCCESS] Screenshot refreshed
[[09:42:10]] [INFO] Refreshing screenshot...
[[09:42:10]] [INFO] eOm1WExcrK=pass
[[09:42:05]] [INFO] eOm1WExcrK=running
[[09:42:05]] [INFO] Executing action 94/643: iOS Function: text - Text: "env[uname]"
[[09:41:45]] [SUCCESS] Screenshot refreshed successfully
[[09:41:45]] [SUCCESS] Screenshot refreshed successfully
[[09:41:45]] [SUCCESS] Screenshot refreshed
[[09:41:45]] [INFO] Refreshing screenshot...
[[09:41:45]] [INFO] 50Z2jrodNd=pass
[[09:41:41]] [INFO] 50Z2jrodNd=running
[[09:41:41]] [INFO] Executing action 93/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:41:34]] [SUCCESS] Screenshot refreshed successfully
[[09:41:34]] [SUCCESS] Screenshot refreshed successfully
[[09:41:34]] [SUCCESS] Screenshot refreshed
[[09:41:34]] [INFO] Refreshing screenshot...
[[09:41:34]] [INFO] eJnHS9n9VL=pass
[[09:41:31]] [INFO] eJnHS9n9VL=running
[[09:41:31]] [INFO] Executing action 92/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:41:11]] [SUCCESS] Screenshot refreshed successfully
[[09:41:11]] [SUCCESS] Screenshot refreshed successfully
[[09:41:10]] [SUCCESS] Screenshot refreshed
[[09:41:10]] [INFO] Refreshing screenshot...
[[09:41:10]] [INFO] XuLgjNG74w=pass
[[09:41:08]] [INFO] XuLgjNG74w=running
[[09:41:08]] [INFO] Executing action 91/643: iOS Function: alert_accept
[[09:40:48]] [SUCCESS] Screenshot refreshed successfully
[[09:40:48]] [SUCCESS] Screenshot refreshed successfully
[[09:40:48]] [SUCCESS] Screenshot refreshed
[[09:40:48]] [INFO] Refreshing screenshot...
[[09:40:48]] [INFO] qA1ap4n1m4=pass
[[09:40:41]] [INFO] qA1ap4n1m4=running
[[09:40:41]] [INFO] Executing action 90/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:40:28]] [SUCCESS] Screenshot refreshed successfully
[[09:40:28]] [SUCCESS] Screenshot refreshed successfully
[[09:40:27]] [SUCCESS] Screenshot refreshed
[[09:40:27]] [INFO] Refreshing screenshot...
[[09:40:27]] [INFO] JXFxYCr98V=pass
[[09:40:04]] [SUCCESS] Screenshot refreshed successfully
[[09:40:04]] [SUCCESS] Screenshot refreshed successfully
[[09:40:03]] [INFO] JXFxYCr98V=running
[[09:40:03]] [INFO] Executing action 89/643: Restart app: env[appid]
[[09:40:03]] [SUCCESS] Screenshot refreshed
[[09:40:03]] [INFO] Refreshing screenshot...
[[09:39:43]] [SUCCESS] Screenshot refreshed successfully
[[09:39:43]] [SUCCESS] Screenshot refreshed successfully
[[09:39:43]] [SUCCESS] Screenshot refreshed
[[09:39:43]] [INFO] Refreshing screenshot...
[[09:39:41]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:39:33]] [SUCCESS] Screenshot refreshed successfully
[[09:39:33]] [SUCCESS] Screenshot refreshed successfully
[[09:39:33]] [SUCCESS] Screenshot refreshed
[[09:39:33]] [INFO] Refreshing screenshot...
[[09:39:21]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:39:01]] [SUCCESS] Screenshot refreshed successfully
[[09:39:01]] [SUCCESS] Screenshot refreshed successfully
[[09:39:01]] [SUCCESS] Screenshot refreshed
[[09:39:01]] [INFO] Refreshing screenshot...
[[09:38:57]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:38:45]] [SUCCESS] Screenshot refreshed successfully
[[09:38:45]] [SUCCESS] Screenshot refreshed successfully
[[09:38:44]] [SUCCESS] Screenshot refreshed
[[09:38:44]] [INFO] Refreshing screenshot...
[[09:38:41]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:38:33]] [SUCCESS] Screenshot refreshed successfully
[[09:38:33]] [SUCCESS] Screenshot refreshed successfully
[[09:38:33]] [SUCCESS] Screenshot refreshed
[[09:38:33]] [INFO] Refreshing screenshot...
[[09:38:26]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:38:06]] [SUCCESS] Screenshot refreshed successfully
[[09:38:06]] [SUCCESS] Screenshot refreshed successfully
[[09:38:06]] [SUCCESS] Screenshot refreshed
[[09:38:06]] [INFO] Refreshing screenshot...
[[09:37:41]] [SUCCESS] Screenshot refreshed successfully
[[09:37:41]] [SUCCESS] Screenshot refreshed successfully
[[09:37:41]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[09:37:41]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[09:37:41]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[09:37:41]] [INFO] hbIlJIWlVN=running
[[09:37:41]] [INFO] Executing action 88/643: cleanupSteps action
[[09:37:41]] [SUCCESS] Screenshot refreshed
[[09:37:41]] [INFO] Refreshing screenshot...
[[09:37:30]] [SUCCESS] Screenshot refreshed successfully
[[09:37:30]] [SUCCESS] Screenshot refreshed successfully
[[09:37:29]] [SUCCESS] Screenshot refreshed
[[09:37:29]] [INFO] Refreshing screenshot...
[[09:37:26]] [INFO] Executing Multi Step action step 36/36: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[09:37:05]] [SUCCESS] Screenshot refreshed successfully
[[09:37:05]] [SUCCESS] Screenshot refreshed successfully
[[09:37:05]] [SUCCESS] Screenshot refreshed
[[09:37:05]] [INFO] Refreshing screenshot...
[[09:37:01]] [INFO] Executing Multi Step action step 35/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:36:41]] [SUCCESS] Screenshot refreshed successfully
[[09:36:41]] [SUCCESS] Screenshot refreshed successfully
[[09:36:41]] [SUCCESS] Screenshot refreshed
[[09:36:41]] [INFO] Refreshing screenshot...
[[09:36:29]] [INFO] Executing Multi Step action step 34/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[09:36:09]] [SUCCESS] Screenshot refreshed successfully
[[09:36:09]] [SUCCESS] Screenshot refreshed successfully
[[09:36:09]] [SUCCESS] Screenshot refreshed
[[09:36:09]] [INFO] Refreshing screenshot...
[[09:36:05]] [INFO] Executing Multi Step action step 33/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:35:44]] [SUCCESS] Screenshot refreshed successfully
[[09:35:44]] [SUCCESS] Screenshot refreshed successfully
[[09:35:44]] [SUCCESS] Screenshot refreshed
[[09:35:44]] [INFO] Refreshing screenshot...
[[09:35:41]] [INFO] Executing Multi Step action step 32/36: Tap on image: banner-close-updated.png
[[09:35:38]] [SUCCESS] Screenshot refreshed successfully
[[09:35:38]] [SUCCESS] Screenshot refreshed successfully
[[09:35:38]] [SUCCESS] Screenshot refreshed
[[09:35:38]] [INFO] Refreshing screenshot...
[[09:35:29]] [INFO] Executing Multi Step action step 31/36: Swipe from (50%, 70%) to (50%, 30%)
[[09:35:08]] [SUCCESS] Screenshot refreshed successfully
[[09:35:08]] [SUCCESS] Screenshot refreshed successfully
[[09:35:08]] [SUCCESS] Screenshot refreshed
[[09:35:08]] [INFO] Refreshing screenshot...
[[09:35:05]] [INFO] Executing Multi Step action step 30/36: Tap on image: env[delivery-address-img]
[[09:34:45]] [SUCCESS] Screenshot refreshed successfully
[[09:34:45]] [SUCCESS] Screenshot refreshed successfully
[[09:34:45]] [SUCCESS] Screenshot refreshed
[[09:34:45]] [INFO] Refreshing screenshot...
[[09:34:41]] [INFO] Executing Multi Step action step 29/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[09:34:37]] [SUCCESS] Screenshot refreshed successfully
[[09:34:37]] [SUCCESS] Screenshot refreshed successfully
[[09:34:36]] [SUCCESS] Screenshot refreshed
[[09:34:36]] [INFO] Refreshing screenshot...
[[09:34:30]] [INFO] Executing Multi Step action step 28/36: Tap and Type at (54, 314): "305 238 Flinders"
[[09:34:10]] [SUCCESS] Screenshot refreshed successfully
[[09:34:10]] [SUCCESS] Screenshot refreshed successfully
[[09:34:09]] [SUCCESS] Screenshot refreshed
[[09:34:09]] [INFO] Refreshing screenshot...
[[09:34:05]] [INFO] Executing Multi Step action step 27/36: Tap on Text: "address"
[[09:33:45]] [SUCCESS] Screenshot refreshed successfully
[[09:33:45]] [SUCCESS] Screenshot refreshed successfully
[[09:33:45]] [SUCCESS] Screenshot refreshed
[[09:33:45]] [INFO] Refreshing screenshot...
[[09:33:41]] [INFO] Executing Multi Step action step 26/36: iOS Function: text - Text: " "
[[09:33:33]] [SUCCESS] Screenshot refreshed successfully
[[09:33:33]] [SUCCESS] Screenshot refreshed successfully
[[09:33:33]] [SUCCESS] Screenshot refreshed
[[09:33:33]] [INFO] Refreshing screenshot...
[[09:33:29]] [INFO] Executing Multi Step action step 25/36: textClear action
[[09:33:09]] [SUCCESS] Screenshot refreshed successfully
[[09:33:09]] [SUCCESS] Screenshot refreshed successfully
[[09:33:09]] [SUCCESS] Screenshot refreshed
[[09:33:09]] [INFO] Refreshing screenshot...
[[09:33:05]] [INFO] Executing Multi Step action step 24/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[09:32:45]] [SUCCESS] Screenshot refreshed successfully
[[09:32:45]] [SUCCESS] Screenshot refreshed successfully
[[09:32:45]] [SUCCESS] Screenshot refreshed
[[09:32:45]] [INFO] Refreshing screenshot...
[[09:32:41]] [INFO] Executing Multi Step action step 23/36: textClear action
[[09:32:33]] [SUCCESS] Screenshot refreshed successfully
[[09:32:33]] [SUCCESS] Screenshot refreshed successfully
[[09:32:33]] [SUCCESS] Screenshot refreshed
[[09:32:33]] [INFO] Refreshing screenshot...
[[09:32:29]] [INFO] Executing Multi Step action step 22/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:32:09]] [SUCCESS] Screenshot refreshed successfully
[[09:32:09]] [SUCCESS] Screenshot refreshed successfully
[[09:32:09]] [SUCCESS] Screenshot refreshed
[[09:32:09]] [INFO] Refreshing screenshot...
[[09:32:05]] [INFO] Executing Multi Step action step 21/36: textClear action
[[09:31:45]] [SUCCESS] Screenshot refreshed successfully
[[09:31:45]] [SUCCESS] Screenshot refreshed successfully
[[09:31:44]] [SUCCESS] Screenshot refreshed
[[09:31:44]] [INFO] Refreshing screenshot...
[[09:31:41]] [INFO] Executing Multi Step action step 20/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[09:31:33]] [SUCCESS] Screenshot refreshed successfully
[[09:31:33]] [SUCCESS] Screenshot refreshed successfully
[[09:31:33]] [SUCCESS] Screenshot refreshed
[[09:31:33]] [INFO] Refreshing screenshot...
[[09:31:29]] [INFO] Executing Multi Step action step 19/36: textClear action
[[09:31:09]] [SUCCESS] Screenshot refreshed successfully
[[09:31:09]] [SUCCESS] Screenshot refreshed successfully
[[09:31:08]] [SUCCESS] Screenshot refreshed
[[09:31:08]] [INFO] Refreshing screenshot...
[[09:31:05]] [INFO] Executing Multi Step action step 18/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[09:30:45]] [SUCCESS] Screenshot refreshed successfully
[[09:30:45]] [SUCCESS] Screenshot refreshed successfully
[[09:30:44]] [SUCCESS] Screenshot refreshed
[[09:30:44]] [INFO] Refreshing screenshot...
[[09:30:41]] [INFO] Executing Multi Step action step 17/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[09:30:21]] [SUCCESS] Screenshot refreshed successfully
[[09:30:21]] [SUCCESS] Screenshot refreshed successfully
[[09:30:21]] [SUCCESS] Screenshot refreshed
[[09:30:21]] [INFO] Refreshing screenshot...
[[09:30:01]] [INFO] Executing Multi Step action step 16/36: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[09:29:40]] [SUCCESS] Screenshot refreshed successfully
[[09:29:40]] [SUCCESS] Screenshot refreshed successfully
[[09:29:40]] [SUCCESS] Screenshot refreshed
[[09:29:40]] [INFO] Refreshing screenshot...
[[09:29:36]] [INFO] Executing Multi Step action step 15/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[09:29:16]] [SUCCESS] Screenshot refreshed successfully
[[09:29:16]] [SUCCESS] Screenshot refreshed successfully
[[09:29:16]] [SUCCESS] Screenshot refreshed
[[09:29:16]] [INFO] Refreshing screenshot...
[[09:29:13]] [INFO] Executing Multi Step action step 14/36: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[09:28:53]] [SUCCESS] Screenshot refreshed successfully
[[09:28:53]] [SUCCESS] Screenshot refreshed successfully
[[09:28:52]] [SUCCESS] Screenshot refreshed
[[09:28:52]] [INFO] Refreshing screenshot...
[[09:28:41]] [INFO] Executing Multi Step action step 13/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[09:28:35]] [SUCCESS] Screenshot refreshed successfully
[[09:28:35]] [SUCCESS] Screenshot refreshed successfully
[[09:28:35]] [SUCCESS] Screenshot refreshed
[[09:28:35]] [INFO] Refreshing screenshot...
[[09:28:31]] [INFO] Executing Multi Step action step 12/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:28:11]] [SUCCESS] Screenshot refreshed successfully
[[09:28:11]] [SUCCESS] Screenshot refreshed successfully
[[09:28:11]] [SUCCESS] Screenshot refreshed
[[09:28:11]] [INFO] Refreshing screenshot...
[[09:28:04]] [INFO] Executing Multi Step action step 11/36: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[09:27:44]] [SUCCESS] Screenshot refreshed successfully
[[09:27:44]] [SUCCESS] Screenshot refreshed successfully
[[09:27:44]] [SUCCESS] Screenshot refreshed
[[09:27:44]] [INFO] Refreshing screenshot...
[[09:27:41]] [INFO] Executing Multi Step action step 10/36: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:27:37]] [SUCCESS] Screenshot refreshed successfully
[[09:27:37]] [SUCCESS] Screenshot refreshed successfully
[[09:27:37]] [SUCCESS] Screenshot refreshed
[[09:27:37]] [INFO] Refreshing screenshot...
[[09:27:33]] [INFO] Executing Multi Step action step 9/36: iOS Function: text - Text: "Uno card"
[[09:27:13]] [SUCCESS] Screenshot refreshed successfully
[[09:27:13]] [SUCCESS] Screenshot refreshed successfully
[[09:27:12]] [SUCCESS] Screenshot refreshed
[[09:27:12]] [INFO] Refreshing screenshot...
[[09:27:08]] [INFO] Executing Multi Step action step 8/36: Tap on Text: "Find"
[[09:26:48]] [SUCCESS] Screenshot refreshed successfully
[[09:26:48]] [SUCCESS] Screenshot refreshed successfully
[[09:26:47]] [SUCCESS] Screenshot refreshed
[[09:26:47]] [INFO] Refreshing screenshot...
[[09:26:36]] [INFO] Executing Multi Step action step 7/36: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]"
[[09:26:16]] [SUCCESS] Screenshot refreshed successfully
[[09:26:16]] [SUCCESS] Screenshot refreshed successfully
[[09:26:15]] [SUCCESS] Screenshot refreshed
[[09:26:15]] [INFO] Refreshing screenshot...
[[09:26:03]] [INFO] Executing Multi Step action step 6/36: Tap if locator exists: accessibility_id="btnUpdate"
[[09:25:43]] [SUCCESS] Screenshot refreshed successfully
[[09:25:43]] [SUCCESS] Screenshot refreshed successfully
[[09:25:43]] [SUCCESS] Screenshot refreshed
[[09:25:43]] [INFO] Refreshing screenshot...
[[09:25:31]] [INFO] Executing Multi Step action step 5/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[09:25:11]] [SUCCESS] Screenshot refreshed successfully
[[09:25:11]] [SUCCESS] Screenshot refreshed successfully
[[09:25:11]] [SUCCESS] Screenshot refreshed
[[09:25:11]] [INFO] Refreshing screenshot...
[[09:25:06]] [INFO] Executing Multi Step action step 4/36: Tap on Text: "Save"
[[09:24:46]] [SUCCESS] Screenshot refreshed successfully
[[09:24:46]] [SUCCESS] Screenshot refreshed successfully
[[09:24:46]] [SUCCESS] Screenshot refreshed
[[09:24:46]] [INFO] Refreshing screenshot...
[[09:24:41]] [INFO] Executing Multi Step action step 3/36: Tap on element with accessibility_id: btnCurrentLocationButton
[[09:24:30]] [SUCCESS] Screenshot refreshed successfully
[[09:24:30]] [SUCCESS] Screenshot refreshed successfully
[[09:24:30]] [SUCCESS] Screenshot refreshed
[[09:24:30]] [INFO] Refreshing screenshot...
[[09:24:26]] [INFO] Executing Multi Step action step 2/36: Wait till accessibility_id=btnCurrentLocationButton
[[09:24:06]] [SUCCESS] Screenshot refreshed successfully
[[09:24:06]] [SUCCESS] Screenshot refreshed successfully
[[09:24:05]] [SUCCESS] Screenshot refreshed
[[09:24:05]] [INFO] Refreshing screenshot...
[[09:23:41]] [INFO] Executing Multi Step action step 1/36: Tap on Text: "Edit"
[[09:23:41]] [INFO] Loaded 36 steps from test case: Delivery  Buy
[[09:23:41]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[09:23:41]] [INFO] 8ZYdW2lMKv=running
[[09:23:41]] [INFO] Executing action 87/643: Execute Test Case: Delivery  Buy (36 steps)
[[09:23:36]] [SUCCESS] Screenshot refreshed successfully
[[09:23:36]] [SUCCESS] Screenshot refreshed successfully
[[09:23:36]] [SUCCESS] Screenshot refreshed
[[09:23:36]] [INFO] Refreshing screenshot...
[[09:23:36]] [INFO] cKNu2QoRC1=pass
[[09:23:32]] [INFO] cKNu2QoRC1=running
[[09:23:32]] [INFO] Executing action 86/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[09:23:12]] [SUCCESS] Screenshot refreshed successfully
[[09:23:12]] [SUCCESS] Screenshot refreshed successfully
[[09:23:11]] [SUCCESS] Screenshot refreshed
[[09:23:11]] [INFO] Refreshing screenshot...
[[09:23:11]] [INFO] OyUowAaBzD=pass
[[09:23:08]] [INFO] OyUowAaBzD=running
[[09:23:08]] [INFO] Executing action 85/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:22:47]] [SUCCESS] Screenshot refreshed successfully
[[09:22:47]] [SUCCESS] Screenshot refreshed successfully
[[09:22:47]] [SUCCESS] Screenshot refreshed
[[09:22:47]] [INFO] Refreshing screenshot...
[[09:22:47]] [INFO] Ob26qqcA0p=pass
[[09:22:41]] [INFO] Ob26qqcA0p=running
[[09:22:41]] [INFO] Executing action 84/643: Swipe from (50%, 70%) to (50%, 30%)
[[09:22:24]] [SUCCESS] Screenshot refreshed successfully
[[09:22:24]] [SUCCESS] Screenshot refreshed successfully
[[09:22:23]] [SUCCESS] Screenshot refreshed
[[09:22:23]] [INFO] Refreshing screenshot...
[[09:22:23]] [INFO] k3mu9Mt7Ec=pass
[[09:22:20]] [INFO] k3mu9Mt7Ec=running
[[09:22:20]] [INFO] Executing action 83/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:22:05]] [SUCCESS] Screenshot refreshed successfully
[[09:22:05]] [SUCCESS] Screenshot refreshed successfully
[[09:22:05]] [SUCCESS] Screenshot refreshed
[[09:22:05]] [INFO] Refreshing screenshot...
[[09:22:05]] [INFO] 8umPSX0vrr=pass
[[09:22:01]] [INFO] 8umPSX0vrr=running
[[09:22:01]] [INFO] Executing action 82/643: Tap on image: banner-close-updated.png
[[09:21:40]] [SUCCESS] Screenshot refreshed successfully
[[09:21:40]] [SUCCESS] Screenshot refreshed successfully
[[09:21:40]] [SUCCESS] Screenshot refreshed
[[09:21:40]] [INFO] Refreshing screenshot...
[[09:21:40]] [INFO] pr9o8Zsm5p=pass
[[09:21:36]] [INFO] pr9o8Zsm5p=running
[[09:21:36]] [INFO] Executing action 81/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:21:16]] [SUCCESS] Screenshot refreshed successfully
[[09:21:16]] [SUCCESS] Screenshot refreshed successfully
[[09:21:16]] [SUCCESS] Screenshot refreshed
[[09:21:16]] [INFO] Refreshing screenshot...
[[09:21:16]] [INFO] XCynRs6gJ3=pass
[[09:21:09]] [INFO] XCynRs6gJ3=running
[[09:21:09]] [INFO] Executing action 80/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[09:20:49]] [SUCCESS] Screenshot refreshed successfully
[[09:20:49]] [SUCCESS] Screenshot refreshed successfully
[[09:20:48]] [SUCCESS] Screenshot refreshed
[[09:20:48]] [INFO] Refreshing screenshot...
[[09:20:48]] [INFO] UnxZdeLmYu=pass
[[09:20:37]] [INFO] UnxZdeLmYu=running
[[09:20:37]] [INFO] Executing action 79/643: Wait for 10 ms
[[09:20:17]] [SUCCESS] Screenshot refreshed successfully
[[09:20:17]] [SUCCESS] Screenshot refreshed successfully
[[09:20:17]] [SUCCESS] Screenshot refreshed
[[09:20:17]] [INFO] Refreshing screenshot...
[[09:20:17]] [INFO] qjj0i3rcUh=pass
[[09:20:13]] [INFO] qjj0i3rcUh=running
[[09:20:13]] [INFO] Executing action 78/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[09:19:53]] [SUCCESS] Screenshot refreshed successfully
[[09:19:53]] [SUCCESS] Screenshot refreshed successfully
[[09:19:53]] [SUCCESS] Screenshot refreshed
[[09:19:53]] [INFO] Refreshing screenshot...
[[09:19:53]] [INFO] 42Jm6o7r1t=pass
[[09:19:41]] [INFO] 42Jm6o7r1t=running
[[09:19:41]] [INFO] Executing action 77/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[09:19:30]] [SUCCESS] Screenshot refreshed successfully
[[09:19:30]] [SUCCESS] Screenshot refreshed successfully
[[09:19:29]] [SUCCESS] Screenshot refreshed
[[09:19:29]] [INFO] Refreshing screenshot...
[[09:19:29]] [INFO] lWIRxRm6HE=pass
[[09:19:26]] [INFO] lWIRxRm6HE=running
[[09:19:26]] [INFO] Executing action 76/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:19:06]] [SUCCESS] Screenshot refreshed successfully
[[09:19:06]] [SUCCESS] Screenshot refreshed successfully
[[09:19:05]] [SUCCESS] Screenshot refreshed
[[09:19:05]] [INFO] Refreshing screenshot...
[[09:19:05]] [INFO] Q0fomJIDoQ=pass
[[09:19:01]] [INFO] Q0fomJIDoQ=running
[[09:19:01]] [INFO] Executing action 75/643: Tap on image: banner-close-updated.png
[[09:18:41]] [SUCCESS] Screenshot refreshed successfully
[[09:18:41]] [SUCCESS] Screenshot refreshed successfully
[[09:18:41]] [SUCCESS] Screenshot refreshed
[[09:18:41]] [INFO] Refreshing screenshot...
[[09:18:41]] [INFO] 7SpDO20tS2=pass
[[09:18:30]] [INFO] 7SpDO20tS2=running
[[09:18:30]] [INFO] Executing action 74/643: Wait for 10 ms
[[09:18:10]] [SUCCESS] Screenshot refreshed successfully
[[09:18:10]] [SUCCESS] Screenshot refreshed successfully
[[09:18:09]] [SUCCESS] Screenshot refreshed
[[09:18:09]] [INFO] Refreshing screenshot...
[[09:18:09]] [INFO] FKZs2qCWoU=pass
[[09:18:05]] [INFO] FKZs2qCWoU=running
[[09:18:05]] [INFO] Executing action 73/643: Tap on Text: "Tarneit"
[[09:17:45]] [SUCCESS] Screenshot refreshed successfully
[[09:17:45]] [SUCCESS] Screenshot refreshed successfully
[[09:17:45]] [SUCCESS] Screenshot refreshed
[[09:17:45]] [INFO] Refreshing screenshot...
[[09:17:45]] [INFO] Qbg9bipTGs=pass
[[09:17:41]] [INFO] Qbg9bipTGs=running
[[09:17:41]] [INFO] Executing action 72/643: Swipe from (50%, 70%) to (50%, 30%)
[[09:17:29]] [SUCCESS] Screenshot refreshed successfully
[[09:17:29]] [SUCCESS] Screenshot refreshed successfully
[[09:17:28]] [SUCCESS] Screenshot refreshed
[[09:17:28]] [INFO] Refreshing screenshot...
[[09:17:28]] [INFO] qjj0i3rcUh=pass
[[09:17:25]] [INFO] qjj0i3rcUh=running
[[09:17:25]] [INFO] Executing action 71/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[09:17:04]] [SUCCESS] Screenshot refreshed successfully
[[09:17:04]] [SUCCESS] Screenshot refreshed successfully
[[09:17:04]] [SUCCESS] Screenshot refreshed
[[09:17:04]] [INFO] Refreshing screenshot...
[[09:17:04]] [INFO] uM5FOSrU5U=pass
[[09:17:01]] [INFO] uM5FOSrU5U=running
[[09:17:01]] [INFO] Executing action 70/643: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[09:16:40]] [SUCCESS] Screenshot refreshed successfully
[[09:16:40]] [SUCCESS] Screenshot refreshed successfully
[[09:16:40]] [SUCCESS] Screenshot refreshed
[[09:16:40]] [INFO] Refreshing screenshot...
[[09:16:40]] [INFO] QB2bKb0SsP=pass
[[09:16:28]] [INFO] QB2bKb0SsP=running
[[09:16:28]] [INFO] Executing action 69/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[09:16:08]] [SUCCESS] Screenshot refreshed successfully
[[09:16:08]] [SUCCESS] Screenshot refreshed successfully
[[09:16:08]] [SUCCESS] Screenshot refreshed
[[09:16:08]] [INFO] Refreshing screenshot...
[[09:16:08]] [INFO] F1olhgKhUt=pass
[[09:16:04]] [INFO] F1olhgKhUt=running
[[09:16:04]] [INFO] Executing action 68/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:15:44]] [SUCCESS] Screenshot refreshed successfully
[[09:15:44]] [SUCCESS] Screenshot refreshed successfully
[[09:15:44]] [SUCCESS] Screenshot refreshed
[[09:15:44]] [INFO] Refreshing screenshot...
[[09:15:44]] [INFO] jY0oPjKbuS=pass
[[09:15:41]] [INFO] jY0oPjKbuS=running
[[09:15:41]] [INFO] Executing action 67/643: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[09:15:35]] [SUCCESS] Screenshot refreshed successfully
[[09:15:35]] [SUCCESS] Screenshot refreshed successfully
[[09:15:35]] [SUCCESS] Screenshot refreshed
[[09:15:35]] [INFO] Refreshing screenshot...
[[09:15:35]] [INFO] FnrbyHq7bU=pass
[[09:15:29]] [INFO] FnrbyHq7bU=running
[[09:15:29]] [INFO] Executing action 66/643: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[09:15:09]] [SUCCESS] Screenshot refreshed successfully
[[09:15:09]] [SUCCESS] Screenshot refreshed successfully
[[09:15:08]] [SUCCESS] Screenshot refreshed
[[09:15:08]] [INFO] Refreshing screenshot...
[[09:15:08]] [INFO] nAB6Q8LAdv=pass
[[09:15:05]] [INFO] nAB6Q8LAdv=running
[[09:15:05]] [INFO] Executing action 65/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:14:45]] [SUCCESS] Screenshot refreshed successfully
[[09:14:45]] [SUCCESS] Screenshot refreshed successfully
[[09:14:44]] [SUCCESS] Screenshot refreshed
[[09:14:44]] [INFO] Refreshing screenshot...
[[09:14:44]] [INFO] sc2KH9bG6H=pass
[[09:14:41]] [INFO] sc2KH9bG6H=running
[[09:14:41]] [INFO] Executing action 64/643: iOS Function: text - Text: "Uno card"
[[09:14:34]] [SUCCESS] Screenshot refreshed successfully
[[09:14:34]] [SUCCESS] Screenshot refreshed successfully
[[09:14:34]] [SUCCESS] Screenshot refreshed
[[09:14:34]] [INFO] Refreshing screenshot...
[[09:14:34]] [INFO] ZBXCQNlT8z=pass
[[09:14:29]] [INFO] ZBXCQNlT8z=running
[[09:14:29]] [INFO] Executing action 63/643: Tap on Text: "Find"
[[09:14:09]] [SUCCESS] Screenshot refreshed successfully
[[09:14:09]] [SUCCESS] Screenshot refreshed successfully
[[09:14:09]] [SUCCESS] Screenshot refreshed
[[09:14:09]] [INFO] Refreshing screenshot...
[[09:14:09]] [INFO] HYl6Z7Gvqz=pass
[[09:14:06]] [SUCCESS] Screenshot refreshed successfully
[[09:14:06]] [SUCCESS] Screenshot refreshed successfully
[[09:14:05]] [INFO] HYl6Z7Gvqz=running
[[09:14:05]] [INFO] Executing action 62/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[09:14:05]] [SUCCESS] Screenshot refreshed
[[09:14:05]] [INFO] Refreshing screenshot...
[[09:13:45]] [SUCCESS] Screenshot refreshed successfully
[[09:13:45]] [SUCCESS] Screenshot refreshed successfully
[[09:13:45]] [SUCCESS] Screenshot refreshed
[[09:13:45]] [INFO] Refreshing screenshot...
[[09:13:41]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[09:13:34]] [SUCCESS] Screenshot refreshed successfully
[[09:13:34]] [SUCCESS] Screenshot refreshed successfully
[[09:13:34]] [SUCCESS] Screenshot refreshed
[[09:13:34]] [INFO] Refreshing screenshot...
[[09:13:30]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:13:10]] [SUCCESS] Screenshot refreshed successfully
[[09:13:10]] [SUCCESS] Screenshot refreshed successfully
[[09:13:09]] [SUCCESS] Screenshot refreshed
[[09:13:09]] [INFO] Refreshing screenshot...
[[09:13:05]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[09:12:45]] [SUCCESS] Screenshot refreshed successfully
[[09:12:45]] [SUCCESS] Screenshot refreshed successfully
[[09:12:45]] [SUCCESS] Screenshot refreshed
[[09:12:45]] [INFO] Refreshing screenshot...
[[09:12:41]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:12:39]] [SUCCESS] Screenshot refreshed successfully
[[09:12:39]] [SUCCESS] Screenshot refreshed successfully
[[09:12:39]] [SUCCESS] Screenshot refreshed
[[09:12:39]] [INFO] Refreshing screenshot...
[[09:12:27]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:12:27]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[09:12:27]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[09:12:27]] [INFO] El6k4IPZly=running
[[09:12:27]] [INFO] Executing action 61/643: Execute Test Case: Kmart-Signin (8 steps)
[[09:12:07]] [SUCCESS] Screenshot refreshed successfully
[[09:12:07]] [SUCCESS] Screenshot refreshed successfully
[[09:12:07]] [SUCCESS] Screenshot refreshed
[[09:12:07]] [INFO] Refreshing screenshot...
[[09:12:07]] [INFO] 3caMBvQX7k=pass
[[09:12:03]] [INFO] 3caMBvQX7k=running
[[09:12:03]] [INFO] Executing action 60/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:11:43]] [SUCCESS] Screenshot refreshed successfully
[[09:11:43]] [SUCCESS] Screenshot refreshed successfully
[[09:11:43]] [SUCCESS] Screenshot refreshed
[[09:11:43]] [INFO] Refreshing screenshot...
[[09:11:43]] [INFO] yUJyVO5Wev=pass
[[09:11:41]] [INFO] yUJyVO5Wev=running
[[09:11:41]] [INFO] Executing action 59/643: iOS Function: alert_accept
[[09:11:34]] [SUCCESS] Screenshot refreshed successfully
[[09:11:34]] [SUCCESS] Screenshot refreshed successfully
[[09:11:34]] [SUCCESS] Screenshot refreshed
[[09:11:34]] [INFO] Refreshing screenshot...
[[09:11:34]] [INFO] rkL0oz4kiL=pass
[[09:11:27]] [INFO] rkL0oz4kiL=running
[[09:11:27]] [INFO] Executing action 58/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:11:06]] [SUCCESS] Screenshot refreshed successfully
[[09:11:06]] [SUCCESS] Screenshot refreshed successfully
[[09:11:06]] [SUCCESS] Screenshot refreshed
[[09:11:06]] [INFO] Refreshing screenshot...
[[09:11:06]] [INFO] HotUJOd6oB=pass
[[09:10:41]] [SUCCESS] Screenshot refreshed successfully
[[09:10:41]] [SUCCESS] Screenshot refreshed successfully
[[09:10:41]] [INFO] HotUJOd6oB=running
[[09:10:41]] [INFO] Executing action 57/643: Restart app: env[appid]
[[09:10:41]] [SUCCESS] Screenshot refreshed
[[09:10:41]] [INFO] Refreshing screenshot...
[[09:10:40]] [SUCCESS] Screenshot refreshed successfully
[[09:10:40]] [SUCCESS] Screenshot refreshed successfully
[[09:10:39]] [SUCCESS] Screenshot refreshed
[[09:10:39]] [INFO] Refreshing screenshot...
[[09:10:37]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[09:10:17]] [SUCCESS] Screenshot refreshed successfully
[[09:10:17]] [SUCCESS] Screenshot refreshed successfully
[[09:10:17]] [SUCCESS] Screenshot refreshed
[[09:10:17]] [INFO] Refreshing screenshot...
[[09:10:04]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[09:09:44]] [SUCCESS] Screenshot refreshed successfully
[[09:09:44]] [SUCCESS] Screenshot refreshed successfully
[[09:09:44]] [SUCCESS] Screenshot refreshed
[[09:09:44]] [INFO] Refreshing screenshot...
[[09:09:41]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[09:09:35]] [SUCCESS] Screenshot refreshed successfully
[[09:09:35]] [SUCCESS] Screenshot refreshed successfully
[[09:09:35]] [SUCCESS] Screenshot refreshed
[[09:09:35]] [INFO] Refreshing screenshot...
[[09:09:32]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:09:11]] [SUCCESS] Screenshot refreshed successfully
[[09:09:11]] [SUCCESS] Screenshot refreshed successfully
[[09:09:11]] [SUCCESS] Screenshot refreshed
[[09:09:11]] [INFO] Refreshing screenshot...
[[09:09:05]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[09:08:45]] [SUCCESS] Screenshot refreshed successfully
[[09:08:45]] [SUCCESS] Screenshot refreshed successfully
[[09:08:44]] [SUCCESS] Screenshot refreshed
[[09:08:44]] [INFO] Refreshing screenshot...
[[09:08:28]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[09:08:28]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[09:08:28]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[09:08:28]] [INFO] vKo6Ox3YrP=running
[[09:08:28]] [INFO] Executing action 56/643: cleanupSteps action
[[09:08:07]] [SUCCESS] Screenshot refreshed successfully
[[09:08:07]] [SUCCESS] Screenshot refreshed successfully
[[09:08:07]] [SUCCESS] Screenshot refreshed
[[09:08:07]] [INFO] Refreshing screenshot...
[[09:08:07]] [INFO] x4yLCZHaCR=pass
[[09:08:05]] [INFO] x4yLCZHaCR=running
[[09:08:05]] [INFO] Executing action 55/643: Terminate app: env[appid]
[[09:07:45]] [SUCCESS] Screenshot refreshed successfully
[[09:07:45]] [SUCCESS] Screenshot refreshed successfully
[[09:07:44]] [SUCCESS] Screenshot refreshed
[[09:07:44]] [INFO] Refreshing screenshot...
[[09:07:44]] [INFO] 2p13JoJbbA=pass
[[09:07:41]] [INFO] 2p13JoJbbA=running
[[09:07:41]] [INFO] Executing action 54/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:07:40]] [SUCCESS] Screenshot refreshed successfully
[[09:07:40]] [SUCCESS] Screenshot refreshed successfully
[[09:07:39]] [SUCCESS] Screenshot refreshed
[[09:07:39]] [INFO] Refreshing screenshot...
[[09:07:39]] [INFO] 2p13JoJbbA=pass
[[09:07:36]] [INFO] 2p13JoJbbA=running
[[09:07:36]] [INFO] Executing action 53/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[09:07:20]] [SUCCESS] Screenshot refreshed successfully
[[09:07:20]] [SUCCESS] Screenshot refreshed successfully
[[09:07:20]] [SUCCESS] Screenshot refreshed
[[09:07:20]] [INFO] Refreshing screenshot...
[[09:07:20]] [INFO] nyBidG0kHp=pass
[[09:07:13]] [INFO] nyBidG0kHp=running
[[09:07:13]] [INFO] Executing action 52/643: Swipe from (50%, 50%) to (50%, 20%)
[[09:06:53]] [SUCCESS] Screenshot refreshed successfully
[[09:06:53]] [SUCCESS] Screenshot refreshed successfully
[[09:06:53]] [SUCCESS] Screenshot refreshed
[[09:06:53]] [INFO] Refreshing screenshot...
[[09:06:53]] [INFO] w7I4F66YKQ=pass
[[09:06:41]] [INFO] w7I4F66YKQ=running
[[09:06:41]] [INFO] Executing action 51/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[09:06:23]] [SUCCESS] Screenshot refreshed successfully
[[09:06:23]] [SUCCESS] Screenshot refreshed successfully
[[09:06:23]] [SUCCESS] Screenshot refreshed
[[09:06:23]] [INFO] Refreshing screenshot...
[[09:06:23]] [INFO] F4NGh9HrLw=pass
[[09:06:19]] [INFO] F4NGh9HrLw=running
[[09:06:19]] [INFO] Executing action 50/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:05:59]] [SUCCESS] Screenshot refreshed successfully
[[09:05:59]] [SUCCESS] Screenshot refreshed successfully
[[09:05:59]] [SUCCESS] Screenshot refreshed
[[09:05:59]] [INFO] Refreshing screenshot...
[[09:05:59]] [INFO] VtMfqK1V9t=pass
[[09:05:41]] [INFO] VtMfqK1V9t=running
[[09:05:41]] [INFO] Executing action 49/643: Tap on element with accessibility_id: Add to bag
[[09:05:33]] [SUCCESS] Screenshot refreshed successfully
[[09:05:33]] [SUCCESS] Screenshot refreshed successfully
[[09:05:33]] [SUCCESS] Screenshot refreshed
[[09:05:33]] [INFO] Refreshing screenshot...
[[09:05:33]] [INFO] NOnuFzXy63=pass
[[09:05:29]] [INFO] NOnuFzXy63=running
[[09:05:29]] [INFO] Executing action 48/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:05:09]] [SUCCESS] Screenshot refreshed successfully
[[09:05:09]] [SUCCESS] Screenshot refreshed successfully
[[09:05:08]] [SUCCESS] Screenshot refreshed
[[09:05:08]] [INFO] Refreshing screenshot...
[[09:05:08]] [INFO] kz9lnCdwoH=pass
[[09:05:04]] [INFO] kz9lnCdwoH=running
[[09:05:04]] [INFO] Executing action 47/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:04:44]] [SUCCESS] Screenshot refreshed successfully
[[09:04:44]] [SUCCESS] Screenshot refreshed successfully
[[09:04:44]] [SUCCESS] Screenshot refreshed
[[09:04:44]] [INFO] Refreshing screenshot...
[[09:04:44]] [INFO] kz9lnCdwoH=pass
[[09:04:41]] [INFO] kz9lnCdwoH=running
[[09:04:41]] [INFO] Executing action 46/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:04:33]] [SUCCESS] Screenshot refreshed successfully
[[09:04:33]] [SUCCESS] Screenshot refreshed successfully
[[09:04:32]] [SUCCESS] Screenshot refreshed
[[09:04:32]] [INFO] Refreshing screenshot...
[[09:04:32]] [INFO] qIF9CVPc56=pass
[[09:04:29]] [INFO] qIF9CVPc56=running
[[09:04:29]] [INFO] Executing action 45/643: iOS Function: text - Text: "mat"
[[09:04:09]] [SUCCESS] Screenshot refreshed successfully
[[09:04:09]] [SUCCESS] Screenshot refreshed successfully
[[09:04:08]] [SUCCESS] Screenshot refreshed
[[09:04:08]] [INFO] Refreshing screenshot...
[[09:04:08]] [INFO] yEga5MkcRe=pass
[[09:04:05]] [INFO] yEga5MkcRe=running
[[09:04:05]] [INFO] Executing action 44/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[09:03:45]] [SUCCESS] Screenshot refreshed successfully
[[09:03:45]] [SUCCESS] Screenshot refreshed successfully
[[09:03:45]] [SUCCESS] Screenshot refreshed
[[09:03:45]] [INFO] Refreshing screenshot...
[[09:03:45]] [INFO] F4NGh9HrLw=pass
[[09:03:41]] [INFO] F4NGh9HrLw=running
[[09:03:41]] [INFO] Executing action 43/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[09:03:33]] [SUCCESS] Screenshot refreshed successfully
[[09:03:33]] [SUCCESS] Screenshot refreshed successfully
[[09:03:33]] [SUCCESS] Screenshot refreshed
[[09:03:33]] [INFO] Refreshing screenshot...
[[09:03:33]] [INFO] kz9lnCdwoH=pass
[[09:03:29]] [INFO] kz9lnCdwoH=running
[[09:03:29]] [INFO] Executing action 42/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[09:03:09]] [SUCCESS] Screenshot refreshed successfully
[[09:03:09]] [SUCCESS] Screenshot refreshed successfully
[[09:03:08]] [SUCCESS] Screenshot refreshed
[[09:03:08]] [INFO] Refreshing screenshot...
[[09:03:08]] [INFO] kz9lnCdwoH=pass
[[09:03:05]] [INFO] kz9lnCdwoH=running
[[09:03:05]] [INFO] Executing action 41/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:02:45]] [SUCCESS] Screenshot refreshed successfully
[[09:02:45]] [SUCCESS] Screenshot refreshed successfully
[[09:02:44]] [SUCCESS] Screenshot refreshed
[[09:02:44]] [INFO] Refreshing screenshot...
[[09:02:44]] [INFO] JRheDTvpJf=pass
[[09:02:41]] [INFO] JRheDTvpJf=running
[[09:02:41]] [INFO] Executing action 40/643: iOS Function: text - Text: "Kid toy"
[[09:02:39]] [SUCCESS] Screenshot refreshed successfully
[[09:02:39]] [SUCCESS] Screenshot refreshed successfully
[[09:02:39]] [SUCCESS] Screenshot refreshed
[[09:02:39]] [INFO] Refreshing screenshot...
[[09:02:39]] [INFO] yEga5MkcRe=pass
[[09:02:36]] [INFO] yEga5MkcRe=running
[[09:02:36]] [INFO] Executing action 39/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[09:02:34]] [SUCCESS] Screenshot refreshed successfully
[[09:02:34]] [SUCCESS] Screenshot refreshed successfully
[[09:02:33]] [SUCCESS] Screenshot refreshed
[[09:02:33]] [INFO] Refreshing screenshot...
[[09:02:33]] [INFO] F4NGh9HrLw=pass
[[09:02:30]] [INFO] F4NGh9HrLw=running
[[09:02:30]] [INFO] Executing action 38/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[09:02:09]] [SUCCESS] Screenshot refreshed successfully
[[09:02:09]] [SUCCESS] Screenshot refreshed successfully
[[09:02:09]] [SUCCESS] Screenshot refreshed
[[09:02:09]] [INFO] Refreshing screenshot...
[[09:02:09]] [INFO] XPEr3w6Zof=pass
[[09:02:04]] [INFO] XPEr3w6Zof=running
[[09:02:04]] [INFO] Executing action 37/643: Restart app: env[appid]
[[09:01:44]] [SUCCESS] Screenshot refreshed successfully
[[09:01:44]] [SUCCESS] Screenshot refreshed successfully
[[09:01:44]] [SUCCESS] Screenshot refreshed
[[09:01:44]] [INFO] Refreshing screenshot...
[[09:01:44]] [INFO] PiQRBWBe3E=pass
[[09:01:41]] [INFO] PiQRBWBe3E=running
[[09:01:41]] [INFO] Executing action 36/643: Tap on image: env[device-back-img]
[[09:01:30]] [SUCCESS] Screenshot refreshed successfully
[[09:01:30]] [SUCCESS] Screenshot refreshed successfully
[[09:01:30]] [SUCCESS] Screenshot refreshed
[[09:01:30]] [INFO] Refreshing screenshot...
[[09:01:30]] [INFO] GWoppouz1l=pass
[[09:01:27]] [INFO] GWoppouz1l=running
[[09:01:27]] [INFO] Executing action 35/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[09:01:07]] [SUCCESS] Screenshot refreshed successfully
[[09:01:07]] [SUCCESS] Screenshot refreshed successfully
[[09:01:07]] [SUCCESS] Screenshot refreshed
[[09:01:07]] [INFO] Refreshing screenshot...
[[09:01:07]] [INFO] B6GDXWAmWp=pass
[[09:01:03]] [INFO] B6GDXWAmWp=running
[[09:01:03]] [INFO] Executing action 34/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[09:00:43]] [SUCCESS] Screenshot refreshed successfully
[[09:00:43]] [SUCCESS] Screenshot refreshed successfully
[[09:00:42]] [SUCCESS] Screenshot refreshed
[[09:00:42]] [INFO] Refreshing screenshot...
[[09:00:42]] [INFO] mtYqeDttRc=pass
[[09:00:39]] [INFO] mtYqeDttRc=running
[[09:00:39]] [INFO] Executing action 33/643: Tap on image: env[paypal-close-img]
[[09:00:30]] [SUCCESS] Screenshot refreshed successfully
[[09:00:30]] [SUCCESS] Screenshot refreshed successfully
[[09:00:30]] [SUCCESS] Screenshot refreshed
[[09:00:30]] [INFO] Refreshing screenshot...
[[09:00:30]] [INFO] q6cKxgMAIn=pass
[[09:00:24]] [INFO] q6cKxgMAIn=running
[[09:00:24]] [INFO] Executing action 32/643: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[09:00:22]] [SUCCESS] Screenshot refreshed successfully
[[09:00:22]] [SUCCESS] Screenshot refreshed successfully
[[09:00:22]] [SUCCESS] Screenshot refreshed
[[09:00:22]] [INFO] Refreshing screenshot...
[[09:00:22]] [INFO] KRQDBv2D3A=pass
[[09:00:19]] [INFO] KRQDBv2D3A=running
[[09:00:19]] [INFO] Executing action 31/643: Tap on image: env[device-back-img]
[[09:00:18]] [SUCCESS] Screenshot refreshed successfully
[[09:00:18]] [SUCCESS] Screenshot refreshed successfully
[[09:00:18]] [SUCCESS] Screenshot refreshed
[[09:00:18]] [INFO] Refreshing screenshot...
[[09:00:18]] [INFO] P4b2BITpCf=pass
[[09:00:15]] [INFO] P4b2BITpCf=running
[[09:00:15]] [INFO] Executing action 30/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[09:00:15]] [SUCCESS] Screenshot refreshed successfully
[[09:00:15]] [SUCCESS] Screenshot refreshed successfully
[[09:00:15]] [SUCCESS] Screenshot refreshed
[[09:00:15]] [INFO] Refreshing screenshot...
[[09:00:15]] [INFO] inrxgdWzXr=pass
[[09:00:09]] [INFO] inrxgdWzXr=running
[[09:00:09]] [INFO] Executing action 29/643: Tap on element with accessibility_id: Learn more about Zip
[[09:00:08]] [SUCCESS] Screenshot refreshed successfully
[[09:00:08]] [SUCCESS] Screenshot refreshed successfully
[[09:00:08]] [SUCCESS] Screenshot refreshed
[[09:00:08]] [INFO] Refreshing screenshot...
[[09:00:08]] [INFO] Et3kvnFdxh=pass
[[09:00:05]] [INFO] Et3kvnFdxh=running
[[09:00:05]] [INFO] Executing action 28/643: Tap on image: env[device-back-img]
[[09:00:05]] [INFO] Skipping disabled action 27/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[09:00:04]] [SUCCESS] Screenshot refreshed successfully
[[09:00:04]] [SUCCESS] Screenshot refreshed successfully
[[09:00:04]] [SUCCESS] Screenshot refreshed
[[09:00:04]] [INFO] Refreshing screenshot...
[[09:00:04]] [INFO] pk2DLZFBmx=pass
[[08:59:58]] [INFO] pk2DLZFBmx=running
[[08:59:58]] [INFO] Executing action 26/643: Tap on element with accessibility_id: Learn more about AfterPay
[[08:59:58]] [SUCCESS] Screenshot refreshed successfully
[[08:59:58]] [SUCCESS] Screenshot refreshed successfully
[[08:59:58]] [SUCCESS] Screenshot refreshed
[[08:59:58]] [INFO] Refreshing screenshot...
[[08:59:58]] [INFO] ShJSdXvmVL=pass
[[08:59:50]] [INFO] ShJSdXvmVL=running
[[08:59:50]] [INFO] Executing action 25/643: Swipe up till element accessibility_id: "Learn more about AfterPay" is visible
[[08:59:49]] [SUCCESS] Screenshot refreshed successfully
[[08:59:49]] [SUCCESS] Screenshot refreshed successfully
[[08:59:49]] [SUCCESS] Screenshot refreshed
[[08:59:49]] [INFO] Refreshing screenshot...
[[08:59:49]] [INFO] eShagNJmzI=pass
[[08:59:45]] [INFO] eShagNJmzI=running
[[08:59:45]] [INFO] Executing action 24/643: Wait for 3 ms
[[08:59:45]] [SUCCESS] Screenshot refreshed successfully
[[08:59:45]] [SUCCESS] Screenshot refreshed successfully
[[08:59:44]] [SUCCESS] Screenshot refreshed
[[08:59:44]] [INFO] Refreshing screenshot...
[[08:59:44]] [INFO] sHQtYzpI4s=pass
[[08:59:40]] [INFO] sHQtYzpI4s=running
[[08:59:40]] [INFO] Executing action 23/643: Tap on image: env[closebtnimage]
[[08:59:40]] [SUCCESS] Screenshot refreshed successfully
[[08:59:40]] [SUCCESS] Screenshot refreshed successfully
[[08:59:40]] [SUCCESS] Screenshot refreshed
[[08:59:40]] [INFO] Refreshing screenshot...
[[08:59:40]] [INFO] 83tV9A4NOn=pass
[[08:59:36]] [INFO] 83tV9A4NOn=running
[[08:59:36]] [INFO] Executing action 22/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[08:59:36]] [SUCCESS] Screenshot refreshed successfully
[[08:59:36]] [SUCCESS] Screenshot refreshed successfully
[[08:59:36]] [SUCCESS] Screenshot refreshed
[[08:59:36]] [INFO] Refreshing screenshot...
[[08:59:36]] [INFO] dCqKBG3e7u=pass
[[08:59:32]] [INFO] dCqKBG3e7u=running
[[08:59:32]] [INFO] Executing action 21/643: Tap on image: env[product-share-img]
[[08:59:32]] [SUCCESS] Screenshot refreshed successfully
[[08:59:32]] [SUCCESS] Screenshot refreshed successfully
[[08:59:31]] [SUCCESS] Screenshot refreshed
[[08:59:31]] [INFO] Refreshing screenshot...
[[08:59:31]] [INFO] kAQ1yIIw3h=pass
[[08:59:27]] [INFO] kAQ1yIIw3h=running
[[08:59:27]] [INFO] Executing action 20/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[08:59:27]] [SUCCESS] Screenshot refreshed successfully
[[08:59:27]] [SUCCESS] Screenshot refreshed successfully
[[08:59:27]] [SUCCESS] Screenshot refreshed
[[08:59:27]] [INFO] Refreshing screenshot...
[[08:59:27]] [INFO] OmKfD9iBjD=pass
[[08:59:23]] [INFO] OmKfD9iBjD=running
[[08:59:23]] [INFO] Executing action 19/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:59:23]] [SUCCESS] Screenshot refreshed successfully
[[08:59:23]] [SUCCESS] Screenshot refreshed successfully
[[08:59:23]] [SUCCESS] Screenshot refreshed
[[08:59:23]] [INFO] Refreshing screenshot...
[[08:59:23]] [INFO] dMl1PH9Dlc=pass
[[08:59:12]] [INFO] dMl1PH9Dlc=running
[[08:59:12]] [INFO] Executing action 18/643: Wait for 10 ms
[[08:59:11]] [SUCCESS] Screenshot refreshed successfully
[[08:59:11]] [SUCCESS] Screenshot refreshed successfully
[[08:59:11]] [SUCCESS] Screenshot refreshed
[[08:59:11]] [INFO] Refreshing screenshot...
[[08:59:11]] [INFO] eHLWiRoqqS=pass
[[08:59:07]] [INFO] eHLWiRoqqS=running
[[08:59:07]] [INFO] Executing action 17/643: Swipe from (50%, 70%) to (50%, 30%)
[[08:59:06]] [SUCCESS] Screenshot refreshed successfully
[[08:59:06]] [SUCCESS] Screenshot refreshed successfully
[[08:59:06]] [SUCCESS] Screenshot refreshed
[[08:59:06]] [INFO] Refreshing screenshot...
[[08:59:06]] [INFO] huUnpMMjVR=pass
[[08:59:02]] [INFO] huUnpMMjVR=running
[[08:59:02]] [INFO] Executing action 16/643: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[08:59:02]] [SUCCESS] Screenshot refreshed successfully
[[08:59:02]] [SUCCESS] Screenshot refreshed successfully
[[08:59:02]] [SUCCESS] Screenshot refreshed
[[08:59:02]] [INFO] Refreshing screenshot...
[[08:59:02]] [INFO] XmAxcBtFI0=pass
[[08:58:59]] [INFO] XmAxcBtFI0=running
[[08:58:59]] [INFO] Executing action 15/643: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[08:58:58]] [SUCCESS] Screenshot refreshed successfully
[[08:58:58]] [SUCCESS] Screenshot refreshed successfully
[[08:58:58]] [SUCCESS] Screenshot refreshed
[[08:58:58]] [INFO] Refreshing screenshot...
[[08:58:58]] [INFO] ktAufkDJnF=pass
[[08:58:55]] [INFO] ktAufkDJnF=running
[[08:58:55]] [INFO] Executing action 14/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[08:58:54]] [SUCCESS] Screenshot refreshed successfully
[[08:58:54]] [SUCCESS] Screenshot refreshed successfully
[[08:58:54]] [SUCCESS] Screenshot refreshed
[[08:58:54]] [INFO] Refreshing screenshot...
[[08:58:54]] [INFO] dMl1PH9Dlc=pass
[[08:58:48]] [INFO] dMl1PH9Dlc=running
[[08:58:48]] [INFO] Executing action 13/643: Wait for 5 ms
[[08:58:47]] [SUCCESS] Screenshot refreshed successfully
[[08:58:47]] [SUCCESS] Screenshot refreshed successfully
[[08:58:47]] [SUCCESS] Screenshot refreshed
[[08:58:47]] [INFO] Refreshing screenshot...
[[08:58:47]] [INFO] a50JhCx0ir=pass
[[08:58:44]] [INFO] a50JhCx0ir=running
[[08:58:44]] [INFO] Executing action 12/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[08:58:43]] [SUCCESS] Screenshot refreshed successfully
[[08:58:43]] [SUCCESS] Screenshot refreshed successfully
[[08:58:43]] [SUCCESS] Screenshot refreshed
[[08:58:43]] [INFO] Refreshing screenshot...
[[08:58:43]] [INFO] Y1O1clhMSJ=pass
[[08:58:39]] [INFO] Y1O1clhMSJ=running
[[08:58:39]] [INFO] Executing action 11/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[08:58:39]] [SUCCESS] Screenshot refreshed successfully
[[08:58:39]] [SUCCESS] Screenshot refreshed successfully
[[08:58:39]] [SUCCESS] Screenshot refreshed
[[08:58:39]] [INFO] Refreshing screenshot...
[[08:58:39]] [INFO] lYPskZt0Ya=pass
[[08:58:35]] [INFO] lYPskZt0Ya=running
[[08:58:35]] [INFO] Executing action 10/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:58:35]] [SUCCESS] Screenshot refreshed successfully
[[08:58:35]] [SUCCESS] Screenshot refreshed successfully
[[08:58:35]] [SUCCESS] Screenshot refreshed
[[08:58:35]] [INFO] Refreshing screenshot...
[[08:58:35]] [INFO] xUbWFa8Ok2=pass
[[08:58:31]] [INFO] xUbWFa8Ok2=running
[[08:58:31]] [INFO] Executing action 9/643: Tap on Text: "Latest"
[[08:58:31]] [SUCCESS] Screenshot refreshed successfully
[[08:58:31]] [SUCCESS] Screenshot refreshed successfully
[[08:58:30]] [SUCCESS] Screenshot refreshed
[[08:58:30]] [INFO] Refreshing screenshot...
[[08:58:30]] [INFO] RbNtEW6N9T=pass
[[08:58:27]] [INFO] RbNtEW6N9T=running
[[08:58:27]] [INFO] Executing action 8/643: Tap on Text: "Toys"
[[08:58:26]] [SUCCESS] Screenshot refreshed successfully
[[08:58:26]] [SUCCESS] Screenshot refreshed successfully
[[08:58:26]] [SUCCESS] Screenshot refreshed
[[08:58:26]] [INFO] Refreshing screenshot...
[[08:58:26]] [INFO] ltDXyWvtEz=pass
[[08:58:22]] [INFO] ltDXyWvtEz=running
[[08:58:22]] [INFO] Executing action 7/643: Tap on image: env[device-back-img]
[[08:58:22]] [SUCCESS] Screenshot refreshed successfully
[[08:58:22]] [SUCCESS] Screenshot refreshed successfully
[[08:58:22]] [SUCCESS] Screenshot refreshed
[[08:58:22]] [INFO] Refreshing screenshot...
[[08:58:22]] [INFO] QPKR6jUF9O=pass
[[08:58:19]] [INFO] QPKR6jUF9O=running
[[08:58:19]] [INFO] Executing action 6/643: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[08:58:19]] [SUCCESS] Screenshot refreshed successfully
[[08:58:19]] [SUCCESS] Screenshot refreshed successfully
[[08:58:18]] [SUCCESS] Screenshot refreshed
[[08:58:18]] [INFO] Refreshing screenshot...
[[08:58:18]] [INFO] vfwUVEyq6X=pass
[[08:58:16]] [INFO] vfwUVEyq6X=running
[[08:58:16]] [INFO] Executing action 5/643: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[08:58:15]] [SUCCESS] Screenshot refreshed successfully
[[08:58:15]] [SUCCESS] Screenshot refreshed successfully
[[08:58:15]] [SUCCESS] Screenshot refreshed
[[08:58:15]] [INFO] Refreshing screenshot...
[[08:58:15]] [INFO] Xr6F8gdd8q=pass
[[08:58:12]] [INFO] Xr6F8gdd8q=running
[[08:58:12]] [INFO] Executing action 4/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:58:11]] [SUCCESS] Screenshot refreshed successfully
[[08:58:11]] [SUCCESS] Screenshot refreshed successfully
[[08:58:11]] [SUCCESS] Screenshot refreshed
[[08:58:11]] [INFO] Refreshing screenshot...
[[08:58:11]] [INFO] Xr6F8gdd8q=pass
[[08:58:08]] [INFO] Xr6F8gdd8q=running
[[08:58:08]] [INFO] Executing action 3/643: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[08:58:08]] [SUCCESS] Screenshot refreshed successfully
[[08:58:08]] [SUCCESS] Screenshot refreshed successfully
[[08:58:08]] [SUCCESS] Screenshot refreshed
[[08:58:08]] [INFO] Refreshing screenshot...
[[08:58:08]] [INFO] F4NGh9HrLw=pass
[[08:58:04]] [INFO] F4NGh9HrLw=running
[[08:58:04]] [INFO] Executing action 2/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:58:04]] [SUCCESS] Screenshot refreshed successfully
[[08:58:04]] [SUCCESS] Screenshot refreshed successfully
[[08:58:04]] [SUCCESS] Screenshot refreshed
[[08:58:04]] [INFO] Refreshing screenshot...
[[08:58:04]] [INFO] H9fy9qcFbZ=pass
[[08:57:59]] [INFO] H9fy9qcFbZ=running
[[08:57:59]] [INFO] Executing action 1/643: Restart app: env[appid]
[[08:57:59]] [INFO] ExecutionManager: Starting execution of 643 actions...
[[08:57:59]] [SUCCESS] Cleared 0 screenshots from database
[[08:57:59]] [INFO] Clearing screenshots from database before execution...
[[08:57:59]] [SUCCESS] All screenshots deleted successfully
[[08:57:59]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[08:57:59]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250804_085759/screenshots
[[08:57:59]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250804_085759
[[08:57:59]] [SUCCESS] Report directory initialized successfully
[[08:57:59]] [INFO] Initializing report directory and screenshots folder for test suite...
[[08:57:53]] [INFO] Collapsed all test cases
[[08:57:49]] [SUCCESS] All screenshots deleted successfully
[[08:57:49]] [INFO] All actions cleared
[[08:57:49]] [INFO] Cleaning up screenshots...
[[08:57:42]] [SUCCESS] Screenshot refreshed successfully
[[08:57:42]] [SUCCESS] Screenshot refreshed
[[08:57:42]] [INFO] Refreshing screenshot...
[[08:57:41]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[08:57:41]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[08:57:39]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[08:57:36]] [SUCCESS] Found 1 device(s)
[[08:57:35]] [INFO] Refreshing device list...
