Action Log - 2025-08-02 16:33:52
================================================================================

[[16:33:52]] [INFO] Generating execution report...
[[16:33:52]] [SUCCESS] All tests passed successfully!
[[16:33:52]] [SUCCESS] Screenshot refreshed successfully
[[16:33:52]] [SUCCESS] Screenshot refreshed successfully
[[16:33:52]] [SUCCESS] Screenshot refreshed
[[16:33:52]] [INFO] Refreshing screenshot...
[[16:33:52]] [INFO] 0OyVUxNbaF=pass
[[16:33:49]] [INFO] 0OyVUxNbaF=running
[[16:33:49]] [INFO] Executing action 11/11: Tap on Text: "Shopping"
[[16:33:48]] [SUCCESS] Screenshot refreshed successfully
[[16:33:48]] [SUCCESS] Screenshot refreshed successfully
[[16:33:48]] [SUCCESS] Screenshot refreshed
[[16:33:48]] [INFO] Refreshing screenshot...
[[16:33:48]] [INFO] vwjicir8ov=pass
[[16:33:46]] [INFO] vwjicir8ov=running
[[16:33:46]] [INFO] Executing action 10/11: Tap on element with xpath: //XCUIElementTypeButton[@name="TrolleyListRemoveProductButton"]
[[16:33:46]] [SUCCESS] Screenshot refreshed successfully
[[16:33:46]] [SUCCESS] Screenshot refreshed successfully
[[16:33:46]] [SUCCESS] Screenshot refreshed
[[16:33:46]] [INFO] Refreshing screenshot...
[[16:33:46]] [INFO] KFfbUGUAsp=pass
[[16:33:44]] [INFO] KFfbUGUAsp=running
[[16:33:44]] [INFO] Executing action 9/11: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"in Cart")]
[[16:33:43]] [SUCCESS] Screenshot refreshed successfully
[[16:33:43]] [SUCCESS] Screenshot refreshed successfully
[[16:33:43]] [SUCCESS] Screenshot refreshed
[[16:33:43]] [INFO] Refreshing screenshot...
[[16:33:43]] [INFO] yL81dtqigl=pass
[[16:33:41]] [INFO] yL81dtqigl=running
[[16:33:41]] [INFO] Executing action 8/11: Tap on element with xpath: //XCUIElementTypeButton[@name="HomePageQuantitySelectorAddToCartButton"]
[[16:33:41]] [SUCCESS] Screenshot refreshed successfully
[[16:33:41]] [SUCCESS] Screenshot refreshed successfully
[[16:33:40]] [SUCCESS] Screenshot refreshed
[[16:33:40]] [INFO] Refreshing screenshot...
[[16:33:40]] [INFO] q1ZdKSiX2c=pass
[[16:33:38]] [INFO] q1ZdKSiX2c=running
[[16:33:38]] [INFO] Executing action 7/11: Tap on element with xpath: (//XCUIElementTypeButton[@name="ProductCellActionButton"])[1]
[[16:33:38]] [SUCCESS] Screenshot refreshed successfully
[[16:33:38]] [SUCCESS] Screenshot refreshed successfully
[[16:33:38]] [SUCCESS] Screenshot refreshed
[[16:33:38]] [INFO] Refreshing screenshot...
[[16:33:38]] [INFO] hHL72YSZHM=pass
[[16:33:36]] [INFO] hHL72YSZHM=running
[[16:33:36]] [INFO] Executing action 6/11: Tap on element with xpath: //XCUIElementTypeButton[@name="Search"]
[[16:33:35]] [SUCCESS] Screenshot refreshed successfully
[[16:33:35]] [SUCCESS] Screenshot refreshed successfully
[[16:33:35]] [SUCCESS] Screenshot refreshed
[[16:33:35]] [INFO] Refreshing screenshot...
[[16:33:35]] [INFO] PHhUKvweRf=pass
[[16:33:33]] [INFO] PHhUKvweRf=running
[[16:33:33]] [INFO] Executing action 5/11: Input text: "bread"
[[16:33:33]] [SUCCESS] Screenshot refreshed successfully
[[16:33:33]] [SUCCESS] Screenshot refreshed successfully
[[16:33:33]] [SUCCESS] Screenshot refreshed
[[16:33:33]] [INFO] Refreshing screenshot...
[[16:33:33]] [INFO] A0IS4A1nhc=pass
[[16:33:30]] [INFO] A0IS4A1nhc=running
[[16:33:30]] [INFO] Executing action 4/11: Tap on element with xpath: //XCUIElementTypeButton[@name="SearchButton"]
[[16:33:30]] [SUCCESS] Screenshot refreshed successfully
[[16:33:30]] [SUCCESS] Screenshot refreshed successfully
[[16:33:30]] [SUCCESS] Screenshot refreshed
[[16:33:30]] [INFO] Refreshing screenshot...
[[16:33:30]] [INFO] g35EwHVyF1=pass
[[16:33:28]] [INFO] g35EwHVyF1=running
[[16:33:28]] [INFO] Executing action 3/11: Tap on element with xpath: //XCUIElementTypeButton[@name="Products"]
[[16:33:27]] [SUCCESS] Screenshot refreshed successfully
[[16:33:27]] [SUCCESS] Screenshot refreshed successfully
[[16:33:27]] [SUCCESS] Screenshot refreshed
[[16:33:27]] [INFO] Refreshing screenshot...
[[16:33:27]] [INFO] YDCEwNh5Nm=pass
[[16:33:23]] [INFO] YDCEwNh5Nm=running
[[16:33:23]] [INFO] Executing action 2/11: Tap on element with xpath: //XCUIElementTypeImage[@name="tabLists"]
[[16:33:23]] [SUCCESS] Screenshot refreshed successfully
[[16:33:23]] [SUCCESS] Screenshot refreshed successfully
[[16:33:22]] [SUCCESS] Screenshot refreshed
[[16:33:22]] [INFO] Refreshing screenshot...
[[16:33:22]] [INFO] FaBn38JYVm=pass
[[16:33:19]] [INFO] FaBn38JYVm=running
[[16:33:19]] [INFO] Executing action 1/11: Launch app: com.woolworths.supers
[[16:33:19]] [INFO] ExecutionManager: Starting execution of 11 actions...
[[16:33:19]] [SUCCESS] Cleared 0 screenshots from database
[[16:33:19]] [INFO] Clearing screenshots from database before execution...
[[16:33:19]] [SUCCESS] All screenshots deleted successfully
[[16:33:19]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:33:19]] [INFO] Skipping report initialization - single test case execution
[[16:32:48]] [SUCCESS] All screenshots deleted successfully
[[16:32:48]] [INFO] All actions cleared
[[16:32:48]] [INFO] Cleaning up screenshots...
[[16:32:09]] [SUCCESS] Screenshot refreshed successfully
[[16:32:09]] [SUCCESS] Screenshot refreshed
[[16:32:09]] [INFO] Refreshing screenshot...
[[16:32:08]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[16:32:08]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[16:32:06]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[16:32:05]] [SUCCESS] Found 1 device(s)
[[16:32:04]] [INFO] Refreshing device list...
