{"name": "UI Execution 06/08/2025, 19:03:18", "testCases": [{"name": "Browse & PDP\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            56 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3505ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "3024ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1753ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2311ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"More\"]\" exists", "status": "passed", "duration": "1446ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Scan barcode\"]\" exists", "status": "passed", "duration": "1477ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2626ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2648ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "2698ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2072ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2656ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "status": "passed", "duration": "2340ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Show (\")]", "status": "passed", "duration": "2365ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]\" exists", "status": "passed", "duration": "1642ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"In stock only i... ChipsClose\"]", "status": "passed", "duration": "2599ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2986ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10015ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2122ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2669ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[product-share-img]", "status": "passed", "duration": "2527ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"Check out \")]\" exists", "status": "passed", "duration": "1949ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2824ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Wait for 3 ms", "status": "passed", "duration": "3012ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Swipe up till element accessibility_id: \"Learn more about AfterPay\" is visible", "status": "passed", "duration": "7023ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Learn more about AfterPay", "status": "passed", "duration": "4892ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]\" exists", "status": "unknown", "duration": "13773ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2349ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Tap on element with accessibility_id: Learn more about Zip", "status": "passed", "duration": "4832ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"What is Zip?\"]\" exists", "status": "passed", "duration": "1520ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2255ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with accessibility_id: Learn more about PayPal Pay in 4", "status": "passed", "duration": "4865ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[paypal-close-img]", "status": "passed", "duration": "2492ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Shop at\"]/following-sibling::XCUIElementTypeButton", "status": "passed", "duration": "2790ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPostCodeSelectionScreenHeader\"]\" exists", "status": "passed", "duration": "1414ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2242ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3237ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2483ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2228ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Kid toy\"", "status": "passed", "duration": "2620ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2078ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2668ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "2654ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "2295ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"mat\"", "status": "passed", "duration": "2498ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2155ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "status": "passed", "duration": "2696ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2704ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "17398ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2995ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10712ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 20%)", "status": "passed", "duration": "5563ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2480ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2452ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1079ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Delivery & CNC\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            32 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2185ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5559ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1175ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2076ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "4729ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3373ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2644ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2001ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add", "status": "passed", "duration": "4884ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1663ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2570ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10642ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"Click & Collect\"]\" exists", "status": "passed", "duration": "1536ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2451ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2949ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Tap on Text: \"Tarneit\"", "status": "passed", "duration": "2886ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10021ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2367ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2582ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10557ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2494ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10020ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "6271ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2476ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2219ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2625ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5544ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2391ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2537ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Delivery  Buy (36 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Sign ins\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            97 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2181ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5443ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1305ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2125ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2697ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3256ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2668ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3058ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "3229ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2526ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5496ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2373ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2501ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtLog in\"]", "status": "passed", "duration": "2268ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1177ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2172ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2686ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3243ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2715ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3030ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2234ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "2014ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2523ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5676ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2405ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3229ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3514ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2789ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2039ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2630ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2481ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeStaticText[@name=\"Already a member?\"]\" is visible", "status": "passed", "duration": "14570ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3265ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1170ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "3723ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,\"Email\")]", "status": "passed", "duration": "2854ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname1]\"", "status": "passed", "duration": "3367ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Tap on image: captha-chkbox-op-ios.png", "status": "passed", "duration": "2298ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2809ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3115ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "5483ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2859ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5399ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2361ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3225ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2840ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2293ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1176ms", "action_id": "screenshot_20250806_185155", "screenshot_filename": "screenshot_20250806_185155.png", "report_screenshot": "screenshot_20250806_185155.png", "resolved_screenshot": "screenshots/screenshot_20250806_185155.png", "clean_action_id": "screenshot_20250806_185155", "prefixed_action_id": "al_screenshot_20250806_185155", "action_id_screenshot": "screenshots/screenshot_20250806_185155.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3779ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5415ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2391ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2520ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3453ms", "action_id": "screenshot_20250806_180011", "screenshot_filename": "screenshot_20250806_180011.png", "report_screenshot": "screenshot_20250806_180011.png", "resolved_screenshot": "screenshots/screenshot_20250806_180011.png", "clean_action_id": "screenshot_20250806_180011", "prefixed_action_id": "al_screenshot_20250806_180011", "action_id_screenshot": "screenshots/screenshot_20250806_180011.png"}, {"name": "iOS Function: text - Text: \"uno card\"", "status": "passed", "duration": "2619ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2035ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2626ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibility_id: \"Add to bag\" is visible", "status": "passed", "duration": "6692ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5635ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5013ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2884ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10507ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1935ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2466ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "6862ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4540ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "2775ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1171ms", "action_id": "screenshot_20250806_185753", "screenshot_filename": "screenshot_20250806_185753.png", "report_screenshot": "screenshot_20250806_185753.png", "resolved_screenshot": "screenshots/screenshot_20250806_185753.png", "clean_action_id": "screenshot_20250806_185753", "prefixed_action_id": "al_screenshot_20250806_185753", "action_id_screenshot": "screenshots/screenshot_20250806_185753.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2733ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3218ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "status": "passed", "duration": "2709ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3019ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10587ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1960ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2464ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "14060ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2194ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2952ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5411ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2378ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3292ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2929ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_183450", "screenshot_filename": "screenshot_20250806_183450.png", "report_screenshot": "screenshot_20250806_183450.png", "resolved_screenshot": "screenshots/screenshot_20250806_183450.png", "clean_action_id": "screenshot_20250806_183450", "prefixed_action_id": "al_screenshot_20250806_183450", "action_id_screenshot": "screenshots/screenshot_20250806_183450.png"}, {"name": "Swipe up till element accessibilityid: \"Continue to details\" is visible", "status": "passed", "duration": "12825ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Continue to details", "status": "passed", "duration": "4549ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5014ms", "action_id": "screenshot_20250806_173947", "screenshot_filename": "screenshot_20250806_173947.png", "report_screenshot": "screenshot_20250806_173947.png", "resolved_screenshot": "screenshots/screenshot_20250806_173947.png", "clean_action_id": "screenshot_20250806_173947", "prefixed_action_id": "al_screenshot_20250806_173947", "action_id_screenshot": "screenshots/screenshot_20250806_173947.png"}, {"name": "Tap on Text: \"in\"", "status": "passed", "duration": "2789ms", "action_id": "screenshot_20250806_180617", "screenshot_filename": "screenshot_20250806_180617.png", "report_screenshot": "screenshot_20250806_180617.png", "resolved_screenshot": "screenshots/screenshot_20250806_180617.png", "clean_action_id": "screenshot_20250806_180617", "prefixed_action_id": "al_screenshot_20250806_180617", "action_id_screenshot": "screenshots/screenshot_20250806_180617.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1162ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_184840", "screenshot_filename": "screenshot_20250806_184840.png", "report_screenshot": "screenshot_20250806_184840.png", "resolved_screenshot": "screenshots/screenshot_20250806_184840.png", "clean_action_id": "screenshot_20250806_184840", "prefixed_action_id": "al_screenshot_20250806_184840", "action_id_screenshot": "screenshots/screenshot_20250806_184840.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10756ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2484ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "13839ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[closebtnimage]", "status": "passed", "duration": "2302ms", "action_id": "closebtnim", "screenshot_filename": "closebtnim.png", "report_screenshot": "closebtnim.png", "resolved_screenshot": "screenshots/closebtnim.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2633ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5423ms", "action_id": "screenshot_20250806_174231", "screenshot_filename": "screenshot_20250806_174231.png", "report_screenshot": "screenshot_20250806_174231.png", "resolved_screenshot": "screenshots/screenshot_20250806_174231.png", "clean_action_id": "screenshot_20250806_174231", "prefixed_action_id": "al_screenshot_20250806_174231", "action_id_screenshot": "screenshots/screenshot_20250806_174231.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2348ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "WishList\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            50 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2176ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "6694ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1192ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2072ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2688ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3249ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2649ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "2999ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "9177ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3374ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2635ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2146ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2684ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2550ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "5592ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2897ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"You may also like\"]/following-sibling::*[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "13755ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2803ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "5519ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2868ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3277ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3638ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "iOS Function: text - Text: \"P_43386093\"", "status": "passed", "duration": "2609ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2432ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "4591ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"to wishlist\")]\" is visible", "status": "passed", "duration": "5642ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2957ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2961ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1599ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2302ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "passed", "duration": "2514ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2330ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2514ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "passed", "duration": "2294ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2547ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2953ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10757ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2919ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "1959ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "2498ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2277ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2879ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "ifThenSteps action", "status": "passed", "duration": "4207ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2588ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "ifThenSteps action", "status": "passed", "duration": "4201ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2418ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2283ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5413ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2358ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Kmart-Prod-Signin\n                            \n                            \n                        \n                        \n                            \n                                 <PERSON>try\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "3298ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "7930ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1125ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2165ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2668ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3247ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2690ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "2998ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2801ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2521ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5408ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2361ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2091ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4390ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1186ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2130ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"OnePass\"", "status": "passed", "duration": "2948ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email*\"]", "status": "passed", "duration": "2793ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3339ms", "action_id": "kiransah<PERSON>", "screenshot_filename": "kiransahoo.png", "report_screenshot": "kiransahoo.png", "resolved_screenshot": "screenshots/kiransahoo.png"}, {"name": "Tap on image: captha-chkbox-op-ios.png", "status": "passed", "duration": "2442ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password*\"]", "status": "passed", "duration": "2831ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@6\"", "status": "passed", "duration": "3131ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2753ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2505ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4213ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2387ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2104ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4402ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1190ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2140ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3071ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Tap on Text: \"Apple\"", "status": "passed", "duration": "2932ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10021ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Passcode\"", "status": "passed", "duration": "1713ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"5\"]", "status": "passed", "duration": "1703ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"9\"]", "status": "passed", "duration": "1647ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"1\"]", "status": "passed", "duration": "1626ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"2\"]", "status": "passed", "duration": "1624ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"3\"]", "status": "passed", "duration": "1651ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"4\"]", "status": "passed", "duration": "1642ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "4941ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2449ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5496ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2365ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with accessibility_id=\"txtHomeAccountCtaSignIn\" exists", "status": "passed", "duration": "2082ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4412ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1216ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2163ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Sign in with Google\"]\" is visible", "status": "passed", "duration": "5180ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Sign in with Google\"]", "status": "passed", "duration": "2693ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "status": "passed", "duration": "2824ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2931ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2523ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5450ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2472ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1057ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU- MyAccount\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            62 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2184ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5021ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "5600ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1176ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON>Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "12867ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "1855ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2704ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2521ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Email tax invoice\"]\" is visible", "status": "passed", "duration": "14495ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Email tax invoice\"]", "status": "passed", "duration": "2477ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Print order details", "status": "passed", "duration": "4652ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Cancel\"]", "status": "passed", "duration": "3135ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2347ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5022ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Online orders\")]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2498ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]\" exists", "status": "passed", "duration": "1582ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Learn more about refunds\"]", "status": "passed", "duration": "3002ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Exchanges Returns\"]\" exists", "status": "passed", "duration": "4739ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2275ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "11382ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"Return\"", "status": "passed", "duration": "2932ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2479ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2622ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "status": "passed", "duration": "2371ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"details\"", "status": "passed", "duration": "2661ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2417ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "2681ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2299ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "Tap on Text: \"payment\"", "status": "passed", "duration": "2670ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2304ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2324ms", "action_id": "screenshot_20250806_185155", "screenshot_filename": "screenshot_20250806_185155.png", "report_screenshot": "screenshot_20250806_185155.png", "resolved_screenshot": "screenshots/screenshot_20250806_185155.png", "clean_action_id": "screenshot_20250806_185155", "prefixed_action_id": "al_screenshot_20250806_185155", "action_id_screenshot": "screenshots/screenshot_20250806_185155.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "2679ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Wait till accessibility_id=btneditFlybuysCard", "status": "passed", "duration": "3247ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btneditFlybuysCard", "status": "passed", "duration": "4169ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Remove card", "status": "passed", "duration": "4141ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnRemove", "status": "passed", "duration": "4189ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "2767ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with accessibility_id: btnLinkFlyBuys", "status": "passed", "duration": "4210ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Flybuys barcode number", "status": "passed", "duration": "4216ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Input text: \"2791234567890\"", "status": "passed", "duration": "1678ms", "action_id": "2791234567", "screenshot_filename": "2791234567.png", "report_screenshot": "2791234567.png", "resolved_screenshot": "screenshots/2791234567.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4320ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveFlybuysCard", "status": "passed", "duration": "4171ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2360ms", "action_id": "screenshot_20250806_180011", "screenshot_filename": "screenshot_20250806_180011.png", "report_screenshot": "screenshot_20250806_180011.png", "resolved_screenshot": "screenshots/screenshot_20250806_180011.png", "clean_action_id": "screenshot_20250806_180011", "prefixed_action_id": "al_screenshot_20250806_180011", "action_id_screenshot": "screenshots/screenshot_20250806_180011.png"}, {"name": "Check if element with accessibility_id=\"txtMy Flybuys card\" exists", "status": "passed", "duration": "2051ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5449ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on Text: \"locator\"", "status": "passed", "duration": "2586ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3262ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Tap and Type at (env[store-locator-x], env[store-locator-y]): \"env[store-locator-postcode]\"", "status": "passed", "duration": "5329ms", "action_id": "screenshot_20250806_185753", "screenshot_filename": "screenshot_20250806_185753.png", "report_screenshot": "screenshot_20250806_185753.png", "resolved_screenshot": "screenshots/screenshot_20250806_185753.png", "clean_action_id": "screenshot_20250806_185753", "prefixed_action_id": "al_screenshot_20250806_185753", "action_id_screenshot": "screenshots/screenshot_20250806_185753.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3178ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Melbourne Cbd\"]\" exists", "status": "passed", "duration": "1703ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2309ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "Tap on Text: \"Invite\"", "status": "passed", "duration": "2614ms", "action_id": "screenshot_20250806_183450", "screenshot_filename": "screenshot_20250806_183450.png", "report_screenshot": "screenshot_20250806_183450.png", "resolved_screenshot": "screenshots/screenshot_20250806_183450.png", "clean_action_id": "screenshot_20250806_183450", "prefixed_action_id": "al_screenshot_20250806_183450", "action_id_screenshot": "screenshots/screenshot_20250806_183450.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[contains(@name,\"I’m loving the new Kmart app\")]\" exists", "status": "passed", "duration": "1774ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2815ms", "action_id": "screenshot_20250806_173947", "screenshot_filename": "screenshot_20250806_173947.png", "report_screenshot": "screenshot_20250806_173947.png", "resolved_screenshot": "screenshots/screenshot_20250806_173947.png", "clean_action_id": "screenshot_20250806_173947", "prefixed_action_id": "al_screenshot_20250806_173947", "action_id_screenshot": "screenshots/screenshot_20250806_173947.png"}, {"name": "Tap on Text: \"Customer\"", "status": "passed", "duration": "2611ms", "action_id": "screenshot_20250806_180617", "screenshot_filename": "screenshot_20250806_180617.png", "report_screenshot": "screenshot_20250806_180617.png", "resolved_screenshot": "screenshots/screenshot_20250806_180617.png", "clean_action_id": "screenshot_20250806_180617", "prefixed_action_id": "al_screenshot_20250806_180617", "action_id_screenshot": "screenshots/screenshot_20250806_180617.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2388ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5425ms", "action_id": "screenshot_20250806_184840", "screenshot_filename": "screenshot_20250806_184840.png", "report_screenshot": "screenshot_20250806_184840.png", "resolved_screenshot": "screenshots/screenshot_20250806_184840.png", "clean_action_id": "screenshot_20250806_184840", "prefixed_action_id": "al_screenshot_20250806_184840", "action_id_screenshot": "screenshots/screenshot_20250806_184840.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2589ms", "action_id": "screenshot_20250806_174231", "screenshot_filename": "screenshot_20250806_174231.png", "report_screenshot": "screenshot_20250806_174231.png", "resolved_screenshot": "screenshots/screenshot_20250806_174231.png", "clean_action_id": "screenshot_20250806_174231", "prefixed_action_id": "al_screenshot_20250806_174231", "action_id_screenshot": "screenshots/screenshot_20250806_174231.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Others\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            43 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2176ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "status": "passed", "duration": "2502ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "513ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"Barcode Scanner\"]\" exists", "status": "passed", "duration": "1335ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"imgHelp\"]\" exists", "status": "passed", "duration": "1339ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]\" exists", "status": "passed", "duration": "1369ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2758ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2625ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "status": "passed", "duration": "2334ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Order number\"]", "status": "passed", "duration": "2356ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"env[searchorder]\"", "status": "passed", "duration": "1637ms", "action_id": "searchorde", "screenshot_filename": "searchorde.png", "report_screenshot": "searchorde.png", "resolved_screenshot": "screenshots/searchorde.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email Address\"] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2489ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"<EMAIL>\"", "status": "passed", "duration": "1828ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Search for order\"]", "status": "passed", "duration": "2488ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"refunded\"]\" exists", "status": "passed", "duration": "1516ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2339ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2319ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1210ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2705ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"<EMAIL>\"", "status": "passed", "duration": "3275ms", "action_id": "kiransah<PERSON>", "screenshot_filename": "kiransahoo.png", "report_screenshot": "kiransahoo.png", "resolved_screenshot": "screenshots/kiransahoo.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2691ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"Wonderbaby@5\"", "status": "passed", "duration": "3058ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]\" exists", "status": "passed", "duration": "2679ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "status": "passed", "duration": "2354ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]\" exists", "status": "passed", "duration": "1414ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2305ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Tap on Text: \"receipts\"", "status": "passed", "duration": "2642ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Tap on Text: \"Store\"", "status": "passed", "duration": "2825ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Check if element with xpath=\"(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]\" exists", "status": "passed", "duration": "1536ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2498ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeOther[@name=\"<PERSON>ly<PERSON> Receipt\"]/XCUIElementTypeOther[2]\" exists", "status": "passed", "duration": "1979ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "3120ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3277ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3075ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "2832ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"btnUpdate\"]\"", "status": "passed", "duration": "11045ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[device-back-img]", "status": "passed", "duration": "2264ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Save my location\"]\"", "status": "passed", "duration": "10236ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2596ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2793ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2358ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1060ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "23226ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Postcode Flow\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            52 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2169ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "6660ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1209ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "2102ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2090ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[contains(@name,\"Deliver\")]", "status": "passed", "duration": "2519ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4187ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3365ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "2896ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3239ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2860ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Tap if locator exists: accessibility_id=\"btnUpdate\"", "status": "passed", "duration": "5345ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "7097ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "iOS Function: text - Text: \"Uno card\"", "status": "passed", "duration": "2647ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "2510ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3306ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Wait till accessibility_id=btnCurrentLocationButton", "status": "passed", "duration": "3233ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "2840ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3239ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2818ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Check if element with text=\"Tarneit\" exists", "status": "passed", "duration": "13076ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2054ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2623ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "3429ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "3357ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap on element with accessibility_id: Search suburb or postcode", "status": "passed", "duration": "4156ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "textClear action", "status": "passed", "duration": "3341ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "2944ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "3240ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Save\"", "status": "passed", "duration": "2934ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "Check if element with text=\"Broadway\" exists", "status": "passed", "duration": "11459ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "16686ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]\" exists", "status": "passed", "duration": "1847ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2844ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10377ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Click & Collect\"]", "status": "passed", "duration": "2394ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "status": "passed", "duration": "1869ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "3174ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with accessibility_id: delete", "status": "passed", "duration": "4781ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): \"3000\"", "status": "passed", "duration": "5308ms", "action_id": "screenshot_20250806_185155", "screenshot_filename": "screenshot_20250806_185155.png", "report_screenshot": "screenshot_20250806_185155.png", "resolved_screenshot": "screenshots/screenshot_20250806_185155.png", "clean_action_id": "screenshot_20250806_185155", "prefixed_action_id": "al_screenshot_20250806_185155", "action_id_screenshot": "screenshots/screenshot_20250806_185155.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "3130ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on element with accessibility_id: Done", "status": "passed", "duration": "4619ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2873ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2437ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "1755ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "2271ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with text=\"Melbourne\" exists", "status": "passed", "duration": "14242ms", "action_id": "screenshot_20250806_180011", "screenshot_filename": "screenshot_20250806_180011.png", "report_screenshot": "screenshot_20250806_180011.png", "resolved_screenshot": "screenshots/screenshot_20250806_180011.png", "clean_action_id": "screenshot_20250806_180011", "prefixed_action_id": "al_screenshot_20250806_180011", "action_id_screenshot": "screenshots/screenshot_20250806_180011.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2867ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "5520ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "2363ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "App Settings AU\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            68 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2184ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "3596ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1195ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Terminate app: com.apple.Preferences", "status": "passed", "duration": "162ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "1256ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Tap on Text: \"Wi-Fi\"", "status": "passed", "duration": "2194ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5020ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "1000ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3208ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "268ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "761ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "224ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "status": "passed", "duration": "673ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "232ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "663ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "192ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Launch app: com.apple.Preferences", "status": "passed", "duration": "130ms", "action_id": "Preference", "screenshot_filename": "Preference.png", "report_screenshot": "Preference.png", "resolved_screenshot": "screenshots/Preference.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5013ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "status": "passed", "duration": "792ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3262ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "3926ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2832ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2556ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Restart app: com.apple.mobilesafari", "status": "passed", "duration": "2207ms", "action_id": "mobilesafa", "screenshot_filename": "mobilesafa.png", "report_screenshot": "mobilesafa.png", "resolved_screenshot": "screenshots/mobilesafa.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "status": "passed", "duration": "1274ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"kmart au\"", "status": "passed", "duration": "1826ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "status": "passed", "duration": "1302ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]\" exists", "status": "passed", "duration": "1541ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "status": "passed", "duration": "2450ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "2132ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2554ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21043ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2791ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "3892ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2393ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "7593ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "6322ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "status": "passed", "duration": "3596ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Chat now\"]/preceding-sibling::XCUIElementTypeImage[1]\"", "status": "passed", "duration": "11154ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "2649ms", "action_id": "screenshot_20250806_185155", "screenshot_filename": "screenshot_20250806_185155.png", "report_screenshot": "screenshot_20250806_185155.png", "resolved_screenshot": "screenshots/screenshot_20250806_185155.png", "clean_action_id": "screenshot_20250806_185155", "prefixed_action_id": "al_screenshot_20250806_185155", "action_id_screenshot": "screenshots/screenshot_20250806_185155.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]/XCUIElementTypeImage[1]\"", "status": "passed", "duration": "21415ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[catalogue-menu-img]", "status": "passed", "duration": "2834ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "Tap on Text: \"List\"", "status": "passed", "duration": "3951ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Kmart Catalogue\"]//XCUIElementTypeStaticText[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2416ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "16578ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "3471ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10894ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Increase quantity\"]", "status": "passed", "duration": "2475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Decrease quantity\"]", "status": "passed", "duration": "2734ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "5346ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2216ms", "action_id": "screenshot_20250806_180011", "screenshot_filename": "screenshot_20250806_180011.png", "report_screenshot": "screenshot_20250806_180011.png", "resolved_screenshot": "screenshots/screenshot_20250806_180011.png", "clean_action_id": "screenshot_20250806_180011", "prefixed_action_id": "al_screenshot_20250806_180011", "action_id_screenshot": "screenshots/screenshot_20250806_180011.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "status": "passed", "duration": "2884ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]\" is visible", "status": "passed", "duration": "4876ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Home & Living\"]", "status": "passed", "duration": "2486ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "status": "passed", "duration": "2491ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,\"$\")])[1]", "status": "passed", "duration": "2352ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "6844ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Tap if locator exists: accessibility_id=\"Add to bag\"", "status": "passed", "duration": "7238ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3366ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "status": "passed", "duration": "3060ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11377ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 50%) to (50%, 30%)", "status": "passed", "duration": "3816ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[contains(@name,\"Remove\")]\"", "status": "passed", "duration": "3633ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "screenshot_20250806_185753", "screenshot_filename": "screenshot_20250806_185753.png", "report_screenshot": "screenshot_20250806_185753.png", "resolved_screenshot": "screenshots/screenshot_20250806_185753.png", "clean_action_id": "screenshot_20250806_185753", "prefixed_action_id": "al_screenshot_20250806_185753", "action_id_screenshot": "screenshots/screenshot_20250806_185753.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "AU - Performance\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            69 actions", "status": "passed", "steps": [{"name": "Restart app: env[appid]", "status": "passed", "duration": "2179ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "3418ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeStaticText[@name=\"Chat now\"]/preceding-sibling::XCUIElementTypeImage[1]\"", "status": "passed", "duration": "11129ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "2685ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2560ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "18460ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "2979ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "Tap on Text: \"1800\"", "status": "passed", "duration": "2774ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Tap on Text: \"+61\"", "status": "passed", "duration": "2213ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Launch app: env[appid]", "status": "passed", "duration": "113ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2382ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3513ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "iOS Function: text - Text: \"kids toys\"", "status": "passed", "duration": "2609ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Execute Test Case: Click_Paginations (8 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png"}, {"name": "Restart app: env[appid]", "status": "passed", "duration": "3514ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "3489ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2565ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Tap on Text: \"Age\"", "status": "passed", "duration": "2591ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap on Text: \"Months\"", "status": "passed", "duration": "2511ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "4178ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Swipe from (5%, 50%) to (90%, 50%)", "status": "passed", "duration": "3803ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2296ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4414ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1175ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2721ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[uname]\"", "status": "passed", "duration": "3257ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "passed", "duration": "2708ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text - Text: \"env[pwd]\"", "status": "passed", "duration": "3039ms", "action_id": "screenshot_20250806_185155", "screenshot_filename": "screenshot_20250806_185155.png", "report_screenshot": "screenshot_20250806_185155.png", "resolved_screenshot": "screenshots/screenshot_20250806_185155.png", "clean_action_id": "screenshot_20250806_185155", "prefixed_action_id": "al_screenshot_20250806_185155", "action_id_screenshot": "screenshots/screenshot_20250806_185155.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "4067ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3362ms", "action_id": "ysJIY9A3gq", "screenshot_filename": "ysJIY9A3gq.png", "report_screenshot": "ysJIY9A3gq.png", "resolved_screenshot": "screenshots/ysJIY9A3gq.png", "clean_action_id": "ysJIY9A3gq", "prefixed_action_id": "al_ysJIY9A3gq", "action_id_screenshot": "screenshots/ysJIY9A3gq.png"}, {"name": "iOS Function: text - Text: \"env[cooker-id]\"", "status": "passed", "duration": "2636ms", "action_id": "EO3cMmdUyM", "screenshot_filename": "EO3cMmdUyM.png", "report_screenshot": "EO3cMmdUyM.png", "resolved_screenshot": "screenshots/EO3cMmdUyM.png", "clean_action_id": "EO3cMmdUyM", "prefixed_action_id": "al_EO3cMmdUyM", "action_id_screenshot": "screenshots/EO3cMmdUyM.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1859ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2444ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "2695ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 40%)", "status": "passed", "duration": "4300ms", "action_id": "screenshot_20250806_180011", "screenshot_filename": "screenshot_20250806_180011.png", "report_screenshot": "screenshot_20250806_180011.png", "resolved_screenshot": "screenshots/screenshot_20250806_180011.png", "clean_action_id": "screenshot_20250806_180011", "prefixed_action_id": "al_screenshot_20250806_180011", "action_id_screenshot": "screenshots/screenshot_20250806_180011.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[contains(@name,\"Select to add\") and contains(@name,\"to wishlist\")]\"", "status": "passed", "duration": "10388ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2849ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (90%, 30%) to (30%, 30%)", "status": "passed", "duration": "2153ms", "action_id": "jmKjclMUWT", "screenshot_filename": "jmKjclMUWT.png", "report_screenshot": "jmKjclMUWT.png", "resolved_screenshot": "screenshots/jmKjclMUWT.png", "clean_action_id": "jmKjclMUWT", "prefixed_action_id": "al_jmKjclMUWT", "action_id_screenshot": "screenshots/jmKjclMUWT.png"}, {"name": "Swipe from (90%, 30%) to (30%, 30%)", "status": "passed", "duration": "2201ms", "action_id": "TAKgcEDqvz", "screenshot_filename": "TAKgcEDqvz.png", "report_screenshot": "TAKgcEDqvz.png", "resolved_screenshot": "screenshots/TAKgcEDqvz.png", "clean_action_id": "TAKgcEDqvz", "prefixed_action_id": "al_TAKgcEDqvz", "action_id_screenshot": "screenshots/TAKgcEDqvz.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2275ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2253ms", "action_id": "qcZQIqosdj", "screenshot_filename": "qcZQIqosdj.png", "report_screenshot": "qcZQIqosdj.png", "resolved_screenshot": "screenshots/qcZQIqosdj.png", "clean_action_id": "qcZQIqosdj", "prefixed_action_id": "al_qcZQIqosdj", "action_id_screenshot": "screenshots/qcZQIqosdj.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2572ms", "action_id": "screenshot_20250806_185753", "screenshot_filename": "screenshot_20250806_185753.png", "report_screenshot": "screenshot_20250806_185753.png", "resolved_screenshot": "screenshots/screenshot_20250806_185753.png", "clean_action_id": "screenshot_20250806_185753", "prefixed_action_id": "al_screenshot_20250806_185753", "action_id_screenshot": "screenshots/screenshot_20250806_185753.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "status": "passed", "duration": "2533ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtJoinTodayButton\"]", "status": "passed", "duration": "2475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1165ms", "action_id": "XLpUP3Wr93", "screenshot_filename": "XLpUP3Wr93.png", "report_screenshot": "XLpUP3Wr93.png", "resolved_screenshot": "screenshots/XLpUP3Wr93.png", "clean_action_id": "XLpUP3Wr93", "prefixed_action_id": "al_XLpUP3Wr93", "action_id_screenshot": "screenshots/XLpUP3Wr93.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1687ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Cancel\"", "status": "passed", "duration": "3367ms", "action_id": "rWuyGodCon", "screenshot_filename": "rWuyGodCon.png", "report_screenshot": "rWuyGodCon.png", "resolved_screenshot": "screenshots/rWuyGodCon.png", "clean_action_id": "rWuyGodCon", "prefixed_action_id": "al_rWuyGodCon", "action_id_screenshot": "screenshots/rWuyGodCon.png"}, {"name": "Wait till accessibility_id=txtHomeAccountCtaSignIn", "status": "passed", "duration": "3437ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Search and Add (Notebooks) (6 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_183450", "screenshot_filename": "screenshot_20250806_183450.png", "report_screenshot": "screenshot_20250806_183450.png", "resolved_screenshot": "screenshots/screenshot_20250806_183450.png", "clean_action_id": "screenshot_20250806_183450", "prefixed_action_id": "al_screenshot_20250806_183450", "action_id_screenshot": "screenshots/screenshot_20250806_183450.png"}, {"name": "Tap on Text: \"Click\"", "status": "passed", "duration": "2873ms", "action_id": "screenshot_20250806_173947", "screenshot_filename": "screenshot_20250806_173947.png", "report_screenshot": "screenshot_20250806_173947.png", "resolved_screenshot": "screenshots/screenshot_20250806_173947.png", "clean_action_id": "screenshot_20250806_173947", "prefixed_action_id": "al_screenshot_20250806_173947", "action_id_screenshot": "screenshots/screenshot_20250806_173947.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,\"store details\")])[1]", "status": "passed", "duration": "2524ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2441ms", "action_id": "screenshot_20250806_180617", "screenshot_filename": "screenshot_20250806_180617.png", "report_screenshot": "screenshot_20250806_180617.png", "resolved_screenshot": "screenshots/screenshot_20250806_180617.png", "clean_action_id": "screenshot_20250806_180617", "prefixed_action_id": "al_screenshot_20250806_180617", "action_id_screenshot": "screenshots/screenshot_20250806_180617.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]\" is visible", "status": "passed", "duration": "16756ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]", "status": "passed", "duration": "2502ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2344ms", "action_id": "H9fy9qcFbZ", "screenshot_filename": "H9fy9qcFbZ.png", "report_screenshot": "H9fy9qcFbZ.png", "resolved_screenshot": "screenshots/H9fy9qcFbZ.png", "clean_action_id": "H9fy9qcFbZ", "prefixed_action_id": "al_H9fy9qcFbZ", "action_id_screenshot": "screenshots/H9fy9qcFbZ.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"About KHub Stores\"]", "status": "passed", "duration": "2487ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2193ms", "action_id": "screenshot_20250806_184840", "screenshot_filename": "screenshot_20250806_184840.png", "report_screenshot": "screenshot_20250806_184840.png", "resolved_screenshot": "screenshots/screenshot_20250806_184840.png", "clean_action_id": "screenshot_20250806_184840", "prefixed_action_id": "al_screenshot_20250806_184840", "action_id_screenshot": "screenshots/screenshot_20250806_184840.png"}, {"name": "Swipe from (50%, 30%) to (50%, 70%)", "status": "passed", "duration": "2935ms", "action_id": "screenshot_20250806_174231", "screenshot_filename": "screenshot_20250806_174231.png", "report_screenshot": "screenshot_20250806_174231.png", "resolved_screenshot": "screenshots/screenshot_20250806_174231.png", "clean_action_id": "screenshot_20250806_174231", "prefixed_action_id": "al_screenshot_20250806_174231", "action_id_screenshot": "screenshots/screenshot_20250806_174231.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "13899ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2252ms", "action_id": "screenshot_20250806_181051", "screenshot_filename": "screenshot_20250806_181051.png", "report_screenshot": "screenshot_20250806_181051.png", "resolved_screenshot": "screenshots/screenshot_20250806_181051.png", "clean_action_id": "screenshot_20250806_181051", "prefixed_action_id": "al_screenshot_20250806_181051", "action_id_screenshot": "screenshots/screenshot_20250806_181051.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2612ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\"", "status": "passed", "duration": "11035ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Remove item\"]\"", "status": "passed", "duration": "10992ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "2265ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtMoreAccountJoinTodayButton\"]", "status": "passed", "duration": "2359ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1187ms", "action_id": "Pd7cReoJM6", "screenshot_filename": "Pd7cReoJM6.png", "report_screenshot": "Pd7cReoJM6.png", "resolved_screenshot": "screenshots/Pd7cReoJM6.png", "clean_action_id": "Pd7cReoJM6", "prefixed_action_id": "al_Pd7cReoJM6", "action_id_screenshot": "screenshots/Pd7cReoJM6.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Create account\"]\" exists", "status": "passed", "duration": "1707ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: env[appid]", "status": "passed", "duration": "1083ms", "action_id": "d6vTfR4Y0D", "screenshot_filename": "d6vTfR4Y0D.png", "report_screenshot": "d6vTfR4Y0D.png", "resolved_screenshot": "screenshots/d6vTfR4Y0D.png", "clean_action_id": "d6vTfR4Y0D", "prefixed_action_id": "al_d6vTfR4Y0D", "action_id_screenshot": "screenshots/d6vTfR4Y0D.png"}, {"name": "cleanupSteps action", "status": "passed", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "All Payments Check\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            57 actions", "status": "passed", "steps": [{"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "29ms", "action_id": "kAQ1yIIw3h", "screenshot_filename": "kAQ1yIIw3h.png", "report_screenshot": "kAQ1yIIw3h.png", "resolved_screenshot": "screenshots/kAQ1yIIw3h.png", "clean_action_id": "kAQ1yIIw3h", "prefixed_action_id": "al_kAQ1yIIw3h", "action_id_screenshot": "screenshots/kAQ1yIIw3h.png"}, {"name": "Restart app: au.com.kmart", "status": "passed", "duration": "2181ms", "action_id": "screenshot_20250806_183929", "screenshot_filename": "screenshot_20250806_183929.png", "report_screenshot": "screenshot_20250806_183929.png", "resolved_screenshot": "screenshots/screenshot_20250806_183929.png", "clean_action_id": "screenshot_20250806_183929", "prefixed_action_id": "al_screenshot_20250806_183929", "action_id_screenshot": "screenshots/screenshot_20250806_183929.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "6540ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1211ms", "action_id": "screenshot_20250806_174437", "screenshot_filename": "screenshot_20250806_174437.png", "report_screenshot": "screenshot_20250806_174437.png", "resolved_screenshot": "screenshots/screenshot_20250806_174437.png", "clean_action_id": "screenshot_20250806_174437", "prefixed_action_id": "al_screenshot_20250806_174437", "action_id_screenshot": "screenshots/screenshot_20250806_174437.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-Signin (5 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250806_181643", "screenshot_filename": "screenshot_20250806_181643.png", "report_screenshot": "screenshot_20250806_181643.png", "resolved_screenshot": "screenshots/screenshot_20250806_181643.png", "clean_action_id": "screenshot_20250806_181643", "prefixed_action_id": "al_screenshot_20250806_181643", "action_id_screenshot": "screenshots/screenshot_20250806_181643.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "status": "passed", "duration": "4501ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3441ms", "action_id": "screenshot_20250806_185343", "screenshot_filename": "screenshot_20250806_185343.png", "report_screenshot": "screenshot_20250806_185343.png", "resolved_screenshot": "screenshots/screenshot_20250806_185343.png", "clean_action_id": "screenshot_20250806_185343", "prefixed_action_id": "al_screenshot_20250806_185343", "action_id_screenshot": "screenshots/screenshot_20250806_185343.png"}, {"name": "iOS Function: text - Text: \"P_42691341\"", "status": "passed", "duration": "2668ms", "action_id": "screenshot_20250806_183732", "screenshot_filename": "screenshot_20250806_183732.png", "report_screenshot": "screenshot_20250806_183732.png", "resolved_screenshot": "screenshots/screenshot_20250806_183732.png", "clean_action_id": "screenshot_20250806_183732", "prefixed_action_id": "al_screenshot_20250806_183732", "action_id_screenshot": "screenshots/screenshot_20250806_183732.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1846ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "2436ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 50%)", "status": "passed", "duration": "3325ms", "action_id": "screenshot_20250806_180549", "screenshot_filename": "screenshot_20250806_180549.png", "report_screenshot": "screenshot_20250806_180549.png", "resolved_screenshot": "screenshots/screenshot_20250806_180549.png", "clean_action_id": "screenshot_20250806_180549", "prefixed_action_id": "al_screenshot_20250806_180549", "action_id_screenshot": "screenshots/screenshot_20250806_180549.png"}, {"name": "Tap on element with accessibility_id: Add to bag", "status": "passed", "duration": "5597ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2888ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "10677ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "1964ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2527ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Continue to details\"]\" is visible", "status": "passed", "duration": "26719ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Continue to details\"]", "status": "passed", "duration": "2479ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"First Name\"]", "status": "passed", "duration": "2503ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5085ms", "action_id": "njiHWyVooT", "screenshot_filename": "njiHWyVooT.png", "report_screenshot": "njiHWyVooT.png", "resolved_screenshot": "screenshots/njiHWyVooT.png", "clean_action_id": "njiHWyVooT", "prefixed_action_id": "al_njiHWyVooT", "action_id_screenshot": "screenshots/njiHWyVooT.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Last Name\"]", "status": "passed", "duration": "2512ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5050ms", "action_id": "screenshot_20250806_184713", "screenshot_filename": "screenshot_20250806_184713.png", "report_screenshot": "screenshot_20250806_184713.png", "resolved_screenshot": "screenshots/screenshot_20250806_184713.png", "clean_action_id": "screenshot_20250806_184713", "prefixed_action_id": "al_screenshot_20250806_184713", "action_id_screenshot": "screenshots/screenshot_20250806_184713.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "passed", "duration": "2461ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5790ms", "action_id": "screenshot_20250806_180944", "screenshot_filename": "screenshot_20250806_180944.png", "report_screenshot": "screenshot_20250806_180944.png", "resolved_screenshot": "screenshots/screenshot_20250806_180944.png", "clean_action_id": "screenshot_20250806_180944", "prefixed_action_id": "al_screenshot_20250806_180944", "action_id_screenshot": "screenshots/screenshot_20250806_180944.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Mobile number\"]", "status": "passed", "duration": "2447ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "textClear action", "status": "passed", "duration": "5306ms", "action_id": "DaVBARRwft", "screenshot_filename": "DaVBARRwft.png", "report_screenshot": "DaVBARRwft.png", "resolved_screenshot": "screenshots/DaVBARRwft.png", "clean_action_id": "DaVBARRwft", "prefixed_action_id": "al_DaVBARRwft", "action_id_screenshot": "screenshots/DaVBARRwft.png"}, {"name": "iOS Function: text - Text: \" \"", "status": "passed", "duration": "2545ms", "action_id": "screenshot_20250806_175517", "screenshot_filename": "screenshot_20250806_175517.png", "report_screenshot": "screenshot_20250806_175517.png", "resolved_screenshot": "screenshots/screenshot_20250806_175517.png", "clean_action_id": "screenshot_20250806_175517", "prefixed_action_id": "al_screenshot_20250806_175517", "action_id_screenshot": "screenshots/screenshot_20250806_175517.png"}, {"name": "Tap on Text: \"address\"", "status": "passed", "duration": "3253ms", "action_id": "screenshot_20250806_182821", "screenshot_filename": "screenshot_20250806_182821.png", "report_screenshot": "screenshot_20250806_182821.png", "resolved_screenshot": "screenshots/screenshot_20250806_182821.png", "clean_action_id": "screenshot_20250806_182821", "prefixed_action_id": "al_screenshot_20250806_182821", "action_id_screenshot": "screenshots/screenshot_20250806_182821.png"}, {"name": "Tap and Type at (54, 304): \"305 238 Flinders\"", "status": "passed", "duration": "5732ms", "action_id": "screenshot_20250806_175259", "screenshot_filename": "screenshot_20250806_175259.png", "report_screenshot": "screenshot_20250806_175259.png", "resolved_screenshot": "screenshots/screenshot_20250806_175259.png", "clean_action_id": "screenshot_20250806_175259", "prefixed_action_id": "al_screenshot_20250806_175259", "action_id_screenshot": "screenshots/screenshot_20250806_175259.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"<PERSON>\"]", "status": "passed", "duration": "2533ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: env[delivery-address-img]", "status": "passed", "duration": "2154ms", "action_id": "screenshot_20250806_174806", "screenshot_filename": "screenshot_20250806_174806.png", "report_screenshot": "screenshot_20250806_174806.png", "resolved_screenshot": "screenshots/screenshot_20250806_174806.png", "clean_action_id": "screenshot_20250806_174806", "prefixed_action_id": "al_screenshot_20250806_174806", "action_id_screenshot": "screenshots/screenshot_20250806_174806.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[@name=\"Continue to payment\"]\" is visible", "status": "passed", "duration": "6294ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Continue to payment\"]", "status": "passed", "duration": "2540ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "status": "passed", "duration": "6700ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeLink[@name=\"PayPal\"]\" is visible", "status": "passed", "duration": "6443ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"PayPal\"]", "status": "passed", "duration": "2463ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[contains(@name,\"PayPal\")]\" exists", "status": "passed", "duration": "1894ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"close\"]", "status": "passed", "duration": "2719ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther", "status": "passed", "duration": "2455ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeLink[@name=\"Pay in 4\"]", "status": "passed", "duration": "2447ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Pay in 4 with PayPal\"]\" exists", "status": "passed", "duration": "1604ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"close\"]", "status": "passed", "duration": "2530ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "status": "passed", "duration": "2475ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "status": "passed", "duration": "2451ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeImage[@name=\"Afterpay\"]\" exists", "status": "passed", "duration": "1494ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2570ms", "action_id": "screenshot_20250806_183256", "screenshot_filename": "screenshot_20250806_183256.png", "report_screenshot": "screenshot_20250806_183256.png", "resolved_screenshot": "screenshots/screenshot_20250806_183256.png", "clean_action_id": "screenshot_20250806_183256", "prefixed_action_id": "al_screenshot_20250806_183256", "action_id_screenshot": "screenshots/screenshot_20250806_183256.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther", "status": "passed", "duration": "2560ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "status": "passed", "duration": "2469ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"Sign in with your Zip account\"]\" exists", "status": "passed", "duration": "1612ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2621ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2342ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap if locator exists: xpath=\"//XCUIElementTypeButton[@name=\"Checkout\"]\"", "status": "passed", "duration": "11398ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Delivery\"]", "status": "passed", "duration": "2420ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe up till element xpath: \"//XCUIElementTypeButton[contains(@name,\"Remove\")]\" is visible", "status": "passed", "duration": "5672ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Remove\")]", "status": "passed", "duration": "2917ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "status": "passed", "duration": "3912ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: Kmart_AU_Cleanup (6 steps)", "status": "passed", "duration": "0ms", "action_id": "ly2oT3zqmf", "screenshot_filename": "ly2oT3zqmf.png", "report_screenshot": "ly2oT3zqmf.png", "resolved_screenshot": "screenshots/ly2oT3zqmf.png", "clean_action_id": "ly2oT3zqmf", "prefixed_action_id": "al_ly2oT3zqmf", "action_id_screenshot": "screenshots/ly2oT3zqmf.png"}]}], "passed": 11, "failed": 0, "skipped": 0, "status": "passed"}