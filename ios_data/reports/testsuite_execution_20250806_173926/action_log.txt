Action Log - 2025-08-06 19:03:20
================================================================================

[[19:03:18]] [INFO] Generating execution report...
[[19:03:18]] [SUCCESS] All tests passed successfully!
[[19:03:18]] [SUCCESS] Screenshot refreshed
[[19:03:18]] [INFO] Refreshing screenshot...
[[19:03:18]] [SUCCESS] Screenshot refreshed successfully
[[19:03:18]] [SUCCESS] Screenshot refreshed successfully
[[19:03:18]] [SUCCESS] Screenshot refreshed
[[19:03:18]] [INFO] Refreshing screenshot...
[[19:03:15]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[19:03:15]] [SUCCESS] Screenshot refreshed successfully
[[19:03:15]] [SUCCESS] Screenshot refreshed successfully
[[19:03:15]] [SUCCESS] Screenshot refreshed
[[19:03:15]] [INFO] Refreshing screenshot...
[[19:03:10]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[19:03:09]] [SUCCESS] Screenshot refreshed successfully
[[19:03:09]] [SUCCESS] Screenshot refreshed successfully
[[19:03:09]] [SUCCESS] Screenshot refreshed
[[19:03:09]] [INFO] Refreshing screenshot...
[[19:03:06]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[19:03:05]] [SUCCESS] Screenshot refreshed successfully
[[19:03:05]] [SUCCESS] Screenshot refreshed successfully
[[19:03:05]] [SUCCESS] Screenshot refreshed
[[19:03:05]] [INFO] Refreshing screenshot...
[[19:03:01]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[19:03:01]] [SUCCESS] Screenshot refreshed successfully
[[19:03:01]] [SUCCESS] Screenshot refreshed successfully
[[19:03:01]] [SUCCESS] Screenshot refreshed
[[19:03:01]] [INFO] Refreshing screenshot...
[[19:02:54]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[19:02:54]] [SUCCESS] Screenshot refreshed successfully
[[19:02:54]] [SUCCESS] Screenshot refreshed successfully
[[19:02:54]] [SUCCESS] Screenshot refreshed
[[19:02:54]] [INFO] Refreshing screenshot...
[[19:02:47]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[19:02:46]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[19:02:46]] [INFO] Loading steps for multiStep action: Kmart_AU_Cleanup
[[19:02:46]] [INFO] 4pFqgUdIwt=running
[[19:02:46]] [INFO] Executing action 643/643: Execute Test Case: Kmart_AU_Cleanup (6 steps)
[[19:02:45]] [SUCCESS] Screenshot refreshed successfully
[[19:02:45]] [SUCCESS] Screenshot refreshed successfully
[[19:02:45]] [SUCCESS] Screenshot refreshed
[[19:02:45]] [INFO] Refreshing screenshot...
[[19:02:45]] [INFO] q6kSH9e0MI=pass
[[19:02:39]] [INFO] q6kSH9e0MI=running
[[19:02:39]] [INFO] Executing action 642/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[19:02:39]] [SUCCESS] Screenshot refreshed successfully
[[19:02:39]] [SUCCESS] Screenshot refreshed successfully
[[19:02:39]] [SUCCESS] Screenshot refreshed
[[19:02:39]] [INFO] Refreshing screenshot...
[[19:02:39]] [INFO] a4pJa7EAyI=pass
[[19:02:34]] [INFO] a4pJa7EAyI=running
[[19:02:34]] [INFO] Executing action 641/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[19:02:34]] [SUCCESS] Screenshot refreshed successfully
[[19:02:34]] [SUCCESS] Screenshot refreshed successfully
[[19:02:34]] [SUCCESS] Screenshot refreshed
[[19:02:34]] [INFO] Refreshing screenshot...
[[19:02:34]] [INFO] 2bcxKJ2cPg=pass
[[19:02:27]] [INFO] 2bcxKJ2cPg=running
[[19:02:27]] [INFO] Executing action 640/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[19:02:26]] [SUCCESS] Screenshot refreshed successfully
[[19:02:26]] [SUCCESS] Screenshot refreshed successfully
[[19:02:26]] [SUCCESS] Screenshot refreshed
[[19:02:26]] [INFO] Refreshing screenshot...
[[19:02:26]] [INFO] aqBkqyVhrZ=pass
[[19:02:22]] [INFO] aqBkqyVhrZ=running
[[19:02:22]] [INFO] Executing action 639/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[19:02:22]] [SUCCESS] Screenshot refreshed successfully
[[19:02:22]] [SUCCESS] Screenshot refreshed successfully
[[19:02:21]] [SUCCESS] Screenshot refreshed
[[19:02:21]] [INFO] Refreshing screenshot...
[[19:02:21]] [INFO] wSHsGWAwPm=pass
[[19:02:08]] [INFO] wSHsGWAwPm=running
[[19:02:08]] [INFO] Executing action 638/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[19:02:08]] [SUCCESS] Screenshot refreshed successfully
[[19:02:08]] [SUCCESS] Screenshot refreshed successfully
[[19:02:08]] [SUCCESS] Screenshot refreshed
[[19:02:08]] [INFO] Refreshing screenshot...
[[19:02:08]] [INFO] gPYNwJ0HKo=pass
[[19:02:04]] [INFO] gPYNwJ0HKo=running
[[19:02:04]] [INFO] Executing action 637/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[19:02:04]] [SUCCESS] Screenshot refreshed successfully
[[19:02:04]] [SUCCESS] Screenshot refreshed successfully
[[19:02:04]] [SUCCESS] Screenshot refreshed
[[19:02:04]] [INFO] Refreshing screenshot...
[[19:02:04]] [INFO] vYLhraWpQm=pass
[[19:02:00]] [INFO] vYLhraWpQm=running
[[19:02:00]] [INFO] Executing action 636/643: Tap on image: banner-close-updated.png
[[19:01:59]] [SUCCESS] Screenshot refreshed successfully
[[19:01:59]] [SUCCESS] Screenshot refreshed successfully
[[19:01:59]] [SUCCESS] Screenshot refreshed
[[19:01:59]] [INFO] Refreshing screenshot...
[[19:01:59]] [INFO] TAKgcEDqvz=pass
[[19:01:56]] [INFO] TAKgcEDqvz=running
[[19:01:56]] [INFO] Executing action 635/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[19:01:56]] [SUCCESS] Screenshot refreshed successfully
[[19:01:56]] [SUCCESS] Screenshot refreshed successfully
[[19:01:56]] [SUCCESS] Screenshot refreshed
[[19:01:56]] [INFO] Refreshing screenshot...
[[19:01:56]] [INFO] UgjXUTZy7Z=pass
[[19:01:52]] [INFO] UgjXUTZy7Z=running
[[19:01:52]] [INFO] Executing action 634/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[19:01:52]] [SUCCESS] Screenshot refreshed successfully
[[19:01:52]] [SUCCESS] Screenshot refreshed successfully
[[19:01:51]] [SUCCESS] Screenshot refreshed
[[19:01:51]] [INFO] Refreshing screenshot...
[[19:01:51]] [INFO] YqmO7h7VP0=pass
[[19:01:47]] [INFO] YqmO7h7VP0=running
[[19:01:47]] [INFO] Executing action 633/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[19:00:54]] [SUCCESS] Screenshot refreshed successfully
[[19:00:54]] [SUCCESS] Screenshot refreshed successfully
[[19:00:53]] [SUCCESS] Screenshot refreshed
[[19:00:53]] [INFO] Refreshing screenshot...
[[19:00:53]] [INFO] vYLhraWpQm=pass
[[19:00:49]] [INFO] vYLhraWpQm=running
[[19:00:49]] [INFO] Executing action 632/643: Tap on image: banner-close-updated.png
[[19:00:49]] [SUCCESS] Screenshot refreshed successfully
[[19:00:49]] [SUCCESS] Screenshot refreshed successfully
[[19:00:49]] [SUCCESS] Screenshot refreshed
[[19:00:49]] [INFO] Refreshing screenshot...
[[19:00:49]] [INFO] lSG7un0qKK=pass
[[19:00:46]] [INFO] lSG7un0qKK=running
[[19:00:46]] [INFO] Executing action 631/643: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[19:00:03]] [SUCCESS] Screenshot refreshed successfully
[[19:00:03]] [SUCCESS] Screenshot refreshed successfully
[[19:00:03]] [SUCCESS] Screenshot refreshed
[[19:00:03]] [INFO] Refreshing screenshot...
[[19:00:03]] [INFO] 9Pwdq32eUk=pass
[[18:59:58]] [INFO] 9Pwdq32eUk=running
[[18:59:58]] [INFO] Executing action 630/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[18:59:31]] [SUCCESS] Screenshot refreshed successfully
[[18:59:31]] [SUCCESS] Screenshot refreshed successfully
[[18:59:31]] [SUCCESS] Screenshot refreshed
[[18:59:31]] [INFO] Refreshing screenshot...
[[18:59:31]] [INFO] YBT2MVclAv=pass
[[18:59:26]] [INFO] YBT2MVclAv=running
[[18:59:26]] [INFO] Executing action 629/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[18:59:16]] [SUCCESS] Screenshot refreshed successfully
[[18:59:16]] [SUCCESS] Screenshot refreshed successfully
[[18:59:16]] [SUCCESS] Screenshot refreshed
[[18:59:16]] [INFO] Refreshing screenshot...
[[18:59:16]] [INFO] TzPItWbvDR=pass
[[18:59:12]] [INFO] TzPItWbvDR=running
[[18:59:12]] [INFO] Executing action 628/643: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[18:59:10]] [SUCCESS] Screenshot refreshed successfully
[[18:59:10]] [SUCCESS] Screenshot refreshed successfully
[[18:59:10]] [SUCCESS] Screenshot refreshed
[[18:59:10]] [INFO] Refreshing screenshot...
[[18:59:10]] [INFO] wSdfNe4Kww=pass
[[18:59:07]] [INFO] wSdfNe4Kww=running
[[18:59:07]] [INFO] Executing action 627/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[18:59:03]] [SUCCESS] Screenshot refreshed successfully
[[18:59:03]] [SUCCESS] Screenshot refreshed successfully
[[18:59:03]] [SUCCESS] Screenshot refreshed
[[18:59:03]] [INFO] Refreshing screenshot...
[[18:59:03]] [INFO] GN587Y6VBQ=pass
[[18:58:59]] [INFO] GN587Y6VBQ=running
[[18:58:59]] [INFO] Executing action 626/643: Tap on element with xpath: //XCUIElementTypeLink[@name="Pay in 4"]
[[18:58:58]] [SUCCESS] Screenshot refreshed successfully
[[18:58:58]] [SUCCESS] Screenshot refreshed successfully
[[18:58:57]] [SUCCESS] Screenshot refreshed
[[18:58:57]] [INFO] Refreshing screenshot...
[[18:58:57]] [INFO] dkSs61jGvX=pass
[[18:58:53]] [INFO] dkSs61jGvX=running
[[18:58:53]] [INFO] Executing action 625/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[18:58:51]] [SUCCESS] Screenshot refreshed successfully
[[18:58:51]] [SUCCESS] Screenshot refreshed successfully
[[18:58:51]] [SUCCESS] Screenshot refreshed
[[18:58:51]] [INFO] Refreshing screenshot...
[[18:58:51]] [INFO] XLpUP3Wr93=pass
[[18:58:47]] [INFO] XLpUP3Wr93=running
[[18:58:47]] [INFO] Executing action 624/643: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[18:58:47]] [SUCCESS] Screenshot refreshed successfully
[[18:58:47]] [SUCCESS] Screenshot refreshed successfully
[[18:58:47]] [SUCCESS] Screenshot refreshed
[[18:58:47]] [INFO] Refreshing screenshot...
[[18:58:47]] [INFO] mfOWujfRpL=pass
[[18:58:43]] [INFO] mfOWujfRpL=running
[[18:58:43]] [INFO] Executing action 623/643: Check if element with xpath="//XCUIElementTypeStaticText[contains(@name,"PayPal")]" exists
[[18:58:43]] [SUCCESS] Screenshot refreshed successfully
[[18:58:43]] [SUCCESS] Screenshot refreshed successfully
[[18:58:43]] [SUCCESS] Screenshot refreshed
[[18:58:43]] [INFO] Refreshing screenshot...
[[18:58:43]] [INFO] ftA0OJvd0W=pass
[[18:58:39]] [INFO] ftA0OJvd0W=running
[[18:58:39]] [INFO] Executing action 622/643: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[18:58:39]] [SUCCESS] Screenshot refreshed successfully
[[18:58:39]] [SUCCESS] Screenshot refreshed successfully
[[18:58:39]] [SUCCESS] Screenshot refreshed
[[18:58:39]] [INFO] Refreshing screenshot...
[[18:58:39]] [INFO] CBBib3pFkq=pass
[[18:58:31]] [INFO] CBBib3pFkq=running
[[18:58:31]] [INFO] Executing action 621/643: Swipe up till element xpath: "//XCUIElementTypeLink[@name="PayPal"]" is visible
[[18:58:30]] [SUCCESS] Screenshot refreshed successfully
[[18:58:30]] [SUCCESS] Screenshot refreshed successfully
[[18:58:30]] [SUCCESS] Screenshot refreshed
[[18:58:30]] [INFO] Refreshing screenshot...
[[18:58:30]] [INFO] 6LQ5cq0f6N=pass
[[18:58:22]] [INFO] 6LQ5cq0f6N=running
[[18:58:22]] [INFO] Executing action 620/643: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[18:58:22]] [SUCCESS] Screenshot refreshed successfully
[[18:58:22]] [SUCCESS] Screenshot refreshed successfully
[[18:58:22]] [SUCCESS] Screenshot refreshed
[[18:58:22]] [INFO] Refreshing screenshot...
[[18:58:22]] [INFO] 1Lirmyxkft=pass
[[18:58:18]] [INFO] 1Lirmyxkft=running
[[18:58:18]] [INFO] Executing action 619/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[18:58:17]] [SUCCESS] Screenshot refreshed successfully
[[18:58:17]] [SUCCESS] Screenshot refreshed successfully
[[18:58:17]] [SUCCESS] Screenshot refreshed
[[18:58:17]] [INFO] Refreshing screenshot...
[[18:58:17]] [INFO] TTpwkHEyuE=pass
[[18:58:10]] [INFO] TTpwkHEyuE=running
[[18:58:10]] [INFO] Executing action 618/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to payment"]" is visible
[[18:58:09]] [SUCCESS] Screenshot refreshed successfully
[[18:58:09]] [SUCCESS] Screenshot refreshed successfully
[[18:58:09]] [SUCCESS] Screenshot refreshed
[[18:58:09]] [INFO] Refreshing screenshot...
[[18:58:09]] [INFO] mMnRNh3NEd=pass
[[18:58:06]] [INFO] mMnRNh3NEd=running
[[18:58:06]] [INFO] Executing action 617/643: Tap on image: env[delivery-address-img]
[[18:58:05]] [SUCCESS] Screenshot refreshed successfully
[[18:58:05]] [SUCCESS] Screenshot refreshed successfully
[[18:58:05]] [SUCCESS] Screenshot refreshed
[[18:58:05]] [INFO] Refreshing screenshot...
[[18:58:05]] [INFO] NcU6aex76k=pass
[[18:58:01]] [INFO] NcU6aex76k=running
[[18:58:01]] [INFO] Executing action 616/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:58:01]] [SUCCESS] Screenshot refreshed successfully
[[18:58:01]] [SUCCESS] Screenshot refreshed successfully
[[18:58:01]] [SUCCESS] Screenshot refreshed
[[18:58:01]] [INFO] Refreshing screenshot...
[[18:58:01]] [INFO] SQ1i1ElZCE=pass
[[18:57:54]] [INFO] SQ1i1ElZCE=running
[[18:57:54]] [INFO] Executing action 615/643: Tap and Type at (54, 304): "305 238 Flinders"
[[18:57:53]] [SUCCESS] Screenshot refreshed successfully
[[18:57:53]] [SUCCESS] Screenshot refreshed successfully
[[18:57:53]] [SUCCESS] Screenshot refreshed
[[18:57:53]] [INFO] Refreshing screenshot...
[[18:57:53]] [INFO] 5ZzW1VVSzy=pass
[[18:57:49]] [INFO] 5ZzW1VVSzy=running
[[18:57:49]] [INFO] Executing action 614/643: Tap on Text: "address"
[[18:57:48]] [SUCCESS] Screenshot refreshed successfully
[[18:57:48]] [SUCCESS] Screenshot refreshed successfully
[[18:57:48]] [SUCCESS] Screenshot refreshed
[[18:57:48]] [INFO] Refreshing screenshot...
[[18:57:48]] [INFO] kDpsm2D3xt=pass
[[18:57:44]] [INFO] kDpsm2D3xt=running
[[18:57:44]] [INFO] Executing action 613/643: iOS Function: text - Text: " "
[[18:57:44]] [SUCCESS] Screenshot refreshed successfully
[[18:57:44]] [SUCCESS] Screenshot refreshed successfully
[[18:57:44]] [SUCCESS] Screenshot refreshed
[[18:57:44]] [INFO] Refreshing screenshot...
[[18:57:44]] [INFO] SFj4Aa7RHQ=pass
[[18:57:37]] [INFO] SFj4Aa7RHQ=running
[[18:57:37]] [INFO] Executing action 612/643: textClear action
[[18:57:37]] [SUCCESS] Screenshot refreshed successfully
[[18:57:37]] [SUCCESS] Screenshot refreshed successfully
[[18:57:36]] [SUCCESS] Screenshot refreshed
[[18:57:36]] [INFO] Refreshing screenshot...
[[18:57:36]] [INFO] yi5EsHEFvc=pass
[[18:57:33]] [INFO] yi5EsHEFvc=running
[[18:57:33]] [INFO] Executing action 611/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[18:57:32]] [SUCCESS] Screenshot refreshed successfully
[[18:57:32]] [SUCCESS] Screenshot refreshed successfully
[[18:57:32]] [SUCCESS] Screenshot refreshed
[[18:57:32]] [INFO] Refreshing screenshot...
[[18:57:32]] [INFO] lWJtKSqlPS=pass
[[18:57:25]] [INFO] lWJtKSqlPS=running
[[18:57:25]] [INFO] Executing action 610/643: textClear action
[[18:57:25]] [SUCCESS] Screenshot refreshed successfully
[[18:57:25]] [SUCCESS] Screenshot refreshed successfully
[[18:57:25]] [SUCCESS] Screenshot refreshed
[[18:57:25]] [INFO] Refreshing screenshot...
[[18:57:25]] [INFO] 9B5MQGTmpP=pass
[[18:57:21]] [INFO] 9B5MQGTmpP=running
[[18:57:21]] [INFO] Executing action 609/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:57:20]] [SUCCESS] Screenshot refreshed successfully
[[18:57:20]] [SUCCESS] Screenshot refreshed successfully
[[18:57:20]] [SUCCESS] Screenshot refreshed
[[18:57:20]] [INFO] Refreshing screenshot...
[[18:57:20]] [INFO] QvuueoTR8W=pass
[[18:57:14]] [INFO] QvuueoTR8W=running
[[18:57:14]] [INFO] Executing action 608/643: textClear action
[[18:57:14]] [SUCCESS] Screenshot refreshed successfully
[[18:57:14]] [SUCCESS] Screenshot refreshed successfully
[[18:57:13]] [SUCCESS] Screenshot refreshed
[[18:57:13]] [INFO] Refreshing screenshot...
[[18:57:13]] [INFO] p8rfQL9ara=pass
[[18:57:10]] [INFO] p8rfQL9ara=running
[[18:57:10]] [INFO] Executing action 607/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[18:57:09]] [SUCCESS] Screenshot refreshed successfully
[[18:57:09]] [SUCCESS] Screenshot refreshed successfully
[[18:57:09]] [SUCCESS] Screenshot refreshed
[[18:57:09]] [INFO] Refreshing screenshot...
[[18:57:09]] [INFO] CLMmkV1OIM=pass
[[18:57:03]] [INFO] CLMmkV1OIM=running
[[18:57:03]] [INFO] Executing action 606/643: textClear action
[[18:57:02]] [SUCCESS] Screenshot refreshed successfully
[[18:57:02]] [SUCCESS] Screenshot refreshed successfully
[[18:57:02]] [SUCCESS] Screenshot refreshed
[[18:57:02]] [INFO] Refreshing screenshot...
[[18:57:02]] [INFO] h9trcMrvxt=pass
[[18:56:58]] [INFO] h9trcMrvxt=running
[[18:56:58]] [INFO] Executing action 605/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[18:56:58]] [SUCCESS] Screenshot refreshed successfully
[[18:56:58]] [SUCCESS] Screenshot refreshed successfully
[[18:56:58]] [SUCCESS] Screenshot refreshed
[[18:56:58]] [INFO] Refreshing screenshot...
[[18:56:58]] [INFO] Q5A0cNaJ24=pass
[[18:56:54]] [INFO] Q5A0cNaJ24=running
[[18:56:54]] [INFO] Executing action 604/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[18:56:54]] [SUCCESS] Screenshot refreshed successfully
[[18:56:54]] [SUCCESS] Screenshot refreshed successfully
[[18:56:53]] [SUCCESS] Screenshot refreshed
[[18:56:53]] [INFO] Refreshing screenshot...
[[18:56:53]] [INFO] xAa049Qgls=pass
[[18:56:25]] [INFO] xAa049Qgls=running
[[18:56:25]] [INFO] Executing action 603/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[18:56:25]] [SUCCESS] Screenshot refreshed successfully
[[18:56:25]] [SUCCESS] Screenshot refreshed successfully
[[18:56:25]] [SUCCESS] Screenshot refreshed
[[18:56:25]] [INFO] Refreshing screenshot...
[[18:56:25]] [INFO] hwdyCKFAUJ=pass
[[18:56:21]] [INFO] hwdyCKFAUJ=running
[[18:56:21]] [INFO] Executing action 602/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:56:21]] [SUCCESS] Screenshot refreshed successfully
[[18:56:21]] [SUCCESS] Screenshot refreshed successfully
[[18:56:20]] [SUCCESS] Screenshot refreshed
[[18:56:20]] [INFO] Refreshing screenshot...
[[18:56:20]] [INFO] aqBkqyVhrZ=pass
[[18:56:17]] [INFO] aqBkqyVhrZ=running
[[18:56:17]] [INFO] Executing action 601/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:56:17]] [SUCCESS] Screenshot refreshed successfully
[[18:56:17]] [SUCCESS] Screenshot refreshed successfully
[[18:56:17]] [SUCCESS] Screenshot refreshed
[[18:56:17]] [INFO] Refreshing screenshot...
[[18:56:17]] [INFO] E3RDcrIH6J=pass
[[18:56:05]] [INFO] E3RDcrIH6J=running
[[18:56:05]] [INFO] Executing action 600/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:56:04]] [SUCCESS] Screenshot refreshed successfully
[[18:56:04]] [SUCCESS] Screenshot refreshed successfully
[[18:56:04]] [SUCCESS] Screenshot refreshed
[[18:56:04]] [INFO] Refreshing screenshot...
[[18:56:04]] [INFO] gPYNwJ0HKo=pass
[[18:56:00]] [INFO] gPYNwJ0HKo=running
[[18:56:00]] [INFO] Executing action 599/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:56:00]] [SUCCESS] Screenshot refreshed successfully
[[18:56:00]] [SUCCESS] Screenshot refreshed successfully
[[18:55:59]] [SUCCESS] Screenshot refreshed
[[18:55:59]] [INFO] Refreshing screenshot...
[[18:55:59]] [INFO] VLrfDHfkI8=pass
[[18:55:52]] [INFO] VLrfDHfkI8=running
[[18:55:52]] [INFO] Executing action 598/643: Tap on element with accessibility_id: Add to bag
[[18:55:52]] [SUCCESS] Screenshot refreshed successfully
[[18:55:52]] [SUCCESS] Screenshot refreshed successfully
[[18:55:52]] [SUCCESS] Screenshot refreshed
[[18:55:52]] [INFO] Refreshing screenshot...
[[18:55:52]] [INFO] PzxTDnwsZ7=pass
[[18:55:47]] [INFO] PzxTDnwsZ7=running
[[18:55:47]] [INFO] Executing action 597/643: Swipe from (50%, 70%) to (50%, 50%)
[[18:55:47]] [SUCCESS] Screenshot refreshed successfully
[[18:55:47]] [SUCCESS] Screenshot refreshed successfully
[[18:55:47]] [SUCCESS] Screenshot refreshed
[[18:55:47]] [INFO] Refreshing screenshot...
[[18:55:47]] [INFO] 6GkdPPZo8e=pass
[[18:55:43]] [INFO] 6GkdPPZo8e=running
[[18:55:43]] [INFO] Executing action 596/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:55:43]] [SUCCESS] Screenshot refreshed successfully
[[18:55:43]] [SUCCESS] Screenshot refreshed successfully
[[18:55:42]] [SUCCESS] Screenshot refreshed
[[18:55:42]] [INFO] Refreshing screenshot...
[[18:55:42]] [INFO] FSM5PqLDko=pass
[[18:55:39]] [INFO] FSM5PqLDko=running
[[18:55:39]] [INFO] Executing action 595/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:55:39]] [SUCCESS] Screenshot refreshed successfully
[[18:55:39]] [SUCCESS] Screenshot refreshed successfully
[[18:55:39]] [SUCCESS] Screenshot refreshed
[[18:55:39]] [INFO] Refreshing screenshot...
[[18:55:39]] [INFO] GPTMDcrFC2=pass
[[18:55:35]] [INFO] GPTMDcrFC2=running
[[18:55:35]] [INFO] Executing action 594/643: iOS Function: text - Text: "P_42691341"
[[18:55:34]] [SUCCESS] Screenshot refreshed successfully
[[18:55:34]] [SUCCESS] Screenshot refreshed successfully
[[18:55:34]] [SUCCESS] Screenshot refreshed
[[18:55:34]] [INFO] Refreshing screenshot...
[[18:55:34]] [INFO] 91WZz4k3NI=pass
[[18:55:29]] [INFO] 91WZz4k3NI=running
[[18:55:29]] [INFO] Executing action 593/643: Tap on Text: "Find"
[[18:55:29]] [SUCCESS] Screenshot refreshed successfully
[[18:55:29]] [SUCCESS] Screenshot refreshed successfully
[[18:55:29]] [SUCCESS] Screenshot refreshed
[[18:55:29]] [INFO] Refreshing screenshot...
[[18:55:29]] [INFO] ACaNCAo69V=pass
[[18:55:23]] [SUCCESS] Screenshot refreshed successfully
[[18:55:23]] [SUCCESS] Screenshot refreshed successfully
[[18:55:23]] [INFO] ACaNCAo69V=running
[[18:55:23]] [INFO] Executing action 592/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[18:55:23]] [SUCCESS] Screenshot refreshed
[[18:55:23]] [INFO] Refreshing screenshot...
[[18:55:23]] [SUCCESS] Screenshot refreshed successfully
[[18:55:23]] [SUCCESS] Screenshot refreshed successfully
[[18:55:22]] [SUCCESS] Screenshot refreshed
[[18:55:22]] [INFO] Refreshing screenshot...
[[18:55:18]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:55:18]] [SUCCESS] Screenshot refreshed successfully
[[18:55:18]] [SUCCESS] Screenshot refreshed successfully
[[18:55:18]] [SUCCESS] Screenshot refreshed
[[18:55:18]] [INFO] Refreshing screenshot...
[[18:55:14]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:55:13]] [SUCCESS] Screenshot refreshed successfully
[[18:55:13]] [SUCCESS] Screenshot refreshed successfully
[[18:55:13]] [SUCCESS] Screenshot refreshed
[[18:55:13]] [INFO] Refreshing screenshot...
[[18:55:09]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:55:08]] [SUCCESS] Screenshot refreshed successfully
[[18:55:08]] [SUCCESS] Screenshot refreshed successfully
[[18:55:08]] [SUCCESS] Screenshot refreshed
[[18:55:08]] [INFO] Refreshing screenshot...
[[18:55:04]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:55:04]] [SUCCESS] Screenshot refreshed successfully
[[18:55:04]] [SUCCESS] Screenshot refreshed successfully
[[18:55:04]] [SUCCESS] Screenshot refreshed
[[18:55:04]] [INFO] Refreshing screenshot...
[[18:54:58]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:54:58]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:54:58]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:54:58]] [INFO] JEpLBji8jZ=running
[[18:54:58]] [INFO] Executing action 591/643: Execute Test Case: Kmart-Signin (5 steps)
[[18:54:58]] [SUCCESS] Screenshot refreshed successfully
[[18:54:58]] [SUCCESS] Screenshot refreshed successfully
[[18:54:57]] [SUCCESS] Screenshot refreshed
[[18:54:57]] [INFO] Refreshing screenshot...
[[18:54:57]] [INFO] TrbMRAIV8i=pass
[[18:54:55]] [INFO] TrbMRAIV8i=running
[[18:54:55]] [INFO] Executing action 590/643: iOS Function: alert_accept
[[18:54:55]] [SUCCESS] Screenshot refreshed successfully
[[18:54:55]] [SUCCESS] Screenshot refreshed successfully
[[18:54:54]] [SUCCESS] Screenshot refreshed
[[18:54:54]] [INFO] Refreshing screenshot...
[[18:54:54]] [INFO] MxtVneSHFi=pass
[[18:54:46]] [INFO] MxtVneSHFi=running
[[18:54:46]] [INFO] Executing action 589/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:54:46]] [SUCCESS] Screenshot refreshed successfully
[[18:54:46]] [SUCCESS] Screenshot refreshed successfully
[[18:54:46]] [SUCCESS] Screenshot refreshed
[[18:54:46]] [INFO] Refreshing screenshot...
[[18:54:46]] [INFO] 3uORTsBIAg=pass
[[18:54:42]] [INFO] 3uORTsBIAg=running
[[18:54:42]] [INFO] Executing action 588/643: Restart app: au.com.kmart
[[18:54:42]] [SUCCESS] Screenshot refreshed successfully
[[18:54:42]] [SUCCESS] Screenshot refreshed successfully
[[18:54:42]] [SUCCESS] Screenshot refreshed
[[18:54:42]] [INFO] Refreshing screenshot...
[[18:54:42]] [INFO] K8uGC1LDOS=pass
[[18:54:31]] [SUCCESS] Screenshot refreshed successfully
[[18:54:31]] [SUCCESS] Screenshot refreshed successfully
[[18:54:30]] [INFO] K8uGC1LDOS=running
[[18:54:30]] [INFO] Executing action 587/643: Terminate app: au.com.kmart
[[18:54:30]] [SUCCESS] Screenshot refreshed
[[18:54:30]] [INFO] Refreshing screenshot...
[[18:54:30]] [SUCCESS] Screenshot refreshed successfully
[[18:54:30]] [SUCCESS] Screenshot refreshed successfully
[[18:54:30]] [SUCCESS] Screenshot refreshed
[[18:54:30]] [INFO] Refreshing screenshot...
[[18:54:27]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:54:27]] [SUCCESS] Screenshot refreshed successfully
[[18:54:27]] [SUCCESS] Screenshot refreshed successfully
[[18:54:27]] [SUCCESS] Screenshot refreshed
[[18:54:27]] [INFO] Refreshing screenshot...
[[18:54:15]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:54:14]] [SUCCESS] Screenshot refreshed successfully
[[18:54:14]] [SUCCESS] Screenshot refreshed successfully
[[18:54:14]] [SUCCESS] Screenshot refreshed
[[18:54:14]] [INFO] Refreshing screenshot...
[[18:54:10]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:54:10]] [SUCCESS] Screenshot refreshed successfully
[[18:54:10]] [SUCCESS] Screenshot refreshed successfully
[[18:54:10]] [SUCCESS] Screenshot refreshed
[[18:54:10]] [INFO] Refreshing screenshot...
[[18:54:06]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:54:06]] [SUCCESS] Screenshot refreshed successfully
[[18:54:06]] [SUCCESS] Screenshot refreshed successfully
[[18:54:06]] [SUCCESS] Screenshot refreshed
[[18:54:06]] [INFO] Refreshing screenshot...
[[18:53:59]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:53:59]] [SUCCESS] Screenshot refreshed successfully
[[18:53:59]] [SUCCESS] Screenshot refreshed successfully
[[18:53:59]] [SUCCESS] Screenshot refreshed
[[18:53:59]] [INFO] Refreshing screenshot...
[[18:53:53]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:53:53]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:53:53]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:53:53]] [INFO] Ll4UlkE3L9=running
[[18:53:53]] [INFO] Executing action 586/643: cleanupSteps action
[[18:53:53]] [SUCCESS] Screenshot refreshed successfully
[[18:53:53]] [SUCCESS] Screenshot refreshed successfully
[[18:53:52]] [SUCCESS] Screenshot refreshed
[[18:53:52]] [INFO] Refreshing screenshot...
[[18:53:52]] [INFO] 25UEKPIknm=pass
[[18:53:50]] [INFO] 25UEKPIknm=running
[[18:53:50]] [INFO] Executing action 585/643: Terminate app: env[appid]
[[18:53:50]] [SUCCESS] Screenshot refreshed successfully
[[18:53:50]] [SUCCESS] Screenshot refreshed successfully
[[18:53:49]] [SUCCESS] Screenshot refreshed
[[18:53:49]] [INFO] Refreshing screenshot...
[[18:53:49]] [INFO] UqgDn5CuPY=pass
[[18:53:46]] [INFO] UqgDn5CuPY=running
[[18:53:46]] [INFO] Executing action 584/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[18:53:46]] [SUCCESS] Screenshot refreshed successfully
[[18:53:46]] [SUCCESS] Screenshot refreshed successfully
[[18:53:46]] [SUCCESS] Screenshot refreshed
[[18:53:46]] [INFO] Refreshing screenshot...
[[18:53:46]] [INFO] VfTTTtrliQ=pass
[[18:53:43]] [INFO] VfTTTtrliQ=running
[[18:53:43]] [INFO] Executing action 583/643: iOS Function: alert_accept
[[18:53:43]] [SUCCESS] Screenshot refreshed successfully
[[18:53:43]] [SUCCESS] Screenshot refreshed successfully
[[18:53:43]] [SUCCESS] Screenshot refreshed
[[18:53:43]] [INFO] Refreshing screenshot...
[[18:53:43]] [INFO] ipT2XD9io6=pass
[[18:53:39]] [INFO] ipT2XD9io6=running
[[18:53:39]] [INFO] Executing action 582/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountJoinTodayButton"]
[[18:53:39]] [SUCCESS] Screenshot refreshed successfully
[[18:53:39]] [SUCCESS] Screenshot refreshed successfully
[[18:53:39]] [SUCCESS] Screenshot refreshed
[[18:53:39]] [INFO] Refreshing screenshot...
[[18:53:39]] [INFO] OKCHAK6HCJ=pass
[[18:53:35]] [INFO] OKCHAK6HCJ=running
[[18:53:35]] [INFO] Executing action 581/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:53:35]] [SUCCESS] Screenshot refreshed successfully
[[18:53:35]] [SUCCESS] Screenshot refreshed successfully
[[18:53:34]] [SUCCESS] Screenshot refreshed
[[18:53:34]] [INFO] Refreshing screenshot...
[[18:53:34]] [INFO] VLlqyGmmr8=pass
[[18:53:22]] [INFO] VLlqyGmmr8=running
[[18:53:22]] [INFO] Executing action 580/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Remove item"]"
[[18:53:22]] [SUCCESS] Screenshot refreshed successfully
[[18:53:22]] [SUCCESS] Screenshot refreshed successfully
[[18:53:22]] [SUCCESS] Screenshot refreshed
[[18:53:22]] [INFO] Refreshing screenshot...
[[18:53:22]] [INFO] rWuyGodCon=pass
[[18:53:09]] [INFO] rWuyGodCon=running
[[18:53:09]] [INFO] Executing action 579/643: Tap if locator exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]"
[[18:53:09]] [SUCCESS] Screenshot refreshed successfully
[[18:53:09]] [SUCCESS] Screenshot refreshed successfully
[[18:53:09]] [SUCCESS] Screenshot refreshed
[[18:53:09]] [INFO] Refreshing screenshot...
[[18:53:09]] [INFO] HlpBHrQZnk=pass
[[18:53:05]] [INFO] HlpBHrQZnk=running
[[18:53:05]] [INFO] Executing action 578/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:53:05]] [SUCCESS] Screenshot refreshed successfully
[[18:53:05]] [SUCCESS] Screenshot refreshed successfully
[[18:53:04]] [SUCCESS] Screenshot refreshed
[[18:53:04]] [INFO] Refreshing screenshot...
[[18:53:04]] [INFO] AEnFqnkOa1=pass
[[18:53:01]] [INFO] AEnFqnkOa1=running
[[18:53:01]] [INFO] Executing action 577/643: Tap on image: banner-close-updated.png
[[18:53:00]] [SUCCESS] Screenshot refreshed successfully
[[18:53:00]] [SUCCESS] Screenshot refreshed successfully
[[18:53:00]] [SUCCESS] Screenshot refreshed
[[18:53:00]] [INFO] Refreshing screenshot...
[[18:53:00]] [INFO] z1CfcW4xYT=pass
[[18:52:45]] [INFO] z1CfcW4xYT=running
[[18:52:45]] [INFO] Executing action 576/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:52:45]] [SUCCESS] Screenshot refreshed successfully
[[18:52:45]] [SUCCESS] Screenshot refreshed successfully
[[18:52:44]] [SUCCESS] Screenshot refreshed
[[18:52:44]] [INFO] Refreshing screenshot...
[[18:52:44]] [INFO] dJNRgTXoqs=pass
[[18:52:40]] [INFO] dJNRgTXoqs=running
[[18:52:40]] [INFO] Executing action 575/643: Swipe from (50%, 30%) to (50%, 70%)
[[18:52:40]] [SUCCESS] Screenshot refreshed successfully
[[18:52:40]] [SUCCESS] Screenshot refreshed successfully
[[18:52:40]] [SUCCESS] Screenshot refreshed
[[18:52:40]] [INFO] Refreshing screenshot...
[[18:52:40]] [INFO] ceF4VRTJlO=pass
[[18:52:36]] [INFO] ceF4VRTJlO=running
[[18:52:36]] [INFO] Executing action 574/643: Tap on image: banner-close-updated.png
[[18:52:36]] [SUCCESS] Screenshot refreshed successfully
[[18:52:36]] [SUCCESS] Screenshot refreshed successfully
[[18:52:36]] [SUCCESS] Screenshot refreshed
[[18:52:36]] [INFO] Refreshing screenshot...
[[18:52:36]] [INFO] 8hCPyY2zPt=pass
[[18:52:32]] [INFO] 8hCPyY2zPt=running
[[18:52:32]] [INFO] Executing action 573/643: Tap on element with xpath: //XCUIElementTypeButton[@name="About KHub Stores"]
[[18:52:31]] [SUCCESS] Screenshot refreshed successfully
[[18:52:31]] [SUCCESS] Screenshot refreshed successfully
[[18:52:31]] [SUCCESS] Screenshot refreshed
[[18:52:31]] [INFO] Refreshing screenshot...
[[18:52:31]] [INFO] r0FfJ85LFM=pass
[[18:52:27]] [INFO] r0FfJ85LFM=running
[[18:52:27]] [INFO] Executing action 572/643: Tap on image: banner-close-updated.png
[[18:52:27]] [SUCCESS] Screenshot refreshed successfully
[[18:52:27]] [SUCCESS] Screenshot refreshed successfully
[[18:52:27]] [SUCCESS] Screenshot refreshed
[[18:52:27]] [INFO] Refreshing screenshot...
[[18:52:27]] [INFO] 2QEdm5WM18=pass
[[18:52:23]] [INFO] 2QEdm5WM18=running
[[18:52:23]] [INFO] Executing action 571/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Bags for Click & Collect orders"]
[[18:52:23]] [SUCCESS] Screenshot refreshed successfully
[[18:52:23]] [SUCCESS] Screenshot refreshed successfully
[[18:52:23]] [SUCCESS] Screenshot refreshed
[[18:52:23]] [INFO] Refreshing screenshot...
[[18:52:23]] [INFO] NW6M15JbAy=pass
[[18:52:05]] [INFO] NW6M15JbAy=running
[[18:52:05]] [INFO] Executing action 570/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Bags for Click & Collect orders"]" is visible
[[18:52:04]] [SUCCESS] Screenshot refreshed successfully
[[18:52:04]] [SUCCESS] Screenshot refreshed successfully
[[18:52:04]] [SUCCESS] Screenshot refreshed
[[18:52:04]] [INFO] Refreshing screenshot...
[[18:52:04]] [INFO] njiHWyVooT=pass
[[18:52:00]] [INFO] njiHWyVooT=running
[[18:52:00]] [INFO] Executing action 569/643: Tap on image: banner-close-updated.png
[[18:52:00]] [SUCCESS] Screenshot refreshed successfully
[[18:52:00]] [SUCCESS] Screenshot refreshed successfully
[[18:52:00]] [SUCCESS] Screenshot refreshed
[[18:52:00]] [INFO] Refreshing screenshot...
[[18:52:00]] [INFO] 93bAew9Y4Y=pass
[[18:51:56]] [INFO] 93bAew9Y4Y=running
[[18:51:56]] [INFO] Executing action 568/643: Tap on element with xpath: (//XCUIElementTypeButton[contains(@name,"store details")])[1]
[[18:51:55]] [SUCCESS] Screenshot refreshed successfully
[[18:51:55]] [SUCCESS] Screenshot refreshed successfully
[[18:51:55]] [SUCCESS] Screenshot refreshed
[[18:51:55]] [INFO] Refreshing screenshot...
[[18:51:55]] [INFO] rPQ5EkTza1=pass
[[18:51:51]] [SUCCESS] Screenshot refreshed successfully
[[18:51:51]] [SUCCESS] Screenshot refreshed successfully
[[18:51:51]] [INFO] rPQ5EkTza1=running
[[18:51:51]] [INFO] Executing action 567/643: Tap on Text: "Click"
[[18:51:51]] [SUCCESS] Screenshot refreshed
[[18:51:51]] [INFO] Refreshing screenshot...
[[18:51:51]] [SUCCESS] Screenshot refreshed successfully
[[18:51:51]] [SUCCESS] Screenshot refreshed successfully
[[18:51:51]] [SUCCESS] Screenshot refreshed
[[18:51:51]] [INFO] Refreshing screenshot...
[[18:51:47]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:51:47]] [SUCCESS] Screenshot refreshed successfully
[[18:51:47]] [SUCCESS] Screenshot refreshed successfully
[[18:51:47]] [SUCCESS] Screenshot refreshed
[[18:51:47]] [INFO] Refreshing screenshot...
[[18:51:35]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:51:35]] [SUCCESS] Screenshot refreshed successfully
[[18:51:35]] [SUCCESS] Screenshot refreshed successfully
[[18:51:35]] [SUCCESS] Screenshot refreshed
[[18:51:35]] [INFO] Refreshing screenshot...
[[18:51:28]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[18:51:28]] [SUCCESS] Screenshot refreshed successfully
[[18:51:28]] [SUCCESS] Screenshot refreshed successfully
[[18:51:28]] [SUCCESS] Screenshot refreshed
[[18:51:28]] [INFO] Refreshing screenshot...
[[18:51:24]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:51:24]] [SUCCESS] Screenshot refreshed successfully
[[18:51:24]] [SUCCESS] Screenshot refreshed successfully
[[18:51:23]] [SUCCESS] Screenshot refreshed
[[18:51:23]] [INFO] Refreshing screenshot...
[[18:51:19]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[18:51:19]] [SUCCESS] Screenshot refreshed successfully
[[18:51:19]] [SUCCESS] Screenshot refreshed successfully
[[18:51:19]] [SUCCESS] Screenshot refreshed
[[18:51:19]] [INFO] Refreshing screenshot...
[[18:51:15]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:51:15]] [SUCCESS] Screenshot refreshed successfully
[[18:51:15]] [SUCCESS] Screenshot refreshed successfully
[[18:51:14]] [SUCCESS] Screenshot refreshed
[[18:51:14]] [INFO] Refreshing screenshot...
[[18:51:10]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[18:51:10]] [SUCCESS] Screenshot refreshed successfully
[[18:51:10]] [SUCCESS] Screenshot refreshed successfully
[[18:51:10]] [SUCCESS] Screenshot refreshed
[[18:51:10]] [INFO] Refreshing screenshot...
[[18:51:03]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[18:51:03]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[18:51:03]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[18:51:03]] [INFO] 0YgZZfWdYY=running
[[18:51:03]] [INFO] Executing action 566/643: Execute Test Case: Search and Add (Notebooks) (6 steps)
[[18:51:03]] [SUCCESS] Screenshot refreshed successfully
[[18:51:03]] [SUCCESS] Screenshot refreshed successfully
[[18:51:02]] [SUCCESS] Screenshot refreshed
[[18:51:02]] [INFO] Refreshing screenshot...
[[18:51:02]] [INFO] arH1CZCPXh=pass
[[18:50:58]] [INFO] arH1CZCPXh=running
[[18:50:58]] [INFO] Executing action 565/643: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[18:50:57]] [SUCCESS] Screenshot refreshed successfully
[[18:50:57]] [SUCCESS] Screenshot refreshed successfully
[[18:50:57]] [SUCCESS] Screenshot refreshed
[[18:50:57]] [INFO] Refreshing screenshot...
[[18:50:57]] [INFO] JLAJhxPdsl=pass
[[18:50:52]] [INFO] JLAJhxPdsl=running
[[18:50:52]] [INFO] Executing action 564/643: Tap on Text: "Cancel"
[[18:50:52]] [SUCCESS] Screenshot refreshed successfully
[[18:50:52]] [SUCCESS] Screenshot refreshed successfully
[[18:50:52]] [SUCCESS] Screenshot refreshed
[[18:50:52]] [INFO] Refreshing screenshot...
[[18:50:52]] [INFO] UqgDn5CuPY=pass
[[18:50:49]] [INFO] UqgDn5CuPY=running
[[18:50:49]] [INFO] Executing action 563/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Create account"]" exists
[[18:50:48]] [SUCCESS] Screenshot refreshed successfully
[[18:50:48]] [SUCCESS] Screenshot refreshed successfully
[[18:50:48]] [SUCCESS] Screenshot refreshed
[[18:50:48]] [INFO] Refreshing screenshot...
[[18:50:48]] [INFO] VfTTTtrliQ=pass
[[18:50:46]] [INFO] VfTTTtrliQ=running
[[18:50:46]] [INFO] Executing action 562/643: iOS Function: alert_accept
[[18:50:46]] [SUCCESS] Screenshot refreshed successfully
[[18:50:46]] [SUCCESS] Screenshot refreshed successfully
[[18:50:45]] [SUCCESS] Screenshot refreshed
[[18:50:45]] [INFO] Refreshing screenshot...
[[18:50:45]] [INFO] ipT2XD9io6=pass
[[18:50:41]] [INFO] ipT2XD9io6=running
[[18:50:41]] [INFO] Executing action 561/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtJoinTodayButton"]
[[18:50:41]] [SUCCESS] Screenshot refreshed successfully
[[18:50:41]] [SUCCESS] Screenshot refreshed successfully
[[18:50:41]] [SUCCESS] Screenshot refreshed
[[18:50:41]] [INFO] Refreshing screenshot...
[[18:50:41]] [INFO] OKCHAK6HCJ=pass
[[18:50:37]] [INFO] OKCHAK6HCJ=running
[[18:50:37]] [INFO] Executing action 560/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:50:37]] [SUCCESS] Screenshot refreshed successfully
[[18:50:37]] [SUCCESS] Screenshot refreshed successfully
[[18:50:36]] [SUCCESS] Screenshot refreshed
[[18:50:36]] [INFO] Refreshing screenshot...
[[18:50:36]] [INFO] RbD937Xbte=pass
[[18:50:32]] [INFO] RbD937Xbte=running
[[18:50:32]] [INFO] Executing action 559/643: Tap on Text: "out"
[[18:50:32]] [SUCCESS] Screenshot refreshed successfully
[[18:50:32]] [SUCCESS] Screenshot refreshed successfully
[[18:50:32]] [SUCCESS] Screenshot refreshed
[[18:50:32]] [INFO] Refreshing screenshot...
[[18:50:32]] [INFO] ylslyLAYKb=pass
[[18:50:28]] [INFO] ylslyLAYKb=running
[[18:50:28]] [INFO] Executing action 558/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:50:28]] [SUCCESS] Screenshot refreshed successfully
[[18:50:28]] [SUCCESS] Screenshot refreshed successfully
[[18:50:28]] [SUCCESS] Screenshot refreshed
[[18:50:28]] [INFO] Refreshing screenshot...
[[18:50:28]] [INFO] wguGCt7OoB=pass
[[18:50:24]] [INFO] wguGCt7OoB=running
[[18:50:24]] [INFO] Executing action 557/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:50:24]] [SUCCESS] Screenshot refreshed successfully
[[18:50:24]] [SUCCESS] Screenshot refreshed successfully
[[18:50:24]] [SUCCESS] Screenshot refreshed
[[18:50:24]] [INFO] Refreshing screenshot...
[[18:50:24]] [INFO] RDQCFIxjA0=pass
[[18:50:20]] [INFO] RDQCFIxjA0=running
[[18:50:20]] [INFO] Executing action 556/643: Swipe from (90%, 30%) to (30%, 30%)
[[18:50:20]] [SUCCESS] Screenshot refreshed successfully
[[18:50:20]] [SUCCESS] Screenshot refreshed successfully
[[18:50:20]] [SUCCESS] Screenshot refreshed
[[18:50:20]] [INFO] Refreshing screenshot...
[[18:50:20]] [INFO] x4Mid4HQ0Z=pass
[[18:50:16]] [INFO] x4Mid4HQ0Z=running
[[18:50:16]] [INFO] Executing action 555/643: Swipe from (90%, 30%) to (30%, 30%)
[[18:50:16]] [SUCCESS] Screenshot refreshed successfully
[[18:50:16]] [SUCCESS] Screenshot refreshed successfully
[[18:50:16]] [SUCCESS] Screenshot refreshed
[[18:50:16]] [INFO] Refreshing screenshot...
[[18:50:16]] [INFO] wguGCt7OoB=pass
[[18:50:11]] [INFO] wguGCt7OoB=running
[[18:50:11]] [INFO] Executing action 554/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:50:11]] [SUCCESS] Screenshot refreshed successfully
[[18:50:11]] [SUCCESS] Screenshot refreshed successfully
[[18:50:11]] [SUCCESS] Screenshot refreshed
[[18:50:11]] [INFO] Refreshing screenshot...
[[18:50:11]] [INFO] 39pu9NW124=pass
[[18:49:59]] [INFO] 39pu9NW124=running
[[18:49:59]] [INFO] Executing action 553/643: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Select to add") and contains(@name,"to wishlist")]"
[[18:49:59]] [SUCCESS] Screenshot refreshed successfully
[[18:49:59]] [SUCCESS] Screenshot refreshed successfully
[[18:49:59]] [SUCCESS] Screenshot refreshed
[[18:49:59]] [INFO] Refreshing screenshot...
[[18:49:59]] [INFO] ylslyLAYKb=pass
[[18:49:53]] [INFO] ylslyLAYKb=running
[[18:49:53]] [INFO] Executing action 552/643: Swipe from (50%, 70%) to (50%, 40%)
[[18:49:53]] [SUCCESS] Screenshot refreshed successfully
[[18:49:53]] [SUCCESS] Screenshot refreshed successfully
[[18:49:53]] [SUCCESS] Screenshot refreshed
[[18:49:53]] [INFO] Refreshing screenshot...
[[18:49:53]] [INFO] 0bnBNoqPt8=pass
[[18:49:48]] [INFO] 0bnBNoqPt8=running
[[18:49:48]] [INFO] Executing action 551/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:49:48]] [SUCCESS] Screenshot refreshed successfully
[[18:49:48]] [SUCCESS] Screenshot refreshed successfully
[[18:49:48]] [SUCCESS] Screenshot refreshed
[[18:49:48]] [INFO] Refreshing screenshot...
[[18:49:48]] [INFO] xmelRkcdVx=pass
[[18:49:44]] [INFO] xmelRkcdVx=running
[[18:49:44]] [INFO] Executing action 550/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:49:44]] [SUCCESS] Screenshot refreshed successfully
[[18:49:44]] [SUCCESS] Screenshot refreshed successfully
[[18:49:44]] [SUCCESS] Screenshot refreshed
[[18:49:44]] [INFO] Refreshing screenshot...
[[18:49:44]] [INFO] ksCBjJiwHZ=pass
[[18:49:40]] [INFO] ksCBjJiwHZ=running
[[18:49:40]] [INFO] Executing action 549/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:49:40]] [SUCCESS] Screenshot refreshed successfully
[[18:49:40]] [SUCCESS] Screenshot refreshed successfully
[[18:49:40]] [SUCCESS] Screenshot refreshed
[[18:49:40]] [INFO] Refreshing screenshot...
[[18:49:40]] [INFO] d40Oo7famr=pass
[[18:49:36]] [INFO] d40Oo7famr=running
[[18:49:36]] [INFO] Executing action 548/643: iOS Function: text - Text: "env[cooker-id]"
[[18:49:36]] [SUCCESS] Screenshot refreshed successfully
[[18:49:36]] [SUCCESS] Screenshot refreshed successfully
[[18:49:35]] [SUCCESS] Screenshot refreshed
[[18:49:35]] [INFO] Refreshing screenshot...
[[18:49:35]] [INFO] ewuLtuqVuo=pass
[[18:49:31]] [INFO] ewuLtuqVuo=running
[[18:49:31]] [INFO] Executing action 547/643: Tap on Text: "Find"
[[18:49:31]] [SUCCESS] Screenshot refreshed successfully
[[18:49:31]] [SUCCESS] Screenshot refreshed successfully
[[18:49:30]] [SUCCESS] Screenshot refreshed
[[18:49:30]] [INFO] Refreshing screenshot...
[[18:49:30]] [INFO] GTXmST3hEA=pass
[[18:49:25]] [INFO] GTXmST3hEA=running
[[18:49:25]] [INFO] Executing action 546/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:49:25]] [SUCCESS] Screenshot refreshed successfully
[[18:49:25]] [SUCCESS] Screenshot refreshed successfully
[[18:49:24]] [SUCCESS] Screenshot refreshed
[[18:49:24]] [INFO] Refreshing screenshot...
[[18:49:24]] [INFO] qkZ5KShdEU=pass
[[18:49:20]] [INFO] qkZ5KShdEU=running
[[18:49:20]] [INFO] Executing action 545/643: iOS Function: text - Text: "env[pwd]"
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:20]] [SUCCESS] Screenshot refreshed successfully
[[18:49:19]] [SUCCESS] Screenshot refreshed
[[18:49:19]] [INFO] Refreshing screenshot...
[[18:49:19]] [INFO] 7g2LmvjtEZ=pass
[[18:49:15]] [INFO] 7g2LmvjtEZ=running
[[18:49:15]] [INFO] Executing action 544/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:49:15]] [SUCCESS] Screenshot refreshed successfully
[[18:49:15]] [SUCCESS] Screenshot refreshed successfully
[[18:49:15]] [SUCCESS] Screenshot refreshed
[[18:49:15]] [INFO] Refreshing screenshot...
[[18:49:15]] [INFO] OUT2ASweb6=pass
[[18:49:10]] [INFO] OUT2ASweb6=running
[[18:49:10]] [INFO] Executing action 543/643: iOS Function: text - Text: "env[uname]"
[[18:49:10]] [SUCCESS] Screenshot refreshed successfully
[[18:49:10]] [SUCCESS] Screenshot refreshed successfully
[[18:49:10]] [SUCCESS] Screenshot refreshed
[[18:49:10]] [INFO] Refreshing screenshot...
[[18:49:10]] [INFO] TV4kJIIV9v=pass
[[18:49:06]] [INFO] TV4kJIIV9v=running
[[18:49:06]] [INFO] Executing action 542/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:49:05]] [SUCCESS] Screenshot refreshed successfully
[[18:49:05]] [SUCCESS] Screenshot refreshed successfully
[[18:49:05]] [SUCCESS] Screenshot refreshed
[[18:49:05]] [INFO] Refreshing screenshot...
[[18:49:05]] [INFO] kQJbqm7uCi=pass
[[18:49:03]] [INFO] kQJbqm7uCi=running
[[18:49:03]] [INFO] Executing action 541/643: iOS Function: alert_accept
[[18:49:02]] [SUCCESS] Screenshot refreshed successfully
[[18:49:02]] [SUCCESS] Screenshot refreshed successfully
[[18:49:02]] [SUCCESS] Screenshot refreshed
[[18:49:02]] [INFO] Refreshing screenshot...
[[18:49:02]] [INFO] SPE01N6pyp=pass
[[18:48:56]] [INFO] SPE01N6pyp=running
[[18:48:56]] [INFO] Executing action 540/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:48:56]] [SUCCESS] Screenshot refreshed successfully
[[18:48:56]] [SUCCESS] Screenshot refreshed successfully
[[18:48:56]] [SUCCESS] Screenshot refreshed
[[18:48:56]] [INFO] Refreshing screenshot...
[[18:48:56]] [INFO] WEB5St2Mb7=pass
[[18:48:52]] [INFO] WEB5St2Mb7=running
[[18:48:52]] [INFO] Executing action 539/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:48:52]] [SUCCESS] Screenshot refreshed successfully
[[18:48:52]] [SUCCESS] Screenshot refreshed successfully
[[18:48:52]] [SUCCESS] Screenshot refreshed
[[18:48:52]] [INFO] Refreshing screenshot...
[[18:48:52]] [INFO] To7bij5MnF=pass
[[18:48:47]] [INFO] To7bij5MnF=running
[[18:48:47]] [INFO] Executing action 538/643: Swipe from (5%, 50%) to (90%, 50%)
[[18:48:46]] [SUCCESS] Screenshot refreshed successfully
[[18:48:46]] [SUCCESS] Screenshot refreshed successfully
[[18:48:46]] [SUCCESS] Screenshot refreshed
[[18:48:46]] [INFO] Refreshing screenshot...
[[18:48:46]] [INFO] NkybTKfs2U=pass
[[18:48:41]] [INFO] NkybTKfs2U=running
[[18:48:41]] [INFO] Executing action 537/643: Swipe from (5%, 50%) to (90%, 50%)
[[18:48:40]] [SUCCESS] Screenshot refreshed successfully
[[18:48:40]] [SUCCESS] Screenshot refreshed successfully
[[18:48:40]] [SUCCESS] Screenshot refreshed
[[18:48:40]] [INFO] Refreshing screenshot...
[[18:48:40]] [INFO] dYEtjrv6lz=pass
[[18:48:36]] [INFO] dYEtjrv6lz=running
[[18:48:36]] [INFO] Executing action 536/643: Tap on Text: "Months"
[[18:48:36]] [SUCCESS] Screenshot refreshed successfully
[[18:48:36]] [SUCCESS] Screenshot refreshed successfully
[[18:48:36]] [SUCCESS] Screenshot refreshed
[[18:48:36]] [INFO] Refreshing screenshot...
[[18:48:36]] [INFO] eGQ7VrKUSo=pass
[[18:48:32]] [INFO] eGQ7VrKUSo=running
[[18:48:32]] [INFO] Executing action 535/643: Tap on Text: "Age"
[[18:48:31]] [SUCCESS] Screenshot refreshed successfully
[[18:48:31]] [SUCCESS] Screenshot refreshed successfully
[[18:48:31]] [SUCCESS] Screenshot refreshed
[[18:48:31]] [INFO] Refreshing screenshot...
[[18:48:31]] [INFO] zNRPvs2cC4=pass
[[18:48:27]] [INFO] zNRPvs2cC4=running
[[18:48:27]] [INFO] Executing action 534/643: Tap on Text: "Toys"
[[18:48:27]] [SUCCESS] Screenshot refreshed successfully
[[18:48:27]] [SUCCESS] Screenshot refreshed successfully
[[18:48:27]] [SUCCESS] Screenshot refreshed
[[18:48:27]] [INFO] Refreshing screenshot...
[[18:48:27]] [INFO] KyyS139agr=pass
[[18:48:22]] [INFO] KyyS139agr=running
[[18:48:22]] [INFO] Executing action 533/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[18:48:22]] [SUCCESS] Screenshot refreshed successfully
[[18:48:22]] [SUCCESS] Screenshot refreshed successfully
[[18:48:22]] [SUCCESS] Screenshot refreshed
[[18:48:22]] [INFO] Refreshing screenshot...
[[18:48:22]] [INFO] 5e4LeoW1YU=pass
[[18:48:17]] [SUCCESS] Screenshot refreshed successfully
[[18:48:17]] [SUCCESS] Screenshot refreshed successfully
[[18:48:17]] [INFO] 5e4LeoW1YU=running
[[18:48:17]] [INFO] Executing action 532/643: Restart app: env[appid]
[[18:48:17]] [SUCCESS] Screenshot refreshed
[[18:48:17]] [INFO] Refreshing screenshot...
[[18:48:16]] [SUCCESS] Screenshot refreshed successfully
[[18:48:16]] [SUCCESS] Screenshot refreshed successfully
[[18:48:16]] [SUCCESS] Screenshot refreshed
[[18:48:16]] [INFO] Refreshing screenshot...
[[18:47:58]] [INFO] Executing Multi Step action step 8/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[18:47:58]] [SUCCESS] Screenshot refreshed successfully
[[18:47:58]] [SUCCESS] Screenshot refreshed successfully
[[18:47:57]] [SUCCESS] Screenshot refreshed
[[18:47:57]] [INFO] Refreshing screenshot...
[[18:47:13]] [INFO] Executing Multi Step action step 7/8: Swipe from (50%, 80%) to (50%, 10%)
[[18:47:13]] [SUCCESS] Screenshot refreshed successfully
[[18:47:13]] [SUCCESS] Screenshot refreshed successfully
[[18:47:13]] [SUCCESS] Screenshot refreshed
[[18:47:13]] [INFO] Refreshing screenshot...
[[18:46:56]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[18:46:56]] [SUCCESS] Screenshot refreshed successfully
[[18:46:56]] [SUCCESS] Screenshot refreshed successfully
[[18:46:55]] [SUCCESS] Screenshot refreshed
[[18:46:55]] [INFO] Refreshing screenshot...
[[18:46:12]] [INFO] Executing Multi Step action step 5/8: Swipe from (50%, 80%) to (50%, 10%)
[[18:46:11]] [SUCCESS] Screenshot refreshed successfully
[[18:46:11]] [SUCCESS] Screenshot refreshed successfully
[[18:46:11]] [SUCCESS] Screenshot refreshed
[[18:46:11]] [INFO] Refreshing screenshot...
[[18:45:54]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:45:54]] [SUCCESS] Screenshot refreshed successfully
[[18:45:54]] [SUCCESS] Screenshot refreshed successfully
[[18:45:53]] [SUCCESS] Screenshot refreshed
[[18:45:53]] [INFO] Refreshing screenshot...
[[18:45:09]] [INFO] Executing Multi Step action step 3/8: Swipe from (50%, 80%) to (50%, 10%)
[[18:45:09]] [SUCCESS] Screenshot refreshed successfully
[[18:45:09]] [SUCCESS] Screenshot refreshed successfully
[[18:45:09]] [SUCCESS] Screenshot refreshed
[[18:45:09]] [INFO] Refreshing screenshot...
[[18:44:51]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[18:44:51]] [SUCCESS] Screenshot refreshed successfully
[[18:44:51]] [SUCCESS] Screenshot refreshed successfully
[[18:44:50]] [SUCCESS] Screenshot refreshed
[[18:44:50]] [INFO] Refreshing screenshot...
[[18:44:04]] [INFO] Executing Multi Step action step 1/8: Swipe from (50%, 80%) to (50%, 10%)
[[18:44:04]] [INFO] Loaded 8 steps from test case: Click_Paginations
[[18:44:04]] [INFO] Loading steps for multiStep action: Click_Paginations
[[18:44:04]] [INFO] Z86xBjGUKY=running
[[18:44:04]] [INFO] Executing action 531/643: Execute Test Case: Click_Paginations (8 steps)
[[18:44:04]] [SUCCESS] Screenshot refreshed successfully
[[18:44:04]] [SUCCESS] Screenshot refreshed successfully
[[18:44:03]] [SUCCESS] Screenshot refreshed
[[18:44:03]] [INFO] Refreshing screenshot...
[[18:44:03]] [INFO] IL6kON0uQ9=pass
[[18:43:59]] [INFO] IL6kON0uQ9=running
[[18:43:59]] [INFO] Executing action 530/643: iOS Function: text - Text: "kids toys"
[[18:43:59]] [SUCCESS] Screenshot refreshed successfully
[[18:43:59]] [SUCCESS] Screenshot refreshed successfully
[[18:43:59]] [SUCCESS] Screenshot refreshed
[[18:43:59]] [INFO] Refreshing screenshot...
[[18:43:59]] [INFO] 6G6P3UE7Uy=pass
[[18:43:54]] [INFO] 6G6P3UE7Uy=running
[[18:43:54]] [INFO] Executing action 529/643: Tap on Text: "Find"
[[18:43:54]] [SUCCESS] Screenshot refreshed successfully
[[18:43:54]] [SUCCESS] Screenshot refreshed successfully
[[18:43:54]] [SUCCESS] Screenshot refreshed
[[18:43:54]] [INFO] Refreshing screenshot...
[[18:43:54]] [INFO] 7xs3GiydGF=pass
[[18:43:50]] [INFO] 7xs3GiydGF=running
[[18:43:50]] [INFO] Executing action 528/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:43:49]] [SUCCESS] Screenshot refreshed successfully
[[18:43:49]] [SUCCESS] Screenshot refreshed successfully
[[18:43:49]] [SUCCESS] Screenshot refreshed
[[18:43:49]] [INFO] Refreshing screenshot...
[[18:43:49]] [INFO] VqSa9z9R2Q=pass
[[18:43:48]] [INFO] VqSa9z9R2Q=running
[[18:43:48]] [INFO] Executing action 527/643: Launch app: env[appid]
[[18:43:47]] [SUCCESS] Screenshot refreshed successfully
[[18:43:47]] [SUCCESS] Screenshot refreshed successfully
[[18:43:47]] [SUCCESS] Screenshot refreshed
[[18:43:47]] [INFO] Refreshing screenshot...
[[18:43:47]] [INFO] RHEU77LRMw=pass
[[18:43:44]] [INFO] RHEU77LRMw=running
[[18:43:44]] [INFO] Executing action 526/643: Tap on Text: "+61"
[[18:43:43]] [SUCCESS] Screenshot refreshed successfully
[[18:43:43]] [SUCCESS] Screenshot refreshed successfully
[[18:43:43]] [SUCCESS] Screenshot refreshed
[[18:43:43]] [INFO] Refreshing screenshot...
[[18:43:43]] [INFO] MTRbUlaRvI=pass
[[18:43:39]] [INFO] MTRbUlaRvI=running
[[18:43:39]] [INFO] Executing action 525/643: Tap on Text: "1800"
[[18:43:39]] [SUCCESS] Screenshot refreshed successfully
[[18:43:39]] [SUCCESS] Screenshot refreshed successfully
[[18:43:39]] [SUCCESS] Screenshot refreshed
[[18:43:39]] [INFO] Refreshing screenshot...
[[18:43:39]] [INFO] I0tM87Yjhc=pass
[[18:43:34]] [INFO] I0tM87Yjhc=running
[[18:43:34]] [INFO] Executing action 524/643: Tap on Text: "click"
[[18:43:34]] [SUCCESS] Screenshot refreshed successfully
[[18:43:34]] [SUCCESS] Screenshot refreshed successfully
[[18:43:34]] [SUCCESS] Screenshot refreshed
[[18:43:34]] [INFO] Refreshing screenshot...
[[18:43:34]] [INFO] t6L5vWfBYM=pass
[[18:43:14]] [INFO] t6L5vWfBYM=running
[[18:43:14]] [INFO] Executing action 523/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:43:14]] [SUCCESS] Screenshot refreshed successfully
[[18:43:14]] [SUCCESS] Screenshot refreshed successfully
[[18:43:13]] [SUCCESS] Screenshot refreshed
[[18:43:13]] [INFO] Refreshing screenshot...
[[18:43:13]] [INFO] DhFJzlme9K=pass
[[18:43:10]] [INFO] DhFJzlme9K=running
[[18:43:10]] [INFO] Executing action 522/643: Tap on Text: "FAQ"
[[18:43:09]] [SUCCESS] Screenshot refreshed successfully
[[18:43:09]] [SUCCESS] Screenshot refreshed successfully
[[18:43:09]] [SUCCESS] Screenshot refreshed
[[18:43:09]] [INFO] Refreshing screenshot...
[[18:43:09]] [INFO] g17Boaefhg=pass
[[18:43:05]] [INFO] g17Boaefhg=running
[[18:43:05]] [INFO] Executing action 521/643: Tap on Text: "Help"
[[18:43:05]] [SUCCESS] Screenshot refreshed successfully
[[18:43:05]] [SUCCESS] Screenshot refreshed successfully
[[18:43:05]] [SUCCESS] Screenshot refreshed
[[18:43:05]] [INFO] Refreshing screenshot...
[[18:43:05]] [INFO] nPp27xJcCn=pass
[[18:42:52]] [INFO] nPp27xJcCn=running
[[18:42:52]] [INFO] Executing action 520/643: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[18:42:52]] [SUCCESS] Screenshot refreshed successfully
[[18:42:52]] [SUCCESS] Screenshot refreshed successfully
[[18:42:52]] [SUCCESS] Screenshot refreshed
[[18:42:52]] [INFO] Refreshing screenshot...
[[18:42:52]] [INFO] SqDiBhmyOG=pass
[[18:42:47]] [INFO] SqDiBhmyOG=running
[[18:42:47]] [INFO] Executing action 519/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:42:47]] [SUCCESS] Screenshot refreshed successfully
[[18:42:47]] [SUCCESS] Screenshot refreshed successfully
[[18:42:46]] [SUCCESS] Screenshot refreshed
[[18:42:46]] [INFO] Refreshing screenshot...
[[18:42:46]] [INFO] OR0SKKnFxy=pass
[[18:42:33]] [SUCCESS] Screenshot refreshed successfully
[[18:42:33]] [SUCCESS] Screenshot refreshed successfully
[[18:42:33]] [INFO] OR0SKKnFxy=running
[[18:42:33]] [INFO] Executing action 518/643: Restart app: env[appid]
[[18:42:33]] [SUCCESS] Screenshot refreshed
[[18:42:33]] [INFO] Refreshing screenshot...
[[18:42:33]] [SUCCESS] Screenshot refreshed successfully
[[18:42:33]] [SUCCESS] Screenshot refreshed successfully
[[18:42:32]] [SUCCESS] Screenshot refreshed
[[18:42:32]] [INFO] Refreshing screenshot...
[[18:42:30]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:42:30]] [SUCCESS] Screenshot refreshed successfully
[[18:42:30]] [SUCCESS] Screenshot refreshed successfully
[[18:42:29]] [SUCCESS] Screenshot refreshed
[[18:42:29]] [INFO] Refreshing screenshot...
[[18:42:17]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:42:17]] [SUCCESS] Screenshot refreshed successfully
[[18:42:17]] [SUCCESS] Screenshot refreshed successfully
[[18:42:16]] [SUCCESS] Screenshot refreshed
[[18:42:16]] [INFO] Refreshing screenshot...
[[18:42:13]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:42:13]] [SUCCESS] Screenshot refreshed successfully
[[18:42:13]] [SUCCESS] Screenshot refreshed successfully
[[18:42:12]] [SUCCESS] Screenshot refreshed
[[18:42:12]] [INFO] Refreshing screenshot...
[[18:42:09]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:42:08]] [SUCCESS] Screenshot refreshed successfully
[[18:42:08]] [SUCCESS] Screenshot refreshed successfully
[[18:42:08]] [SUCCESS] Screenshot refreshed
[[18:42:08]] [INFO] Refreshing screenshot...
[[18:42:02]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:42:02]] [SUCCESS] Screenshot refreshed successfully
[[18:42:02]] [SUCCESS] Screenshot refreshed successfully
[[18:42:01]] [SUCCESS] Screenshot refreshed
[[18:42:01]] [INFO] Refreshing screenshot...
[[18:41:55]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:41:55]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:41:55]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:41:55]] [INFO] kPdSiomhwu=running
[[18:41:55]] [INFO] Executing action 517/643: cleanupSteps action
[[18:41:54]] [SUCCESS] Screenshot refreshed successfully
[[18:41:54]] [SUCCESS] Screenshot refreshed successfully
[[18:41:54]] [SUCCESS] Screenshot refreshed
[[18:41:54]] [INFO] Refreshing screenshot...
[[18:41:54]] [INFO] Qb1AArnpCH=pass
[[18:41:48]] [INFO] Qb1AArnpCH=running
[[18:41:48]] [INFO] Executing action 516/643: Wait for 5 ms
[[18:41:47]] [SUCCESS] Screenshot refreshed successfully
[[18:41:47]] [SUCCESS] Screenshot refreshed successfully
[[18:41:47]] [SUCCESS] Screenshot refreshed
[[18:41:47]] [INFO] Refreshing screenshot...
[[18:41:47]] [INFO] yxlzTytgFT=pass
[[18:41:42]] [INFO] yxlzTytgFT=running
[[18:41:42]] [INFO] Executing action 515/643: Tap if locator exists: xpath="//XCUIElementTypeButton[contains(@name,"Remove")]"
[[18:41:42]] [SUCCESS] Screenshot refreshed successfully
[[18:41:42]] [SUCCESS] Screenshot refreshed successfully
[[18:41:42]] [SUCCESS] Screenshot refreshed
[[18:41:42]] [INFO] Refreshing screenshot...
[[18:41:42]] [INFO] K2w7X1cPdH=pass
[[18:41:37]] [INFO] K2w7X1cPdH=running
[[18:41:37]] [INFO] Executing action 514/643: Swipe from (50%, 50%) to (50%, 30%)
[[18:41:36]] [SUCCESS] Screenshot refreshed successfully
[[18:41:36]] [SUCCESS] Screenshot refreshed successfully
[[18:41:36]] [SUCCESS] Screenshot refreshed
[[18:41:36]] [INFO] Refreshing screenshot...
[[18:41:36]] [INFO] P26OyuqWlb=pass
[[18:41:23]] [INFO] P26OyuqWlb=running
[[18:41:23]] [INFO] Executing action 513/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:41:23]] [SUCCESS] Screenshot refreshed successfully
[[18:41:23]] [SUCCESS] Screenshot refreshed successfully
[[18:41:23]] [SUCCESS] Screenshot refreshed
[[18:41:23]] [INFO] Refreshing screenshot...
[[18:41:23]] [INFO] UpUSVInizv=pass
[[18:41:19]] [INFO] UpUSVInizv=running
[[18:41:19]] [INFO] Executing action 512/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:41:18]] [SUCCESS] Screenshot refreshed successfully
[[18:41:18]] [SUCCESS] Screenshot refreshed successfully
[[18:41:18]] [SUCCESS] Screenshot refreshed
[[18:41:18]] [INFO] Refreshing screenshot...
[[18:41:18]] [INFO] c4T3INQkzn=pass
[[18:41:13]] [INFO] c4T3INQkzn=running
[[18:41:13]] [INFO] Executing action 511/643: Restart app: env[appid]
[[18:41:13]] [SUCCESS] Screenshot refreshed successfully
[[18:41:13]] [SUCCESS] Screenshot refreshed successfully
[[18:41:13]] [SUCCESS] Screenshot refreshed
[[18:41:13]] [INFO] Refreshing screenshot...
[[18:41:13]] [INFO] Teyz3d55XS=pass
[[18:41:04]] [INFO] Teyz3d55XS=running
[[18:41:04]] [INFO] Executing action 510/643: Tap if locator exists: accessibility_id="Add to bag"
[[18:41:04]] [SUCCESS] Screenshot refreshed successfully
[[18:41:04]] [SUCCESS] Screenshot refreshed successfully
[[18:41:04]] [SUCCESS] Screenshot refreshed
[[18:41:04]] [INFO] Refreshing screenshot...
[[18:41:04]] [INFO] MA2re5cDWr=pass
[[18:40:55]] [INFO] MA2re5cDWr=running
[[18:40:55]] [INFO] Executing action 509/643: Swipe from (50%, 50%) to (50%, 30%)
[[18:40:55]] [SUCCESS] Screenshot refreshed successfully
[[18:40:55]] [SUCCESS] Screenshot refreshed successfully
[[18:40:55]] [SUCCESS] Screenshot refreshed
[[18:40:55]] [INFO] Refreshing screenshot...
[[18:40:55]] [INFO] 2hGhWulI52=pass
[[18:40:51]] [INFO] 2hGhWulI52=running
[[18:40:51]] [INFO] Executing action 508/643: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[18:40:51]] [SUCCESS] Screenshot refreshed successfully
[[18:40:51]] [SUCCESS] Screenshot refreshed successfully
[[18:40:51]] [SUCCESS] Screenshot refreshed
[[18:40:51]] [INFO] Refreshing screenshot...
[[18:40:51]] [INFO] n57KEWjTea=pass
[[18:40:47]] [INFO] n57KEWjTea=running
[[18:40:47]] [INFO] Executing action 507/643: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[18:40:46]] [SUCCESS] Screenshot refreshed successfully
[[18:40:46]] [SUCCESS] Screenshot refreshed successfully
[[18:40:46]] [SUCCESS] Screenshot refreshed
[[18:40:46]] [INFO] Refreshing screenshot...
[[18:40:46]] [INFO] L59V5hqMX9=pass
[[18:40:42]] [INFO] L59V5hqMX9=running
[[18:40:42]] [INFO] Executing action 506/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[18:40:42]] [SUCCESS] Screenshot refreshed successfully
[[18:40:42]] [SUCCESS] Screenshot refreshed successfully
[[18:40:42]] [SUCCESS] Screenshot refreshed
[[18:40:42]] [INFO] Refreshing screenshot...
[[18:40:42]] [INFO] OKiI82VdnE=pass
[[18:40:35]] [INFO] OKiI82VdnE=running
[[18:40:35]] [INFO] Executing action 505/643: Swipe up till element xpath: "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]" is visible
[[18:40:35]] [SUCCESS] Screenshot refreshed successfully
[[18:40:35]] [SUCCESS] Screenshot refreshed successfully
[[18:40:35]] [SUCCESS] Screenshot refreshed
[[18:40:35]] [INFO] Refreshing screenshot...
[[18:40:35]] [INFO] 3KNqlNy6Bj=pass
[[18:40:31]] [INFO] 3KNqlNy6Bj=running
[[18:40:31]] [INFO] Executing action 504/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[18:40:30]] [SUCCESS] Screenshot refreshed successfully
[[18:40:30]] [SUCCESS] Screenshot refreshed successfully
[[18:40:30]] [SUCCESS] Screenshot refreshed
[[18:40:30]] [INFO] Refreshing screenshot...
[[18:40:30]] [INFO] 3NOS1fbxZs=pass
[[18:40:27]] [INFO] 3NOS1fbxZs=running
[[18:40:27]] [INFO] Executing action 503/643: Tap on image: banner-close-updated.png
[[18:40:26]] [SUCCESS] Screenshot refreshed successfully
[[18:40:26]] [SUCCESS] Screenshot refreshed successfully
[[18:40:26]] [SUCCESS] Screenshot refreshed
[[18:40:26]] [INFO] Refreshing screenshot...
[[18:40:26]] [INFO] K0c1gL9UK1=pass
[[18:40:19]] [INFO] K0c1gL9UK1=running
[[18:40:19]] [INFO] Executing action 502/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:40:19]] [SUCCESS] Screenshot refreshed successfully
[[18:40:19]] [SUCCESS] Screenshot refreshed successfully
[[18:40:19]] [SUCCESS] Screenshot refreshed
[[18:40:19]] [INFO] Refreshing screenshot...
[[18:40:19]] [INFO] IW6uAwdtiW=pass
[[18:40:15]] [INFO] IW6uAwdtiW=running
[[18:40:15]] [INFO] Executing action 501/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[18:40:14]] [SUCCESS] Screenshot refreshed successfully
[[18:40:14]] [SUCCESS] Screenshot refreshed successfully
[[18:40:14]] [SUCCESS] Screenshot refreshed
[[18:40:14]] [INFO] Refreshing screenshot...
[[18:40:14]] [INFO] DbM0d0m6rU=pass
[[18:40:10]] [INFO] DbM0d0m6rU=running
[[18:40:10]] [INFO] Executing action 500/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[18:40:10]] [SUCCESS] Screenshot refreshed successfully
[[18:40:10]] [SUCCESS] Screenshot refreshed successfully
[[18:40:10]] [SUCCESS] Screenshot refreshed
[[18:40:10]] [INFO] Refreshing screenshot...
[[18:40:10]] [INFO] saiPPHQSPa=pass
[[18:39:58]] [INFO] saiPPHQSPa=running
[[18:39:58]] [INFO] Executing action 499/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:39:57]] [SUCCESS] Screenshot refreshed successfully
[[18:39:57]] [SUCCESS] Screenshot refreshed successfully
[[18:39:57]] [SUCCESS] Screenshot refreshed
[[18:39:57]] [INFO] Refreshing screenshot...
[[18:39:57]] [INFO] UpUSVInizv=pass
[[18:39:52]] [INFO] UpUSVInizv=running
[[18:39:52]] [INFO] Executing action 498/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:39:52]] [SUCCESS] Screenshot refreshed successfully
[[18:39:52]] [SUCCESS] Screenshot refreshed successfully
[[18:39:52]] [SUCCESS] Screenshot refreshed
[[18:39:52]] [INFO] Refreshing screenshot...
[[18:39:52]] [INFO] Iab9zCfpqO=pass
[[18:39:34]] [INFO] Iab9zCfpqO=running
[[18:39:34]] [INFO] Executing action 497/643: Tap on element with accessibility_id: Add to bag
[[18:39:34]] [SUCCESS] Screenshot refreshed successfully
[[18:39:34]] [SUCCESS] Screenshot refreshed successfully
[[18:39:33]] [SUCCESS] Screenshot refreshed
[[18:39:33]] [INFO] Refreshing screenshot...
[[18:39:33]] [INFO] Qy0Y0uJchm=pass
[[18:39:30]] [INFO] Qy0Y0uJchm=running
[[18:39:30]] [INFO] Executing action 496/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[18:39:29]] [SUCCESS] Screenshot refreshed successfully
[[18:39:29]] [SUCCESS] Screenshot refreshed successfully
[[18:39:29]] [SUCCESS] Screenshot refreshed
[[18:39:29]] [INFO] Refreshing screenshot...
[[18:39:29]] [INFO] YHaMIjULRf=pass
[[18:39:24]] [INFO] YHaMIjULRf=running
[[18:39:24]] [INFO] Executing action 495/643: Tap on Text: "List"
[[18:39:24]] [SUCCESS] Screenshot refreshed successfully
[[18:39:24]] [SUCCESS] Screenshot refreshed successfully
[[18:39:23]] [SUCCESS] Screenshot refreshed
[[18:39:23]] [INFO] Refreshing screenshot...
[[18:39:23]] [INFO] igReeDqips=pass
[[18:39:19]] [INFO] igReeDqips=running
[[18:39:19]] [INFO] Executing action 494/643: Tap on image: env[catalogue-menu-img]
[[18:39:19]] [SUCCESS] Screenshot refreshed successfully
[[18:39:19]] [SUCCESS] Screenshot refreshed successfully
[[18:39:19]] [SUCCESS] Screenshot refreshed
[[18:39:19]] [INFO] Refreshing screenshot...
[[18:39:19]] [INFO] gcSsGpqKwk=pass
[[18:38:55]] [INFO] gcSsGpqKwk=running
[[18:38:55]] [INFO] Executing action 493/643: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[18:38:55]] [SUCCESS] Screenshot refreshed successfully
[[18:38:55]] [SUCCESS] Screenshot refreshed successfully
[[18:38:55]] [SUCCESS] Screenshot refreshed
[[18:38:55]] [INFO] Refreshing screenshot...
[[18:38:55]] [INFO] gkkQzTCmma=pass
[[18:38:51]] [INFO] gkkQzTCmma=running
[[18:38:51]] [INFO] Executing action 492/643: Tap on Text: "Catalogue"
[[18:38:50]] [SUCCESS] Screenshot refreshed successfully
[[18:38:50]] [SUCCESS] Screenshot refreshed successfully
[[18:38:50]] [SUCCESS] Screenshot refreshed
[[18:38:50]] [INFO] Refreshing screenshot...
[[18:38:50]] [INFO] VpOhIxEl53=pass
[[18:38:38]] [INFO] VpOhIxEl53=running
[[18:38:38]] [INFO] Executing action 491/643: Tap if locator exists: xpath="//XCUIElementTypeStaticText[@name="Chat now"]/preceding-sibling::XCUIElementTypeImage[1]"
[[18:38:37]] [SUCCESS] Screenshot refreshed successfully
[[18:38:37]] [SUCCESS] Screenshot refreshed successfully
[[18:38:37]] [SUCCESS] Screenshot refreshed
[[18:38:37]] [INFO] Refreshing screenshot...
[[18:38:37]] [INFO] UpUSVInizv=pass
[[18:38:32]] [INFO] UpUSVInizv=running
[[18:38:32]] [INFO] Executing action 490/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[18:38:32]] [SUCCESS] Screenshot refreshed successfully
[[18:38:32]] [SUCCESS] Screenshot refreshed successfully
[[18:38:32]] [SUCCESS] Screenshot refreshed
[[18:38:32]] [INFO] Refreshing screenshot...
[[18:38:32]] [INFO] Cmvm82hiAa=pass
[[18:38:24]] [INFO] Cmvm82hiAa=running
[[18:38:24]] [INFO] Executing action 489/643: Tap on element with accessibility_id: Add to bag
[[18:38:24]] [SUCCESS] Screenshot refreshed successfully
[[18:38:24]] [SUCCESS] Screenshot refreshed successfully
[[18:38:24]] [SUCCESS] Screenshot refreshed
[[18:38:24]] [INFO] Refreshing screenshot...
[[18:38:24]] [INFO] ZZPNqTJ65s=pass
[[18:38:15]] [INFO] ZZPNqTJ65s=running
[[18:38:15]] [INFO] Executing action 488/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:38:14]] [SUCCESS] Screenshot refreshed successfully
[[18:38:14]] [SUCCESS] Screenshot refreshed successfully
[[18:38:14]] [SUCCESS] Screenshot refreshed
[[18:38:14]] [INFO] Refreshing screenshot...
[[18:38:14]] [INFO] JcAR0JctQ6=pass
[[18:38:10]] [INFO] JcAR0JctQ6=running
[[18:38:10]] [INFO] Executing action 487/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[18:38:10]] [SUCCESS] Screenshot refreshed successfully
[[18:38:10]] [SUCCESS] Screenshot refreshed successfully
[[18:38:10]] [SUCCESS] Screenshot refreshed
[[18:38:10]] [INFO] Refreshing screenshot...
[[18:38:10]] [INFO] Pd7cReoJM6=pass
[[18:38:05]] [INFO] Pd7cReoJM6=running
[[18:38:05]] [INFO] Executing action 486/643: Tap on Text: "List"
[[18:38:04]] [SUCCESS] Screenshot refreshed successfully
[[18:38:04]] [SUCCESS] Screenshot refreshed successfully
[[18:38:04]] [SUCCESS] Screenshot refreshed
[[18:38:04]] [INFO] Refreshing screenshot...
[[18:38:04]] [INFO] igReeDqips=pass
[[18:38:00]] [INFO] igReeDqips=running
[[18:38:00]] [INFO] Executing action 485/643: Tap on image: env[catalogue-menu-img]
[[18:38:00]] [SUCCESS] Screenshot refreshed successfully
[[18:38:00]] [SUCCESS] Screenshot refreshed successfully
[[18:37:59]] [SUCCESS] Screenshot refreshed
[[18:37:59]] [INFO] Refreshing screenshot...
[[18:37:59]] [INFO] Jh6RTFWeOU=pass
[[18:37:36]] [INFO] Jh6RTFWeOU=running
[[18:37:36]] [INFO] Executing action 484/643: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[18:37:36]] [SUCCESS] Screenshot refreshed successfully
[[18:37:36]] [SUCCESS] Screenshot refreshed successfully
[[18:37:36]] [SUCCESS] Screenshot refreshed
[[18:37:36]] [INFO] Refreshing screenshot...
[[18:37:36]] [INFO] gkkQzTCmma=pass
[[18:37:32]] [INFO] gkkQzTCmma=running
[[18:37:32]] [INFO] Executing action 483/643: Tap on Text: "Catalogue"
[[18:37:32]] [SUCCESS] Screenshot refreshed successfully
[[18:37:32]] [SUCCESS] Screenshot refreshed successfully
[[18:37:32]] [SUCCESS] Screenshot refreshed
[[18:37:32]] [INFO] Refreshing screenshot...
[[18:37:32]] [INFO] QUeGIASAxV=pass
[[18:37:28]] [INFO] QUeGIASAxV=running
[[18:37:28]] [INFO] Executing action 482/643: Swipe from (50%, 50%) to (50%, 30%)
[[18:37:28]] [SUCCESS] Screenshot refreshed successfully
[[18:37:28]] [SUCCESS] Screenshot refreshed successfully
[[18:37:28]] [SUCCESS] Screenshot refreshed
[[18:37:28]] [INFO] Refreshing screenshot...
[[18:37:28]] [INFO] UpUSVInizv=pass
[[18:37:24]] [INFO] UpUSVInizv=running
[[18:37:24]] [INFO] Executing action 481/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[18:37:24]] [SUCCESS] Screenshot refreshed successfully
[[18:37:24]] [SUCCESS] Screenshot refreshed successfully
[[18:37:23]] [SUCCESS] Screenshot refreshed
[[18:37:23]] [INFO] Refreshing screenshot...
[[18:37:23]] [INFO] 0QtNHB5WEK=pass
[[18:37:20]] [INFO] 0QtNHB5WEK=running
[[18:37:20]] [INFO] Executing action 480/643: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[18:37:20]] [SUCCESS] Screenshot refreshed successfully
[[18:37:20]] [SUCCESS] Screenshot refreshed successfully
[[18:37:20]] [SUCCESS] Screenshot refreshed
[[18:37:20]] [INFO] Refreshing screenshot...
[[18:37:20]] [INFO] fTdGMJ3NH3=pass
[[18:37:17]] [INFO] fTdGMJ3NH3=running
[[18:37:17]] [INFO] Executing action 479/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[18:37:17]] [SUCCESS] Screenshot refreshed successfully
[[18:37:17]] [SUCCESS] Screenshot refreshed successfully
[[18:37:17]] [SUCCESS] Screenshot refreshed
[[18:37:17]] [INFO] Refreshing screenshot...
[[18:37:17]] [INFO] rYJcLPh8Aq=pass
[[18:37:13]] [INFO] rYJcLPh8Aq=running
[[18:37:13]] [INFO] Executing action 478/643: iOS Function: text - Text: "kmart au"
[[18:37:13]] [SUCCESS] Screenshot refreshed successfully
[[18:37:13]] [SUCCESS] Screenshot refreshed successfully
[[18:37:13]] [SUCCESS] Screenshot refreshed
[[18:37:13]] [INFO] Refreshing screenshot...
[[18:37:13]] [INFO] 0Q0fm6OTij=pass
[[18:37:10]] [INFO] 0Q0fm6OTij=running
[[18:37:10]] [INFO] Executing action 477/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[18:37:10]] [SUCCESS] Screenshot refreshed successfully
[[18:37:10]] [SUCCESS] Screenshot refreshed successfully
[[18:37:10]] [SUCCESS] Screenshot refreshed
[[18:37:10]] [INFO] Refreshing screenshot...
[[18:37:10]] [INFO] xVuuejtCFA=pass
[[18:37:06]] [INFO] xVuuejtCFA=running
[[18:37:06]] [INFO] Executing action 476/643: Restart app: com.apple.mobilesafari
[[18:37:06]] [SUCCESS] Screenshot refreshed successfully
[[18:37:06]] [SUCCESS] Screenshot refreshed successfully
[[18:37:06]] [SUCCESS] Screenshot refreshed
[[18:37:06]] [INFO] Refreshing screenshot...
[[18:37:06]] [INFO] LcYLwUffqj=pass
[[18:37:02]] [INFO] LcYLwUffqj=running
[[18:37:02]] [INFO] Executing action 475/643: Tap on Text: "out"
[[18:37:01]] [SUCCESS] Screenshot refreshed successfully
[[18:37:01]] [SUCCESS] Screenshot refreshed successfully
[[18:37:01]] [SUCCESS] Screenshot refreshed
[[18:37:01]] [INFO] Refreshing screenshot...
[[18:37:01]] [INFO] ZZPNqTJ65s=pass
[[18:36:57]] [INFO] ZZPNqTJ65s=running
[[18:36:57]] [INFO] Executing action 474/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:36:57]] [SUCCESS] Screenshot refreshed successfully
[[18:36:57]] [SUCCESS] Screenshot refreshed successfully
[[18:36:56]] [SUCCESS] Screenshot refreshed
[[18:36:56]] [INFO] Refreshing screenshot...
[[18:36:56]] [INFO] UpUSVInizv=pass
[[18:36:51]] [INFO] UpUSVInizv=running
[[18:36:51]] [INFO] Executing action 473/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[18:36:51]] [SUCCESS] Screenshot refreshed successfully
[[18:36:51]] [SUCCESS] Screenshot refreshed successfully
[[18:36:51]] [SUCCESS] Screenshot refreshed
[[18:36:51]] [INFO] Refreshing screenshot...
[[18:36:51]] [INFO] hCCEvRtj1A=pass
[[18:36:46]] [INFO] hCCEvRtj1A=running
[[18:36:46]] [INFO] Executing action 472/643: Restart app: env[appid]
[[18:36:46]] [SUCCESS] Screenshot refreshed successfully
[[18:36:46]] [SUCCESS] Screenshot refreshed successfully
[[18:36:46]] [SUCCESS] Screenshot refreshed
[[18:36:46]] [INFO] Refreshing screenshot...
[[18:36:46]] [INFO] V42eHtTRYW=pass
[[18:36:39]] [INFO] V42eHtTRYW=running
[[18:36:39]] [INFO] Executing action 471/643: Wait for 5 ms
[[18:36:39]] [SUCCESS] Screenshot refreshed successfully
[[18:36:39]] [SUCCESS] Screenshot refreshed successfully
[[18:36:39]] [SUCCESS] Screenshot refreshed
[[18:36:39]] [INFO] Refreshing screenshot...
[[18:36:39]] [INFO] GRwHMVK4sA=pass
[[18:36:37]] [INFO] GRwHMVK4sA=running
[[18:36:37]] [INFO] Executing action 470/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[18:36:36]] [SUCCESS] Screenshot refreshed successfully
[[18:36:36]] [SUCCESS] Screenshot refreshed successfully
[[18:36:36]] [SUCCESS] Screenshot refreshed
[[18:36:36]] [INFO] Refreshing screenshot...
[[18:36:36]] [INFO] V42eHtTRYW=pass
[[18:36:30]] [INFO] V42eHtTRYW=running
[[18:36:30]] [INFO] Executing action 469/643: Wait for 5 ms
[[18:36:29]] [SUCCESS] Screenshot refreshed successfully
[[18:36:29]] [SUCCESS] Screenshot refreshed successfully
[[18:36:29]] [SUCCESS] Screenshot refreshed
[[18:36:29]] [INFO] Refreshing screenshot...
[[18:36:29]] [INFO] LfyQctrEJn=pass
[[18:36:28]] [INFO] LfyQctrEJn=running
[[18:36:28]] [INFO] Executing action 468/643: Launch app: com.apple.Preferences
[[18:36:28]] [SUCCESS] Screenshot refreshed successfully
[[18:36:28]] [SUCCESS] Screenshot refreshed successfully
[[18:36:27]] [SUCCESS] Screenshot refreshed
[[18:36:27]] [INFO] Refreshing screenshot...
[[18:36:27]] [INFO] seQcUKjkSU=pass
[[18:36:26]] [INFO] seQcUKjkSU=running
[[18:36:26]] [INFO] Executing action 467/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:36:26]] [SUCCESS] Screenshot refreshed successfully
[[18:36:26]] [SUCCESS] Screenshot refreshed successfully
[[18:36:25]] [SUCCESS] Screenshot refreshed
[[18:36:25]] [INFO] Refreshing screenshot...
[[18:36:25]] [INFO] UpUSVInizv=pass
[[18:36:23]] [INFO] UpUSVInizv=running
[[18:36:23]] [INFO] Executing action 466/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[18:36:23]] [SUCCESS] Screenshot refreshed successfully
[[18:36:23]] [SUCCESS] Screenshot refreshed successfully
[[18:36:23]] [SUCCESS] Screenshot refreshed
[[18:36:23]] [INFO] Refreshing screenshot...
[[18:36:23]] [INFO] WoymrHdtrO=pass
[[18:36:21]] [INFO] WoymrHdtrO=running
[[18:36:21]] [INFO] Executing action 465/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:36:21]] [SUCCESS] Screenshot refreshed successfully
[[18:36:21]] [SUCCESS] Screenshot refreshed successfully
[[18:36:21]] [SUCCESS] Screenshot refreshed
[[18:36:21]] [INFO] Refreshing screenshot...
[[18:36:21]] [INFO] 6xgrAWyfZ4=pass
[[18:36:19]] [INFO] 6xgrAWyfZ4=running
[[18:36:19]] [INFO] Executing action 464/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[18:36:18]] [SUCCESS] Screenshot refreshed successfully
[[18:36:18]] [SUCCESS] Screenshot refreshed successfully
[[18:36:18]] [SUCCESS] Screenshot refreshed
[[18:36:18]] [INFO] Refreshing screenshot...
[[18:36:18]] [INFO] eSr9EFlJek=pass
[[18:36:17]] [INFO] eSr9EFlJek=running
[[18:36:17]] [INFO] Executing action 463/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:36:16]] [SUCCESS] Screenshot refreshed successfully
[[18:36:16]] [SUCCESS] Screenshot refreshed successfully
[[18:36:16]] [SUCCESS] Screenshot refreshed
[[18:36:16]] [INFO] Refreshing screenshot...
[[18:36:16]] [INFO] 3KNqlNy6Bj=pass
[[18:36:14]] [INFO] 3KNqlNy6Bj=running
[[18:36:14]] [INFO] Executing action 462/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:14]] [SUCCESS] Screenshot refreshed successfully
[[18:36:14]] [SUCCESS] Screenshot refreshed
[[18:36:14]] [INFO] Refreshing screenshot...
[[18:36:14]] [INFO] cokvFXhj4c=pass
[[18:36:12]] [INFO] cokvFXhj4c=running
[[18:36:12]] [INFO] Executing action 461/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[18:36:12]] [SUCCESS] Screenshot refreshed successfully
[[18:36:12]] [SUCCESS] Screenshot refreshed successfully
[[18:36:12]] [SUCCESS] Screenshot refreshed
[[18:36:12]] [INFO] Refreshing screenshot...
[[18:36:12]] [INFO] oSQ8sPdVOJ=pass
[[18:36:07]] [INFO] oSQ8sPdVOJ=running
[[18:36:07]] [INFO] Executing action 460/643: Restart app: env[appid]
[[18:36:07]] [SUCCESS] Screenshot refreshed successfully
[[18:36:07]] [SUCCESS] Screenshot refreshed successfully
[[18:36:07]] [SUCCESS] Screenshot refreshed
[[18:36:07]] [INFO] Refreshing screenshot...
[[18:36:07]] [INFO] V42eHtTRYW=pass
[[18:36:00]] [INFO] V42eHtTRYW=running
[[18:36:00]] [INFO] Executing action 459/643: Wait for 5 ms
[[18:36:00]] [SUCCESS] Screenshot refreshed successfully
[[18:36:00]] [SUCCESS] Screenshot refreshed successfully
[[18:36:00]] [SUCCESS] Screenshot refreshed
[[18:36:00]] [INFO] Refreshing screenshot...
[[18:36:00]] [INFO] jUCAk6GJc4=pass
[[18:35:57]] [INFO] jUCAk6GJc4=running
[[18:35:57]] [INFO] Executing action 458/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[18:35:57]] [SUCCESS] Screenshot refreshed successfully
[[18:35:57]] [SUCCESS] Screenshot refreshed successfully
[[18:35:57]] [SUCCESS] Screenshot refreshed
[[18:35:57]] [INFO] Refreshing screenshot...
[[18:35:57]] [INFO] V42eHtTRYW=pass
[[18:35:51]] [INFO] V42eHtTRYW=running
[[18:35:51]] [INFO] Executing action 457/643: Wait for 5 ms
[[18:35:50]] [SUCCESS] Screenshot refreshed successfully
[[18:35:50]] [SUCCESS] Screenshot refreshed successfully
[[18:35:50]] [SUCCESS] Screenshot refreshed
[[18:35:50]] [INFO] Refreshing screenshot...
[[18:35:50]] [INFO] w1RV76df9x=pass
[[18:35:47]] [INFO] w1RV76df9x=running
[[18:35:47]] [INFO] Executing action 456/643: Tap on Text: "Wi-Fi"
[[18:35:46]] [SUCCESS] Screenshot refreshed successfully
[[18:35:46]] [SUCCESS] Screenshot refreshed successfully
[[18:35:46]] [SUCCESS] Screenshot refreshed
[[18:35:46]] [INFO] Refreshing screenshot...
[[18:35:46]] [INFO] LfyQctrEJn=pass
[[18:35:43]] [INFO] LfyQctrEJn=running
[[18:35:43]] [INFO] Executing action 455/643: Launch app: com.apple.Preferences
[[18:35:43]] [SUCCESS] Screenshot refreshed successfully
[[18:35:43]] [SUCCESS] Screenshot refreshed successfully
[[18:35:43]] [SUCCESS] Screenshot refreshed
[[18:35:43]] [INFO] Refreshing screenshot...
[[18:35:43]] [INFO] mIKA85kXaW=pass
[[18:35:42]] [SUCCESS] Screenshot refreshed successfully
[[18:35:42]] [SUCCESS] Screenshot refreshed successfully
[[18:35:41]] [INFO] mIKA85kXaW=running
[[18:35:41]] [INFO] Executing action 454/643: Terminate app: com.apple.Preferences
[[18:35:41]] [SUCCESS] Screenshot refreshed
[[18:35:41]] [INFO] Refreshing screenshot...
[[18:35:41]] [SUCCESS] Screenshot refreshed successfully
[[18:35:41]] [SUCCESS] Screenshot refreshed successfully
[[18:35:41]] [SUCCESS] Screenshot refreshed
[[18:35:41]] [INFO] Refreshing screenshot...
[[18:35:37]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:35:36]] [SUCCESS] Screenshot refreshed successfully
[[18:35:36]] [SUCCESS] Screenshot refreshed successfully
[[18:35:36]] [SUCCESS] Screenshot refreshed
[[18:35:36]] [INFO] Refreshing screenshot...
[[18:35:32]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:35:32]] [SUCCESS] Screenshot refreshed successfully
[[18:35:32]] [SUCCESS] Screenshot refreshed successfully
[[18:35:32]] [SUCCESS] Screenshot refreshed
[[18:35:32]] [INFO] Refreshing screenshot...
[[18:35:27]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:35:27]] [SUCCESS] Screenshot refreshed successfully
[[18:35:27]] [SUCCESS] Screenshot refreshed successfully
[[18:35:27]] [SUCCESS] Screenshot refreshed
[[18:35:27]] [INFO] Refreshing screenshot...
[[18:35:23]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:35:22]] [SUCCESS] Screenshot refreshed successfully
[[18:35:22]] [SUCCESS] Screenshot refreshed successfully
[[18:35:22]] [SUCCESS] Screenshot refreshed
[[18:35:22]] [INFO] Refreshing screenshot...
[[18:35:17]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:35:17]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:35:17]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:35:17]] [INFO] gx05zu87DK=running
[[18:35:17]] [INFO] Executing action 453/643: Execute Test Case: Kmart-Signin (5 steps)
[[18:35:16]] [SUCCESS] Screenshot refreshed successfully
[[18:35:16]] [SUCCESS] Screenshot refreshed successfully
[[18:35:16]] [SUCCESS] Screenshot refreshed
[[18:35:16]] [INFO] Refreshing screenshot...
[[18:35:16]] [INFO] rJ86z4njuR=pass
[[18:35:14]] [INFO] rJ86z4njuR=running
[[18:35:14]] [INFO] Executing action 452/643: iOS Function: alert_accept
[[18:35:13]] [SUCCESS] Screenshot refreshed successfully
[[18:35:13]] [SUCCESS] Screenshot refreshed successfully
[[18:35:13]] [SUCCESS] Screenshot refreshed
[[18:35:13]] [INFO] Refreshing screenshot...
[[18:35:13]] [INFO] veukWo4573=pass
[[18:35:08]] [INFO] veukWo4573=running
[[18:35:08]] [INFO] Executing action 451/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[18:35:08]] [SUCCESS] Screenshot refreshed successfully
[[18:35:08]] [SUCCESS] Screenshot refreshed successfully
[[18:35:08]] [SUCCESS] Screenshot refreshed
[[18:35:08]] [INFO] Refreshing screenshot...
[[18:35:08]] [INFO] XEbZHdi0GT=pass
[[18:34:54]] [SUCCESS] Screenshot refreshed successfully
[[18:34:54]] [SUCCESS] Screenshot refreshed successfully
[[18:34:54]] [INFO] XEbZHdi0GT=running
[[18:34:54]] [INFO] Executing action 450/643: Restart app: env[appid]
[[18:34:54]] [SUCCESS] Screenshot refreshed
[[18:34:54]] [INFO] Refreshing screenshot...
[[18:34:54]] [SUCCESS] Screenshot refreshed successfully
[[18:34:54]] [SUCCESS] Screenshot refreshed successfully
[[18:34:54]] [SUCCESS] Screenshot refreshed
[[18:34:54]] [INFO] Refreshing screenshot...
[[18:34:51]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:34:51]] [SUCCESS] Screenshot refreshed successfully
[[18:34:51]] [SUCCESS] Screenshot refreshed successfully
[[18:34:51]] [SUCCESS] Screenshot refreshed
[[18:34:51]] [INFO] Refreshing screenshot...
[[18:34:38]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:34:38]] [SUCCESS] Screenshot refreshed successfully
[[18:34:38]] [SUCCESS] Screenshot refreshed successfully
[[18:34:38]] [SUCCESS] Screenshot refreshed
[[18:34:38]] [INFO] Refreshing screenshot...
[[18:34:34]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:34:34]] [SUCCESS] Screenshot refreshed successfully
[[18:34:34]] [SUCCESS] Screenshot refreshed successfully
[[18:34:34]] [SUCCESS] Screenshot refreshed
[[18:34:34]] [INFO] Refreshing screenshot...
[[18:34:30]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:34:30]] [SUCCESS] Screenshot refreshed successfully
[[18:34:30]] [SUCCESS] Screenshot refreshed successfully
[[18:34:29]] [SUCCESS] Screenshot refreshed
[[18:34:29]] [INFO] Refreshing screenshot...
[[18:34:23]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:34:23]] [SUCCESS] Screenshot refreshed successfully
[[18:34:23]] [SUCCESS] Screenshot refreshed successfully
[[18:34:23]] [SUCCESS] Screenshot refreshed
[[18:34:23]] [INFO] Refreshing screenshot...
[[18:34:16]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:34:16]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:34:16]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:34:16]] [INFO] ubySifeF65=running
[[18:34:16]] [INFO] Executing action 449/643: cleanupSteps action
[[18:34:15]] [SUCCESS] Screenshot refreshed successfully
[[18:34:15]] [SUCCESS] Screenshot refreshed successfully
[[18:34:15]] [SUCCESS] Screenshot refreshed
[[18:34:15]] [INFO] Refreshing screenshot...
[[18:34:15]] [INFO] xyHVihJMBi=pass
[[18:34:11]] [INFO] xyHVihJMBi=running
[[18:34:11]] [INFO] Executing action 448/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:34:11]] [SUCCESS] Screenshot refreshed successfully
[[18:34:11]] [SUCCESS] Screenshot refreshed successfully
[[18:34:11]] [SUCCESS] Screenshot refreshed
[[18:34:11]] [INFO] Refreshing screenshot...
[[18:34:11]] [INFO] mWeLQtXiL6=pass
[[18:34:04]] [INFO] mWeLQtXiL6=running
[[18:34:04]] [INFO] Executing action 447/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:34:04]] [SUCCESS] Screenshot refreshed successfully
[[18:34:04]] [SUCCESS] Screenshot refreshed successfully
[[18:34:04]] [SUCCESS] Screenshot refreshed
[[18:34:04]] [INFO] Refreshing screenshot...
[[18:34:04]] [INFO] F4NGh9HrLw=pass
[[18:33:59]] [INFO] F4NGh9HrLw=running
[[18:33:59]] [INFO] Executing action 446/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:33:59]] [SUCCESS] Screenshot refreshed successfully
[[18:33:59]] [SUCCESS] Screenshot refreshed successfully
[[18:33:59]] [SUCCESS] Screenshot refreshed
[[18:33:59]] [INFO] Refreshing screenshot...
[[18:33:59]] [INFO] 0f2FSZYjWq=pass
[[18:33:43]] [INFO] 0f2FSZYjWq=running
[[18:33:43]] [INFO] Executing action 445/643: Check if element with text="Melbourne" exists
[[18:33:43]] [SUCCESS] Screenshot refreshed successfully
[[18:33:43]] [SUCCESS] Screenshot refreshed successfully
[[18:33:43]] [SUCCESS] Screenshot refreshed
[[18:33:43]] [INFO] Refreshing screenshot...
[[18:33:43]] [INFO] Tebej51pT2=pass
[[18:33:39]] [INFO] Tebej51pT2=running
[[18:33:39]] [INFO] Executing action 444/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[18:33:39]] [SUCCESS] Screenshot refreshed successfully
[[18:33:39]] [SUCCESS] Screenshot refreshed successfully
[[18:33:39]] [SUCCESS] Screenshot refreshed
[[18:33:39]] [INFO] Refreshing screenshot...
[[18:33:39]] [INFO] I4gwigwXSj=pass
[[18:33:36]] [INFO] I4gwigwXSj=running
[[18:33:36]] [INFO] Executing action 443/643: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[18:33:35]] [SUCCESS] Screenshot refreshed successfully
[[18:33:35]] [SUCCESS] Screenshot refreshed successfully
[[18:33:35]] [SUCCESS] Screenshot refreshed
[[18:33:35]] [INFO] Refreshing screenshot...
[[18:33:35]] [INFO] eVytJrry9x=pass
[[18:33:31]] [INFO] eVytJrry9x=running
[[18:33:31]] [INFO] Executing action 442/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:33:31]] [SUCCESS] Screenshot refreshed successfully
[[18:33:31]] [SUCCESS] Screenshot refreshed successfully
[[18:33:31]] [SUCCESS] Screenshot refreshed
[[18:33:31]] [INFO] Refreshing screenshot...
[[18:33:31]] [INFO] s8h8VDUIOC=pass
[[18:33:27]] [INFO] s8h8VDUIOC=running
[[18:33:27]] [INFO] Executing action 441/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:33:26]] [SUCCESS] Screenshot refreshed successfully
[[18:33:26]] [SUCCESS] Screenshot refreshed successfully
[[18:33:26]] [SUCCESS] Screenshot refreshed
[[18:33:26]] [INFO] Refreshing screenshot...
[[18:33:26]] [INFO] bkU728TrRF=pass
[[18:33:20]] [INFO] bkU728TrRF=running
[[18:33:20]] [INFO] Executing action 440/643: Tap on element with accessibility_id: Done
[[18:33:20]] [SUCCESS] Screenshot refreshed successfully
[[18:33:20]] [SUCCESS] Screenshot refreshed successfully
[[18:33:20]] [SUCCESS] Screenshot refreshed
[[18:33:20]] [INFO] Refreshing screenshot...
[[18:33:20]] [INFO] ZWpYNcpbFA=pass
[[18:33:15]] [INFO] ZWpYNcpbFA=running
[[18:33:15]] [INFO] Executing action 439/643: Tap on Text: "VIC"
[[18:33:15]] [SUCCESS] Screenshot refreshed successfully
[[18:33:15]] [SUCCESS] Screenshot refreshed successfully
[[18:33:15]] [SUCCESS] Screenshot refreshed
[[18:33:15]] [INFO] Refreshing screenshot...
[[18:33:15]] [INFO] Wld5Urg70o=pass
[[18:33:08]] [INFO] Wld5Urg70o=running
[[18:33:08]] [INFO] Executing action 438/643: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[18:33:08]] [SUCCESS] Screenshot refreshed successfully
[[18:33:08]] [SUCCESS] Screenshot refreshed successfully
[[18:33:08]] [SUCCESS] Screenshot refreshed
[[18:33:08]] [INFO] Refreshing screenshot...
[[18:33:08]] [INFO] QpBLC6BStn=pass
[[18:33:01]] [INFO] QpBLC6BStn=running
[[18:33:01]] [INFO] Executing action 437/643: Tap on element with accessibility_id: delete
[[18:33:01]] [SUCCESS] Screenshot refreshed successfully
[[18:33:01]] [SUCCESS] Screenshot refreshed successfully
[[18:33:01]] [SUCCESS] Screenshot refreshed
[[18:33:01]] [INFO] Refreshing screenshot...
[[18:33:01]] [INFO] G4A3KBlXHq=pass
[[18:32:56]] [INFO] G4A3KBlXHq=running
[[18:32:56]] [INFO] Executing action 436/643: Tap on Text: "Nearby"
[[18:32:56]] [SUCCESS] Screenshot refreshed successfully
[[18:32:56]] [SUCCESS] Screenshot refreshed successfully
[[18:32:56]] [SUCCESS] Screenshot refreshed
[[18:32:56]] [INFO] Refreshing screenshot...
[[18:32:56]] [INFO] uArzgeZYf7=pass
[[18:32:53]] [INFO] uArzgeZYf7=running
[[18:32:53]] [INFO] Executing action 435/643: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[18:32:52]] [SUCCESS] Screenshot refreshed successfully
[[18:32:52]] [SUCCESS] Screenshot refreshed successfully
[[18:32:52]] [SUCCESS] Screenshot refreshed
[[18:32:52]] [INFO] Refreshing screenshot...
[[18:32:52]] [INFO] 3gJsiap2Ds=pass
[[18:32:48]] [INFO] 3gJsiap2Ds=running
[[18:32:48]] [INFO] Executing action 434/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[18:32:48]] [SUCCESS] Screenshot refreshed successfully
[[18:32:48]] [SUCCESS] Screenshot refreshed successfully
[[18:32:48]] [SUCCESS] Screenshot refreshed
[[18:32:48]] [INFO] Refreshing screenshot...
[[18:32:48]] [INFO] EReijW5iNX=pass
[[18:32:36]] [INFO] EReijW5iNX=running
[[18:32:36]] [INFO] Executing action 433/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:32:36]] [SUCCESS] Screenshot refreshed successfully
[[18:32:36]] [SUCCESS] Screenshot refreshed successfully
[[18:32:36]] [SUCCESS] Screenshot refreshed
[[18:32:36]] [INFO] Refreshing screenshot...
[[18:32:36]] [INFO] 94ikwhIEE2=pass
[[18:32:32]] [INFO] 94ikwhIEE2=running
[[18:32:32]] [INFO] Executing action 432/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:32:31]] [SUCCESS] Screenshot refreshed successfully
[[18:32:31]] [SUCCESS] Screenshot refreshed successfully
[[18:32:31]] [SUCCESS] Screenshot refreshed
[[18:32:31]] [INFO] Refreshing screenshot...
[[18:32:31]] [INFO] q8oldD8uZt=pass
[[18:32:28]] [INFO] q8oldD8uZt=running
[[18:32:28]] [INFO] Executing action 431/643: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[18:32:28]] [SUCCESS] Screenshot refreshed successfully
[[18:32:28]] [SUCCESS] Screenshot refreshed successfully
[[18:32:27]] [SUCCESS] Screenshot refreshed
[[18:32:27]] [INFO] Refreshing screenshot...
[[18:32:27]] [INFO] Jf2wJyOphY=pass
[[18:32:09]] [INFO] Jf2wJyOphY=running
[[18:32:09]] [INFO] Executing action 430/643: Tap on element with accessibility_id: Add to bag
[[18:32:09]] [SUCCESS] Screenshot refreshed successfully
[[18:32:09]] [SUCCESS] Screenshot refreshed successfully
[[18:32:09]] [SUCCESS] Screenshot refreshed
[[18:32:09]] [INFO] Refreshing screenshot...
[[18:32:09]] [INFO] eRCmRhc3re=pass
[[18:31:56]] [INFO] eRCmRhc3re=running
[[18:31:56]] [INFO] Executing action 429/643: Check if element with text="Broadway" exists
[[18:31:55]] [SUCCESS] Screenshot refreshed successfully
[[18:31:55]] [SUCCESS] Screenshot refreshed successfully
[[18:31:55]] [SUCCESS] Screenshot refreshed
[[18:31:55]] [INFO] Refreshing screenshot...
[[18:31:55]] [INFO] ORI6ZFMBK1=pass
[[18:31:51]] [INFO] ORI6ZFMBK1=running
[[18:31:51]] [INFO] Executing action 428/643: Tap on Text: "Save"
[[18:31:51]] [SUCCESS] Screenshot refreshed successfully
[[18:31:51]] [SUCCESS] Screenshot refreshed successfully
[[18:31:51]] [SUCCESS] Screenshot refreshed
[[18:31:51]] [INFO] Refreshing screenshot...
[[18:31:51]] [INFO] hr0IVckpYI=pass
[[18:31:46]] [INFO] hr0IVckpYI=running
[[18:31:46]] [INFO] Executing action 427/643: Wait till accessibility_id=btnSaveOrContinue
[[18:31:46]] [SUCCESS] Screenshot refreshed successfully
[[18:31:46]] [SUCCESS] Screenshot refreshed successfully
[[18:31:45]] [SUCCESS] Screenshot refreshed
[[18:31:45]] [INFO] Refreshing screenshot...
[[18:31:45]] [INFO] H0ODFz7sWJ=pass
[[18:31:41]] [INFO] H0ODFz7sWJ=running
[[18:31:41]] [INFO] Executing action 426/643: Tap on Text: "2000"
[[18:31:41]] [SUCCESS] Screenshot refreshed successfully
[[18:31:41]] [SUCCESS] Screenshot refreshed successfully
[[18:31:41]] [SUCCESS] Screenshot refreshed
[[18:31:41]] [INFO] Refreshing screenshot...
[[18:31:41]] [INFO] uZHvvAzVfx=pass
[[18:31:36]] [INFO] uZHvvAzVfx=running
[[18:31:36]] [INFO] Executing action 425/643: textClear action
[[18:31:36]] [SUCCESS] Screenshot refreshed successfully
[[18:31:36]] [SUCCESS] Screenshot refreshed successfully
[[18:31:36]] [SUCCESS] Screenshot refreshed
[[18:31:36]] [INFO] Refreshing screenshot...
[[18:31:36]] [INFO] WmNWcsWVHv=pass
[[18:31:30]] [INFO] WmNWcsWVHv=running
[[18:31:30]] [INFO] Executing action 424/643: Tap on element with accessibility_id: Search suburb or postcode
[[18:31:30]] [SUCCESS] Screenshot refreshed successfully
[[18:31:30]] [SUCCESS] Screenshot refreshed successfully
[[18:31:30]] [SUCCESS] Screenshot refreshed
[[18:31:30]] [INFO] Refreshing screenshot...
[[18:31:30]] [INFO] lnjoz8hHUU=pass
[[18:31:25]] [INFO] lnjoz8hHUU=running
[[18:31:25]] [INFO] Executing action 423/643: Tap on Text: "Edit"
[[18:31:25]] [SUCCESS] Screenshot refreshed successfully
[[18:31:25]] [SUCCESS] Screenshot refreshed successfully
[[18:31:24]] [SUCCESS] Screenshot refreshed
[[18:31:24]] [INFO] Refreshing screenshot...
[[18:31:24]] [INFO] letbbewlnA=pass
[[18:31:20]] [INFO] letbbewlnA=running
[[18:31:20]] [INFO] Executing action 422/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:31:19]] [SUCCESS] Screenshot refreshed successfully
[[18:31:19]] [SUCCESS] Screenshot refreshed successfully
[[18:31:19]] [SUCCESS] Screenshot refreshed
[[18:31:19]] [INFO] Refreshing screenshot...
[[18:31:19]] [INFO] trBISwJ8eZ=pass
[[18:31:15]] [INFO] trBISwJ8eZ=running
[[18:31:15]] [INFO] Executing action 421/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:31:15]] [SUCCESS] Screenshot refreshed successfully
[[18:31:15]] [SUCCESS] Screenshot refreshed successfully
[[18:31:14]] [SUCCESS] Screenshot refreshed
[[18:31:14]] [INFO] Refreshing screenshot...
[[18:31:14]] [INFO] foVGMl9wvu=pass
[[18:31:11]] [INFO] foVGMl9wvu=running
[[18:31:11]] [INFO] Executing action 420/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:31:11]] [SUCCESS] Screenshot refreshed successfully
[[18:31:11]] [SUCCESS] Screenshot refreshed successfully
[[18:31:10]] [SUCCESS] Screenshot refreshed
[[18:31:10]] [INFO] Refreshing screenshot...
[[18:31:10]] [INFO] 73NABkfWyY=pass
[[18:30:56]] [INFO] 73NABkfWyY=running
[[18:30:56]] [INFO] Executing action 419/643: Check if element with text="Tarneit" exists
[[18:30:56]] [SUCCESS] Screenshot refreshed successfully
[[18:30:56]] [SUCCESS] Screenshot refreshed successfully
[[18:30:55]] [SUCCESS] Screenshot refreshed
[[18:30:55]] [INFO] Refreshing screenshot...
[[18:30:55]] [INFO] pKjXoj4mNg=pass
[[18:30:51]] [INFO] pKjXoj4mNg=running
[[18:30:51]] [INFO] Executing action 418/643: Tap on Text: "Save"
[[18:30:51]] [SUCCESS] Screenshot refreshed successfully
[[18:30:51]] [SUCCESS] Screenshot refreshed successfully
[[18:30:51]] [SUCCESS] Screenshot refreshed
[[18:30:51]] [INFO] Refreshing screenshot...
[[18:30:51]] [INFO] M3dXqigqRv=pass
[[18:30:46]] [INFO] M3dXqigqRv=running
[[18:30:46]] [INFO] Executing action 417/643: Wait till accessibility_id=btnSaveOrContinue
[[18:30:46]] [SUCCESS] Screenshot refreshed successfully
[[18:30:46]] [SUCCESS] Screenshot refreshed successfully
[[18:30:46]] [SUCCESS] Screenshot refreshed
[[18:30:46]] [INFO] Refreshing screenshot...
[[18:30:46]] [INFO] GYRHQr7TWx=pass
[[18:30:41]] [INFO] GYRHQr7TWx=running
[[18:30:41]] [INFO] Executing action 416/643: Tap on Text: "current"
[[18:30:41]] [SUCCESS] Screenshot refreshed successfully
[[18:30:41]] [SUCCESS] Screenshot refreshed successfully
[[18:30:41]] [SUCCESS] Screenshot refreshed
[[18:30:41]] [INFO] Refreshing screenshot...
[[18:30:41]] [INFO] kiM0WyWE9I=pass
[[18:30:36]] [INFO] kiM0WyWE9I=running
[[18:30:36]] [INFO] Executing action 415/643: Wait till accessibility_id=btnCurrentLocationButton
[[18:30:36]] [SUCCESS] Screenshot refreshed successfully
[[18:30:36]] [SUCCESS] Screenshot refreshed successfully
[[18:30:36]] [SUCCESS] Screenshot refreshed
[[18:30:36]] [INFO] Refreshing screenshot...
[[18:30:36]] [INFO] VkUKQbf1Qt=pass
[[18:30:31]] [INFO] VkUKQbf1Qt=running
[[18:30:31]] [INFO] Executing action 414/643: Tap on Text: "Edit"
[[18:30:31]] [SUCCESS] Screenshot refreshed successfully
[[18:30:31]] [SUCCESS] Screenshot refreshed successfully
[[18:30:31]] [SUCCESS] Screenshot refreshed
[[18:30:31]] [INFO] Refreshing screenshot...
[[18:30:31]] [INFO] C6JHhLdWTv=pass
[[18:30:27]] [INFO] C6JHhLdWTv=running
[[18:30:27]] [INFO] Executing action 413/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:30:26]] [SUCCESS] Screenshot refreshed successfully
[[18:30:26]] [SUCCESS] Screenshot refreshed successfully
[[18:30:26]] [SUCCESS] Screenshot refreshed
[[18:30:26]] [INFO] Refreshing screenshot...
[[18:30:26]] [INFO] IupxLP2Jsr=pass
[[18:30:22]] [INFO] IupxLP2Jsr=running
[[18:30:22]] [INFO] Executing action 412/643: iOS Function: text - Text: "Uno card"
[[18:30:22]] [SUCCESS] Screenshot refreshed successfully
[[18:30:22]] [SUCCESS] Screenshot refreshed successfully
[[18:30:22]] [SUCCESS] Screenshot refreshed
[[18:30:22]] [INFO] Refreshing screenshot...
[[18:30:22]] [INFO] 70iOOakiG7=pass
[[18:30:13]] [INFO] 70iOOakiG7=running
[[18:30:13]] [INFO] Executing action 411/643: Tap on Text: "Find"
[[18:30:13]] [SUCCESS] Screenshot refreshed successfully
[[18:30:13]] [SUCCESS] Screenshot refreshed successfully
[[18:30:13]] [SUCCESS] Screenshot refreshed
[[18:30:13]] [INFO] Refreshing screenshot...
[[18:30:13]] [INFO] vL26X6PBjc=pass
[[18:30:06]] [INFO] vL26X6PBjc=running
[[18:30:06]] [INFO] Executing action 410/643: Tap if locator exists: accessibility_id="btnUpdate"
[[18:30:06]] [SUCCESS] Screenshot refreshed successfully
[[18:30:06]] [SUCCESS] Screenshot refreshed successfully
[[18:30:06]] [SUCCESS] Screenshot refreshed
[[18:30:06]] [INFO] Refreshing screenshot...
[[18:30:06]] [INFO] E2jpN7BioW=pass
[[18:30:01]] [INFO] E2jpN7BioW=running
[[18:30:01]] [INFO] Executing action 409/643: Tap on Text: "Save"
[[18:30:01]] [SUCCESS] Screenshot refreshed successfully
[[18:30:01]] [SUCCESS] Screenshot refreshed successfully
[[18:30:01]] [SUCCESS] Screenshot refreshed
[[18:30:01]] [INFO] Refreshing screenshot...
[[18:30:01]] [INFO] Sl6eiqZkRm=pass
[[18:29:56]] [INFO] Sl6eiqZkRm=running
[[18:29:56]] [INFO] Executing action 408/643: Wait till accessibility_id=btnSaveOrContinue
[[18:29:56]] [SUCCESS] Screenshot refreshed successfully
[[18:29:56]] [SUCCESS] Screenshot refreshed successfully
[[18:29:56]] [SUCCESS] Screenshot refreshed
[[18:29:56]] [INFO] Refreshing screenshot...
[[18:29:56]] [INFO] mw9GQ4mzRE=pass
[[18:29:52]] [INFO] mw9GQ4mzRE=running
[[18:29:52]] [INFO] Executing action 407/643: Tap on Text: "2000"
[[18:29:51]] [SUCCESS] Screenshot refreshed successfully
[[18:29:51]] [SUCCESS] Screenshot refreshed successfully
[[18:29:51]] [SUCCESS] Screenshot refreshed
[[18:29:51]] [INFO] Refreshing screenshot...
[[18:29:51]] [INFO] kbdEPCPYod=pass
[[18:29:46]] [INFO] kbdEPCPYod=running
[[18:29:46]] [INFO] Executing action 406/643: textClear action
[[18:29:46]] [SUCCESS] Screenshot refreshed successfully
[[18:29:46]] [SUCCESS] Screenshot refreshed successfully
[[18:29:46]] [SUCCESS] Screenshot refreshed
[[18:29:46]] [INFO] Refreshing screenshot...
[[18:29:46]] [INFO] 8WCusTZ8q9=pass
[[18:29:40]] [INFO] 8WCusTZ8q9=running
[[18:29:40]] [INFO] Executing action 405/643: Tap on element with accessibility_id: Search suburb or postcode
[[18:29:40]] [SUCCESS] Screenshot refreshed successfully
[[18:29:40]] [SUCCESS] Screenshot refreshed successfully
[[18:29:40]] [SUCCESS] Screenshot refreshed
[[18:29:40]] [INFO] Refreshing screenshot...
[[18:29:40]] [INFO] QMXBlswP6H=pass
[[18:29:36]] [INFO] QMXBlswP6H=running
[[18:29:36]] [INFO] Executing action 404/643: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[18:29:36]] [SUCCESS] Screenshot refreshed successfully
[[18:29:36]] [SUCCESS] Screenshot refreshed successfully
[[18:29:35]] [SUCCESS] Screenshot refreshed
[[18:29:35]] [INFO] Refreshing screenshot...
[[18:29:35]] [INFO] m0956RsrdM=pass
[[18:29:32]] [SUCCESS] Screenshot refreshed successfully
[[18:29:32]] [SUCCESS] Screenshot refreshed successfully
[[18:29:32]] [INFO] m0956RsrdM=running
[[18:29:32]] [INFO] Executing action 403/643: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[18:29:32]] [SUCCESS] Screenshot refreshed
[[18:29:32]] [INFO] Refreshing screenshot...
[[18:29:32]] [SUCCESS] Screenshot refreshed successfully
[[18:29:32]] [SUCCESS] Screenshot refreshed successfully
[[18:29:32]] [SUCCESS] Screenshot refreshed
[[18:29:32]] [INFO] Refreshing screenshot...
[[18:29:27]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:29:27]] [SUCCESS] Screenshot refreshed successfully
[[18:29:27]] [SUCCESS] Screenshot refreshed successfully
[[18:29:27]] [SUCCESS] Screenshot refreshed
[[18:29:27]] [INFO] Refreshing screenshot...
[[18:29:23]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:29:22]] [SUCCESS] Screenshot refreshed successfully
[[18:29:22]] [SUCCESS] Screenshot refreshed successfully
[[18:29:22]] [SUCCESS] Screenshot refreshed
[[18:29:22]] [INFO] Refreshing screenshot...
[[18:29:18]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:29:17]] [SUCCESS] Screenshot refreshed successfully
[[18:29:17]] [SUCCESS] Screenshot refreshed successfully
[[18:29:17]] [SUCCESS] Screenshot refreshed
[[18:29:17]] [INFO] Refreshing screenshot...
[[18:29:13]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:29:13]] [SUCCESS] Screenshot refreshed successfully
[[18:29:13]] [SUCCESS] Screenshot refreshed successfully
[[18:29:13]] [SUCCESS] Screenshot refreshed
[[18:29:13]] [INFO] Refreshing screenshot...
[[18:29:07]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:29:07]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:29:07]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:29:07]] [INFO] HlWGryBWT9=running
[[18:29:07]] [INFO] Executing action 402/643: Execute Test Case: Kmart-Signin (5 steps)
[[18:29:07]] [SUCCESS] Screenshot refreshed successfully
[[18:29:07]] [SUCCESS] Screenshot refreshed successfully
[[18:29:07]] [SUCCESS] Screenshot refreshed
[[18:29:07]] [INFO] Refreshing screenshot...
[[18:29:07]] [INFO] Azb1flbIJJ=pass
[[18:29:03]] [INFO] Azb1flbIJJ=running
[[18:29:03]] [INFO] Executing action 401/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:29:03]] [SUCCESS] Screenshot refreshed successfully
[[18:29:03]] [SUCCESS] Screenshot refreshed successfully
[[18:29:03]] [SUCCESS] Screenshot refreshed
[[18:29:03]] [INFO] Refreshing screenshot...
[[18:29:03]] [INFO] 2xC5fLfLe8=pass
[[18:29:00]] [INFO] 2xC5fLfLe8=running
[[18:29:00]] [INFO] Executing action 400/643: iOS Function: alert_accept
[[18:29:00]] [SUCCESS] Screenshot refreshed successfully
[[18:29:00]] [SUCCESS] Screenshot refreshed successfully
[[18:29:00]] [SUCCESS] Screenshot refreshed
[[18:29:00]] [INFO] Refreshing screenshot...
[[18:29:00]] [INFO] Y8vz7AJD1i=pass
[[18:28:52]] [INFO] Y8vz7AJD1i=running
[[18:28:52]] [INFO] Executing action 399/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:28:51]] [SUCCESS] Screenshot refreshed successfully
[[18:28:51]] [SUCCESS] Screenshot refreshed successfully
[[18:28:51]] [SUCCESS] Screenshot refreshed
[[18:28:51]] [INFO] Refreshing screenshot...
[[18:28:51]] [INFO] H9fy9qcFbZ=pass
[[18:28:38]] [SUCCESS] Screenshot refreshed successfully
[[18:28:38]] [SUCCESS] Screenshot refreshed successfully
[[18:28:38]] [INFO] H9fy9qcFbZ=running
[[18:28:38]] [INFO] Executing action 398/643: Restart app: env[appid]
[[18:28:38]] [SUCCESS] Screenshot refreshed
[[18:28:38]] [INFO] Refreshing screenshot...
[[18:28:37]] [SUCCESS] Screenshot refreshed successfully
[[18:28:37]] [SUCCESS] Screenshot refreshed successfully
[[18:28:37]] [SUCCESS] Screenshot refreshed
[[18:28:37]] [INFO] Refreshing screenshot...
[[18:28:35]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:28:34]] [SUCCESS] Screenshot refreshed successfully
[[18:28:34]] [SUCCESS] Screenshot refreshed successfully
[[18:28:34]] [SUCCESS] Screenshot refreshed
[[18:28:34]] [INFO] Refreshing screenshot...
[[18:28:22]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:28:22]] [SUCCESS] Screenshot refreshed successfully
[[18:28:22]] [SUCCESS] Screenshot refreshed successfully
[[18:28:21]] [SUCCESS] Screenshot refreshed
[[18:28:21]] [INFO] Refreshing screenshot...
[[18:28:18]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:28:18]] [SUCCESS] Screenshot refreshed successfully
[[18:28:18]] [SUCCESS] Screenshot refreshed successfully
[[18:28:17]] [SUCCESS] Screenshot refreshed
[[18:28:17]] [INFO] Refreshing screenshot...
[[18:28:14]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:28:13]] [SUCCESS] Screenshot refreshed successfully
[[18:28:13]] [SUCCESS] Screenshot refreshed successfully
[[18:28:13]] [SUCCESS] Screenshot refreshed
[[18:28:13]] [INFO] Refreshing screenshot...
[[18:28:07]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:28:07]] [SUCCESS] Screenshot refreshed successfully
[[18:28:07]] [SUCCESS] Screenshot refreshed successfully
[[18:28:06]] [SUCCESS] Screenshot refreshed
[[18:28:06]] [INFO] Refreshing screenshot...
[[18:28:01]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:28:01]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:28:01]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:28:01]] [INFO] OMgc2gHHyq=running
[[18:28:01]] [INFO] Executing action 397/643: cleanupSteps action
[[18:28:00]] [SUCCESS] Screenshot refreshed successfully
[[18:28:00]] [SUCCESS] Screenshot refreshed successfully
[[18:28:00]] [SUCCESS] Screenshot refreshed
[[18:28:00]] [INFO] Refreshing screenshot...
[[18:28:00]] [INFO] x4yLCZHaCR=pass
[[18:27:58]] [INFO] x4yLCZHaCR=running
[[18:27:58]] [INFO] Executing action 396/643: Terminate app: env[appid]
[[18:27:57]] [SUCCESS] Screenshot refreshed successfully
[[18:27:57]] [SUCCESS] Screenshot refreshed successfully
[[18:27:57]] [SUCCESS] Screenshot refreshed
[[18:27:57]] [INFO] Refreshing screenshot...
[[18:27:57]] [INFO] 2p13JoJbbA=pass
[[18:27:53]] [INFO] 2p13JoJbbA=running
[[18:27:53]] [INFO] Executing action 395/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:27:53]] [SUCCESS] Screenshot refreshed successfully
[[18:27:53]] [SUCCESS] Screenshot refreshed successfully
[[18:27:53]] [SUCCESS] Screenshot refreshed
[[18:27:53]] [INFO] Refreshing screenshot...
[[18:27:53]] [INFO] qHdMgerbTE=pass
[[18:27:49]] [INFO] qHdMgerbTE=running
[[18:27:49]] [INFO] Executing action 394/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:27:48]] [SUCCESS] Screenshot refreshed successfully
[[18:27:48]] [SUCCESS] Screenshot refreshed successfully
[[18:27:48]] [SUCCESS] Screenshot refreshed
[[18:27:48]] [INFO] Refreshing screenshot...
[[18:27:48]] [INFO] F4NGh9HrLw=pass
[[18:27:44]] [INFO] F4NGh9HrLw=running
[[18:27:44]] [INFO] Executing action 393/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:27:44]] [SUCCESS] Screenshot refreshed successfully
[[18:27:44]] [SUCCESS] Screenshot refreshed successfully
[[18:27:44]] [SUCCESS] Screenshot refreshed
[[18:27:44]] [INFO] Refreshing screenshot...
[[18:27:44]] [INFO] 7mnBGa2GCk=pass
[[18:27:32]] [INFO] 7mnBGa2GCk=running
[[18:27:32]] [INFO] Executing action 392/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Save my location"]"
[[18:27:32]] [SUCCESS] Screenshot refreshed successfully
[[18:27:32]] [SUCCESS] Screenshot refreshed successfully
[[18:27:32]] [SUCCESS] Screenshot refreshed
[[18:27:32]] [INFO] Refreshing screenshot...
[[18:27:32]] [INFO] tWq2Qzn22D=pass
[[18:27:28]] [INFO] tWq2Qzn22D=running
[[18:27:28]] [INFO] Executing action 391/643: Tap on image: env[device-back-img]
[[18:27:28]] [SUCCESS] Screenshot refreshed successfully
[[18:27:28]] [SUCCESS] Screenshot refreshed successfully
[[18:27:27]] [SUCCESS] Screenshot refreshed
[[18:27:27]] [INFO] Refreshing screenshot...
[[18:27:27]] [INFO] ysJIY9A3gq=pass
[[18:27:15]] [INFO] ysJIY9A3gq=running
[[18:27:15]] [INFO] Executing action 390/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]"
[[18:27:15]] [SUCCESS] Screenshot refreshed successfully
[[18:27:15]] [SUCCESS] Screenshot refreshed successfully
[[18:27:15]] [SUCCESS] Screenshot refreshed
[[18:27:15]] [INFO] Refreshing screenshot...
[[18:27:15]] [INFO] jmKjclMUWT=pass
[[18:27:10]] [INFO] jmKjclMUWT=running
[[18:27:10]] [INFO] Executing action 389/643: Tap on Text: "current"
[[18:27:10]] [SUCCESS] Screenshot refreshed successfully
[[18:27:10]] [SUCCESS] Screenshot refreshed successfully
[[18:27:10]] [SUCCESS] Screenshot refreshed
[[18:27:10]] [INFO] Refreshing screenshot...
[[18:27:10]] [INFO] UoH0wdtcLk=pass
[[18:27:06]] [INFO] UoH0wdtcLk=running
[[18:27:06]] [INFO] Executing action 388/643: Tap on Text: "Edit"
[[18:27:05]] [SUCCESS] Screenshot refreshed successfully
[[18:27:05]] [SUCCESS] Screenshot refreshed successfully
[[18:27:05]] [SUCCESS] Screenshot refreshed
[[18:27:05]] [INFO] Refreshing screenshot...
[[18:27:05]] [INFO] U48qCNydwd=pass
[[18:27:00]] [INFO] U48qCNydwd=running
[[18:27:00]] [INFO] Executing action 387/643: Restart app: env[appid]
[[18:27:00]] [SUCCESS] Screenshot refreshed successfully
[[18:27:00]] [SUCCESS] Screenshot refreshed successfully
[[18:27:00]] [SUCCESS] Screenshot refreshed
[[18:27:00]] [INFO] Refreshing screenshot...
[[18:27:00]] [INFO] XjclKOaCTh=pass
[[18:26:55]] [INFO] XjclKOaCTh=running
[[18:26:55]] [INFO] Executing action 386/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[18:26:55]] [SUCCESS] Screenshot refreshed successfully
[[18:26:55]] [SUCCESS] Screenshot refreshed successfully
[[18:26:55]] [SUCCESS] Screenshot refreshed
[[18:26:55]] [INFO] Refreshing screenshot...
[[18:26:55]] [INFO] q6cKxgMAIn=pass
[[18:26:52]] [INFO] q6cKxgMAIn=running
[[18:26:52]] [INFO] Executing action 385/643: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[18:26:51]] [SUCCESS] Screenshot refreshed successfully
[[18:26:51]] [SUCCESS] Screenshot refreshed successfully
[[18:26:51]] [SUCCESS] Screenshot refreshed
[[18:26:51]] [INFO] Refreshing screenshot...
[[18:26:51]] [INFO] zdh8hKYC1a=pass
[[18:26:47]] [INFO] zdh8hKYC1a=running
[[18:26:47]] [INFO] Executing action 384/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[18:26:47]] [SUCCESS] Screenshot refreshed successfully
[[18:26:47]] [SUCCESS] Screenshot refreshed successfully
[[18:26:47]] [SUCCESS] Screenshot refreshed
[[18:26:47]] [INFO] Refreshing screenshot...
[[18:26:47]] [INFO] P4b2BITpCf=pass
[[18:26:44]] [INFO] P4b2BITpCf=running
[[18:26:44]] [INFO] Executing action 383/643: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[18:26:44]] [SUCCESS] Screenshot refreshed successfully
[[18:26:44]] [SUCCESS] Screenshot refreshed successfully
[[18:26:43]] [SUCCESS] Screenshot refreshed
[[18:26:43]] [INFO] Refreshing screenshot...
[[18:26:43]] [INFO] inrxgdWzXr=pass
[[18:26:39]] [INFO] inrxgdWzXr=running
[[18:26:39]] [INFO] Executing action 382/643: Tap on Text: "Store"
[[18:26:39]] [SUCCESS] Screenshot refreshed successfully
[[18:26:39]] [SUCCESS] Screenshot refreshed successfully
[[18:26:39]] [SUCCESS] Screenshot refreshed
[[18:26:39]] [INFO] Refreshing screenshot...
[[18:26:39]] [INFO] inrxgdWzXr=pass
[[18:26:35]] [INFO] inrxgdWzXr=running
[[18:26:35]] [INFO] Executing action 381/643: Tap on Text: "receipts"
[[18:26:34]] [SUCCESS] Screenshot refreshed successfully
[[18:26:34]] [SUCCESS] Screenshot refreshed successfully
[[18:26:34]] [SUCCESS] Screenshot refreshed
[[18:26:34]] [INFO] Refreshing screenshot...
[[18:26:34]] [INFO] GEMv6goQtW=pass
[[18:26:31]] [INFO] GEMv6goQtW=running
[[18:26:31]] [INFO] Executing action 380/643: Tap on image: env[device-back-img]
[[18:26:30]] [SUCCESS] Screenshot refreshed successfully
[[18:26:30]] [SUCCESS] Screenshot refreshed successfully
[[18:26:30]] [SUCCESS] Screenshot refreshed
[[18:26:30]] [INFO] Refreshing screenshot...
[[18:26:30]] [INFO] DhWa2PCBXE=pass
[[18:26:27]] [INFO] DhWa2PCBXE=running
[[18:26:27]] [INFO] Executing action 379/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[18:26:27]] [SUCCESS] Screenshot refreshed successfully
[[18:26:27]] [SUCCESS] Screenshot refreshed successfully
[[18:26:27]] [SUCCESS] Screenshot refreshed
[[18:26:27]] [INFO] Refreshing screenshot...
[[18:26:27]] [INFO] pk2DLZFBmx=pass
[[18:26:23]] [INFO] pk2DLZFBmx=running
[[18:26:23]] [INFO] Executing action 378/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[18:26:23]] [SUCCESS] Screenshot refreshed successfully
[[18:26:23]] [SUCCESS] Screenshot refreshed successfully
[[18:26:23]] [SUCCESS] Screenshot refreshed
[[18:26:23]] [INFO] Refreshing screenshot...
[[18:26:23]] [INFO] ShJSdXvmVL=pass
[[18:26:19]] [INFO] ShJSdXvmVL=running
[[18:26:19]] [INFO] Executing action 377/643: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[18:26:18]] [SUCCESS] Screenshot refreshed successfully
[[18:26:18]] [SUCCESS] Screenshot refreshed successfully
[[18:26:18]] [SUCCESS] Screenshot refreshed
[[18:26:18]] [INFO] Refreshing screenshot...
[[18:26:18]] [INFO] A57bC3QuEM=pass
[[18:26:14]] [INFO] A57bC3QuEM=running
[[18:26:14]] [INFO] Executing action 376/643: iOS Function: text - Text: "Wonderbaby@5"
[[18:26:13]] [SUCCESS] Screenshot refreshed successfully
[[18:26:13]] [SUCCESS] Screenshot refreshed successfully
[[18:26:13]] [SUCCESS] Screenshot refreshed
[[18:26:13]] [INFO] Refreshing screenshot...
[[18:26:13]] [INFO] d6vTfR4Y0D=pass
[[18:26:09]] [INFO] d6vTfR4Y0D=running
[[18:26:09]] [INFO] Executing action 375/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:26:09]] [SUCCESS] Screenshot refreshed successfully
[[18:26:09]] [SUCCESS] Screenshot refreshed successfully
[[18:26:09]] [SUCCESS] Screenshot refreshed
[[18:26:09]] [INFO] Refreshing screenshot...
[[18:26:09]] [INFO] g2CqCO1Kr6=pass
[[18:26:04]] [INFO] g2CqCO1Kr6=running
[[18:26:04]] [INFO] Executing action 374/643: iOS Function: text - Text: "<EMAIL>"
[[18:26:04]] [SUCCESS] Screenshot refreshed successfully
[[18:26:04]] [SUCCESS] Screenshot refreshed successfully
[[18:26:04]] [SUCCESS] Screenshot refreshed
[[18:26:04]] [INFO] Refreshing screenshot...
[[18:26:04]] [INFO] u928vFzSni=pass
[[18:26:00]] [INFO] u928vFzSni=running
[[18:26:00]] [INFO] Executing action 373/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:25:59]] [SUCCESS] Screenshot refreshed successfully
[[18:25:59]] [SUCCESS] Screenshot refreshed successfully
[[18:25:59]] [SUCCESS] Screenshot refreshed
[[18:25:59]] [INFO] Refreshing screenshot...
[[18:25:59]] [INFO] s0WyiD1w0B=pass
[[18:25:57]] [INFO] s0WyiD1w0B=running
[[18:25:57]] [INFO] Executing action 372/643: iOS Function: alert_accept
[[18:25:56]] [SUCCESS] Screenshot refreshed successfully
[[18:25:56]] [SUCCESS] Screenshot refreshed successfully
[[18:25:56]] [SUCCESS] Screenshot refreshed
[[18:25:56]] [INFO] Refreshing screenshot...
[[18:25:56]] [INFO] gekNSY5O2E=pass
[[18:25:52]] [INFO] gekNSY5O2E=running
[[18:25:52]] [INFO] Executing action 371/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[18:25:52]] [SUCCESS] Screenshot refreshed successfully
[[18:25:52]] [SUCCESS] Screenshot refreshed successfully
[[18:25:52]] [SUCCESS] Screenshot refreshed
[[18:25:52]] [INFO] Refreshing screenshot...
[[18:25:52]] [INFO] VJJ3EXXotU=pass
[[18:25:48]] [INFO] VJJ3EXXotU=running
[[18:25:48]] [INFO] Executing action 370/643: Tap on image: env[device-back-img]
[[18:25:48]] [SUCCESS] Screenshot refreshed successfully
[[18:25:48]] [SUCCESS] Screenshot refreshed successfully
[[18:25:48]] [SUCCESS] Screenshot refreshed
[[18:25:48]] [INFO] Refreshing screenshot...
[[18:25:48]] [INFO] 83tV9A4NOn=pass
[[18:25:45]] [INFO] 83tV9A4NOn=running
[[18:25:45]] [INFO] Executing action 369/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[18:25:45]] [SUCCESS] Screenshot refreshed successfully
[[18:25:45]] [SUCCESS] Screenshot refreshed successfully
[[18:25:44]] [SUCCESS] Screenshot refreshed
[[18:25:44]] [INFO] Refreshing screenshot...
[[18:25:44]] [INFO] aNN0yYFLEd=pass
[[18:25:41]] [INFO] aNN0yYFLEd=running
[[18:25:41]] [INFO] Executing action 368/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[18:25:40]] [SUCCESS] Screenshot refreshed successfully
[[18:25:40]] [SUCCESS] Screenshot refreshed successfully
[[18:25:40]] [SUCCESS] Screenshot refreshed
[[18:25:40]] [INFO] Refreshing screenshot...
[[18:25:40]] [INFO] XJv08Gkucs=pass
[[18:25:37]] [INFO] XJv08Gkucs=running
[[18:25:37]] [INFO] Executing action 367/643: Input text: "<EMAIL>"
[[18:25:37]] [SUCCESS] Screenshot refreshed successfully
[[18:25:37]] [SUCCESS] Screenshot refreshed successfully
[[18:25:36]] [SUCCESS] Screenshot refreshed
[[18:25:36]] [INFO] Refreshing screenshot...
[[18:25:36]] [INFO] kAQ1yIIw3h=pass
[[18:25:33]] [INFO] kAQ1yIIw3h=running
[[18:25:33]] [INFO] Executing action 366/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[18:25:32]] [SUCCESS] Screenshot refreshed successfully
[[18:25:32]] [SUCCESS] Screenshot refreshed successfully
[[18:25:32]] [SUCCESS] Screenshot refreshed
[[18:25:32]] [INFO] Refreshing screenshot...
[[18:25:32]] [INFO] 7YbjwQH1Jc=pass
[[18:25:29]] [INFO] 7YbjwQH1Jc=running
[[18:25:29]] [INFO] Executing action 365/643: Input text: "env[searchorder]"
[[18:25:29]] [SUCCESS] Screenshot refreshed successfully
[[18:25:29]] [SUCCESS] Screenshot refreshed successfully
[[18:25:29]] [SUCCESS] Screenshot refreshed
[[18:25:29]] [INFO] Refreshing screenshot...
[[18:25:29]] [INFO] OmKfD9iBjD=pass
[[18:25:25]] [INFO] OmKfD9iBjD=running
[[18:25:25]] [INFO] Executing action 364/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[18:25:25]] [SUCCESS] Screenshot refreshed successfully
[[18:25:25]] [SUCCESS] Screenshot refreshed successfully
[[18:25:24]] [SUCCESS] Screenshot refreshed
[[18:25:24]] [INFO] Refreshing screenshot...
[[18:25:24]] [INFO] eHLWiRoqqS=pass
[[18:25:21]] [INFO] eHLWiRoqqS=running
[[18:25:21]] [INFO] Executing action 363/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[18:25:21]] [SUCCESS] Screenshot refreshed successfully
[[18:25:21]] [SUCCESS] Screenshot refreshed successfully
[[18:25:20]] [SUCCESS] Screenshot refreshed
[[18:25:20]] [INFO] Refreshing screenshot...
[[18:25:20]] [INFO] F4NGh9HrLw=pass
[[18:25:16]] [INFO] F4NGh9HrLw=running
[[18:25:16]] [INFO] Executing action 362/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:25:16]] [SUCCESS] Screenshot refreshed successfully
[[18:25:16]] [SUCCESS] Screenshot refreshed successfully
[[18:25:16]] [SUCCESS] Screenshot refreshed
[[18:25:16]] [INFO] Refreshing screenshot...
[[18:25:16]] [INFO] 74XW7x54ad=pass
[[18:25:12]] [INFO] 74XW7x54ad=running
[[18:25:12]] [INFO] Executing action 361/643: Tap on image: env[device-back-img]
[[18:25:12]] [SUCCESS] Screenshot refreshed successfully
[[18:25:12]] [SUCCESS] Screenshot refreshed successfully
[[18:25:11]] [SUCCESS] Screenshot refreshed
[[18:25:11]] [INFO] Refreshing screenshot...
[[18:25:11]] [INFO] xUbWFa8Ok2=pass
[[18:25:08]] [INFO] xUbWFa8Ok2=running
[[18:25:08]] [INFO] Executing action 360/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[18:25:08]] [SUCCESS] Screenshot refreshed successfully
[[18:25:08]] [SUCCESS] Screenshot refreshed successfully
[[18:25:08]] [SUCCESS] Screenshot refreshed
[[18:25:08]] [INFO] Refreshing screenshot...
[[18:25:08]] [INFO] RbNtEW6N9T=pass
[[18:25:05]] [INFO] RbNtEW6N9T=running
[[18:25:05]] [INFO] Executing action 359/643: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[18:25:05]] [SUCCESS] Screenshot refreshed successfully
[[18:25:05]] [SUCCESS] Screenshot refreshed successfully
[[18:25:05]] [SUCCESS] Screenshot refreshed
[[18:25:05]] [INFO] Refreshing screenshot...
[[18:25:05]] [INFO] F4NGh9HrLw=pass
[[18:25:02]] [INFO] F4NGh9HrLw=running
[[18:25:02]] [INFO] Executing action 358/643: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[18:25:02]] [SUCCESS] Screenshot refreshed successfully
[[18:25:02]] [SUCCESS] Screenshot refreshed successfully
[[18:25:01]] [SUCCESS] Screenshot refreshed
[[18:25:01]] [INFO] Refreshing screenshot...
[[18:25:01]] [INFO] RlDZFks4Lc=pass
[[18:24:59]] [INFO] RlDZFks4Lc=running
[[18:24:59]] [INFO] Executing action 357/643: iOS Function: alert_accept
[[18:24:59]] [SUCCESS] Screenshot refreshed successfully
[[18:24:59]] [SUCCESS] Screenshot refreshed successfully
[[18:24:59]] [SUCCESS] Screenshot refreshed
[[18:24:59]] [INFO] Refreshing screenshot...
[[18:24:59]] [INFO] Dzn2Q7JTe0=pass
[[18:24:55]] [INFO] Dzn2Q7JTe0=running
[[18:24:55]] [INFO] Executing action 356/643: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[18:24:55]] [SUCCESS] Screenshot refreshed successfully
[[18:24:55]] [SUCCESS] Screenshot refreshed successfully
[[18:24:54]] [SUCCESS] Screenshot refreshed
[[18:24:54]] [INFO] Refreshing screenshot...
[[18:24:54]] [INFO] H9fy9qcFbZ=pass
[[18:24:41]] [SUCCESS] Screenshot refreshed successfully
[[18:24:41]] [SUCCESS] Screenshot refreshed successfully
[[18:24:41]] [INFO] H9fy9qcFbZ=running
[[18:24:41]] [INFO] Executing action 355/643: Restart app: env[appid]
[[18:24:41]] [SUCCESS] Screenshot refreshed
[[18:24:41]] [INFO] Refreshing screenshot...
[[18:24:40]] [SUCCESS] Screenshot refreshed successfully
[[18:24:40]] [SUCCESS] Screenshot refreshed successfully
[[18:24:40]] [SUCCESS] Screenshot refreshed
[[18:24:40]] [INFO] Refreshing screenshot...
[[18:24:38]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:24:37]] [SUCCESS] Screenshot refreshed successfully
[[18:24:37]] [SUCCESS] Screenshot refreshed successfully
[[18:24:37]] [SUCCESS] Screenshot refreshed
[[18:24:37]] [INFO] Refreshing screenshot...
[[18:24:25]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:24:25]] [SUCCESS] Screenshot refreshed successfully
[[18:24:25]] [SUCCESS] Screenshot refreshed successfully
[[18:24:24]] [SUCCESS] Screenshot refreshed
[[18:24:24]] [INFO] Refreshing screenshot...
[[18:24:21]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:24:20]] [SUCCESS] Screenshot refreshed successfully
[[18:24:20]] [SUCCESS] Screenshot refreshed successfully
[[18:24:20]] [SUCCESS] Screenshot refreshed
[[18:24:20]] [INFO] Refreshing screenshot...
[[18:24:17]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:24:16]] [SUCCESS] Screenshot refreshed successfully
[[18:24:16]] [SUCCESS] Screenshot refreshed successfully
[[18:24:16]] [SUCCESS] Screenshot refreshed
[[18:24:16]] [INFO] Refreshing screenshot...
[[18:24:10]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:24:09]] [SUCCESS] Screenshot refreshed successfully
[[18:24:09]] [SUCCESS] Screenshot refreshed successfully
[[18:24:09]] [SUCCESS] Screenshot refreshed
[[18:24:09]] [INFO] Refreshing screenshot...
[[18:24:03]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:24:03]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:24:03]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:24:02]] [INFO] AeQaElnzUN=running
[[18:24:02]] [INFO] Executing action 354/643: cleanupSteps action
[[18:24:02]] [SUCCESS] Screenshot refreshed successfully
[[18:24:02]] [SUCCESS] Screenshot refreshed successfully
[[18:24:02]] [SUCCESS] Screenshot refreshed
[[18:24:02]] [INFO] Refreshing screenshot...
[[18:24:02]] [INFO] BracBsfa3Y=pass
[[18:23:58]] [INFO] BracBsfa3Y=running
[[18:23:58]] [INFO] Executing action 353/643: Tap on Text: "out"
[[18:23:58]] [SUCCESS] Screenshot refreshed successfully
[[18:23:58]] [SUCCESS] Screenshot refreshed successfully
[[18:23:58]] [SUCCESS] Screenshot refreshed
[[18:23:58]] [INFO] Refreshing screenshot...
[[18:23:58]] [INFO] s6tWdQ5URW=pass
[[18:23:51]] [INFO] s6tWdQ5URW=running
[[18:23:51]] [INFO] Executing action 352/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:23:50]] [SUCCESS] Screenshot refreshed successfully
[[18:23:50]] [SUCCESS] Screenshot refreshed successfully
[[18:23:50]] [SUCCESS] Screenshot refreshed
[[18:23:50]] [INFO] Refreshing screenshot...
[[18:23:50]] [INFO] wNGRrfUjpK=pass
[[18:23:47]] [INFO] wNGRrfUjpK=running
[[18:23:47]] [INFO] Executing action 351/643: Tap on image: env[device-back-img]
[[18:23:46]] [SUCCESS] Screenshot refreshed successfully
[[18:23:46]] [SUCCESS] Screenshot refreshed successfully
[[18:23:46]] [SUCCESS] Screenshot refreshed
[[18:23:46]] [INFO] Refreshing screenshot...
[[18:23:46]] [INFO] BracBsfa3Y=pass
[[18:23:42]] [INFO] BracBsfa3Y=running
[[18:23:42]] [INFO] Executing action 350/643: Tap on Text: "Customer"
[[18:23:42]] [SUCCESS] Screenshot refreshed successfully
[[18:23:42]] [SUCCESS] Screenshot refreshed successfully
[[18:23:42]] [SUCCESS] Screenshot refreshed
[[18:23:42]] [INFO] Refreshing screenshot...
[[18:23:42]] [INFO] H4WfwVU8YP=pass
[[18:23:38]] [INFO] H4WfwVU8YP=running
[[18:23:38]] [INFO] Executing action 349/643: Tap on image: banner-close-updated.png
[[18:23:37]] [SUCCESS] Screenshot refreshed successfully
[[18:23:37]] [SUCCESS] Screenshot refreshed successfully
[[18:23:37]] [SUCCESS] Screenshot refreshed
[[18:23:37]] [INFO] Refreshing screenshot...
[[18:23:37]] [INFO] ePyaYpttQA=pass
[[18:23:34]] [INFO] ePyaYpttQA=running
[[18:23:34]] [INFO] Executing action 348/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[18:23:34]] [SUCCESS] Screenshot refreshed successfully
[[18:23:34]] [SUCCESS] Screenshot refreshed successfully
[[18:23:33]] [SUCCESS] Screenshot refreshed
[[18:23:33]] [INFO] Refreshing screenshot...
[[18:23:33]] [INFO] BracBsfa3Y=pass
[[18:23:29]] [INFO] BracBsfa3Y=running
[[18:23:29]] [INFO] Executing action 347/643: Tap on Text: "Invite"
[[18:23:29]] [SUCCESS] Screenshot refreshed successfully
[[18:23:29]] [SUCCESS] Screenshot refreshed successfully
[[18:23:29]] [SUCCESS] Screenshot refreshed
[[18:23:29]] [INFO] Refreshing screenshot...
[[18:23:29]] [INFO] xVbCNStsOP=pass
[[18:23:25]] [INFO] xVbCNStsOP=running
[[18:23:25]] [INFO] Executing action 346/643: Tap on image: env[device-back-img]
[[18:23:25]] [SUCCESS] Screenshot refreshed successfully
[[18:23:25]] [SUCCESS] Screenshot refreshed successfully
[[18:23:25]] [SUCCESS] Screenshot refreshed
[[18:23:25]] [INFO] Refreshing screenshot...
[[18:23:25]] [INFO] 8kQkC2FGyZ=pass
[[18:23:22]] [INFO] 8kQkC2FGyZ=running
[[18:23:22]] [INFO] Executing action 345/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[18:23:21]] [SUCCESS] Screenshot refreshed successfully
[[18:23:21]] [SUCCESS] Screenshot refreshed successfully
[[18:23:21]] [SUCCESS] Screenshot refreshed
[[18:23:21]] [INFO] Refreshing screenshot...
[[18:23:21]] [INFO] PgjJCrKFYo=pass
[[18:23:17]] [INFO] PgjJCrKFYo=running
[[18:23:17]] [INFO] Executing action 344/643: Tap on Text: "VIC"
[[18:23:16]] [SUCCESS] Screenshot refreshed successfully
[[18:23:16]] [SUCCESS] Screenshot refreshed successfully
[[18:23:16]] [SUCCESS] Screenshot refreshed
[[18:23:16]] [INFO] Refreshing screenshot...
[[18:23:16]] [INFO] 3Si0csRNaw=pass
[[18:23:10]] [INFO] 3Si0csRNaw=running
[[18:23:10]] [INFO] Executing action 343/643: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[18:23:09]] [SUCCESS] Screenshot refreshed successfully
[[18:23:09]] [SUCCESS] Screenshot refreshed successfully
[[18:23:09]] [SUCCESS] Screenshot refreshed
[[18:23:09]] [INFO] Refreshing screenshot...
[[18:23:09]] [INFO] BracBsfa3Y=pass
[[18:23:05]] [INFO] BracBsfa3Y=running
[[18:23:05]] [INFO] Executing action 342/643: Tap on Text: "Nearby"
[[18:23:04]] [SUCCESS] Screenshot refreshed successfully
[[18:23:04]] [SUCCESS] Screenshot refreshed successfully
[[18:23:04]] [SUCCESS] Screenshot refreshed
[[18:23:04]] [INFO] Refreshing screenshot...
[[18:23:04]] [INFO] BracBsfa3Y=pass
[[18:23:00]] [INFO] BracBsfa3Y=running
[[18:23:00]] [INFO] Executing action 341/643: Tap on Text: "locator"
[[18:23:00]] [SUCCESS] Screenshot refreshed successfully
[[18:23:00]] [SUCCESS] Screenshot refreshed successfully
[[18:23:00]] [SUCCESS] Screenshot refreshed
[[18:23:00]] [INFO] Refreshing screenshot...
[[18:23:00]] [INFO] s6tWdQ5URW=pass
[[18:22:53]] [INFO] s6tWdQ5URW=running
[[18:22:53]] [INFO] Executing action 340/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:22:53]] [SUCCESS] Screenshot refreshed successfully
[[18:22:53]] [SUCCESS] Screenshot refreshed successfully
[[18:22:52]] [SUCCESS] Screenshot refreshed
[[18:22:52]] [INFO] Refreshing screenshot...
[[18:22:52]] [INFO] 2M0KHOVecv=pass
[[18:22:49]] [INFO] 2M0KHOVecv=running
[[18:22:49]] [INFO] Executing action 339/643: Check if element with accessibility_id="txtMy Flybuys card" exists
[[18:22:49]] [SUCCESS] Screenshot refreshed successfully
[[18:22:49]] [SUCCESS] Screenshot refreshed successfully
[[18:22:49]] [SUCCESS] Screenshot refreshed
[[18:22:49]] [INFO] Refreshing screenshot...
[[18:22:49]] [INFO] LBgsj3oLcu=pass
[[18:22:45]] [INFO] LBgsj3oLcu=running
[[18:22:45]] [INFO] Executing action 338/643: Tap on image: env[device-back-img]
[[18:22:45]] [SUCCESS] Screenshot refreshed successfully
[[18:22:45]] [SUCCESS] Screenshot refreshed successfully
[[18:22:44]] [SUCCESS] Screenshot refreshed
[[18:22:44]] [INFO] Refreshing screenshot...
[[18:22:44]] [INFO] biRyWs3nSs=pass
[[18:22:39]] [INFO] biRyWs3nSs=running
[[18:22:39]] [INFO] Executing action 337/643: Tap on element with accessibility_id: btnSaveFlybuysCard
[[18:22:39]] [SUCCESS] Screenshot refreshed successfully
[[18:22:39]] [SUCCESS] Screenshot refreshed successfully
[[18:22:38]] [SUCCESS] Screenshot refreshed
[[18:22:38]] [INFO] Refreshing screenshot...
[[18:22:38]] [INFO] 8cFGh3GD68=pass
[[18:22:33]] [INFO] 8cFGh3GD68=running
[[18:22:33]] [INFO] Executing action 336/643: Tap on element with accessibility_id: Done
[[18:22:32]] [SUCCESS] Screenshot refreshed successfully
[[18:22:32]] [SUCCESS] Screenshot refreshed successfully
[[18:22:32]] [SUCCESS] Screenshot refreshed
[[18:22:32]] [INFO] Refreshing screenshot...
[[18:22:32]] [INFO] sLe0Wurhgm=pass
[[18:22:29]] [INFO] sLe0Wurhgm=running
[[18:22:29]] [INFO] Executing action 335/643: Input text: "2791234567890"
[[18:22:29]] [SUCCESS] Screenshot refreshed successfully
[[18:22:29]] [SUCCESS] Screenshot refreshed successfully
[[18:22:29]] [SUCCESS] Screenshot refreshed
[[18:22:29]] [INFO] Refreshing screenshot...
[[18:22:29]] [INFO] Ey86YRVRzU=pass
[[18:22:23]] [INFO] Ey86YRVRzU=running
[[18:22:23]] [INFO] Executing action 334/643: Tap on element with accessibility_id: Flybuys barcode number
[[18:22:23]] [SUCCESS] Screenshot refreshed successfully
[[18:22:23]] [SUCCESS] Screenshot refreshed successfully
[[18:22:23]] [SUCCESS] Screenshot refreshed
[[18:22:23]] [INFO] Refreshing screenshot...
[[18:22:23]] [INFO] Gxhf3XGc6e=pass
[[18:22:17]] [INFO] Gxhf3XGc6e=running
[[18:22:17]] [INFO] Executing action 333/643: Tap on element with accessibility_id: btnLinkFlyBuys
[[18:22:17]] [SUCCESS] Screenshot refreshed successfully
[[18:22:17]] [SUCCESS] Screenshot refreshed successfully
[[18:22:17]] [SUCCESS] Screenshot refreshed
[[18:22:17]] [INFO] Refreshing screenshot...
[[18:22:17]] [INFO] BracBsfa3Y=pass
[[18:22:13]] [INFO] BracBsfa3Y=running
[[18:22:13]] [INFO] Executing action 332/643: Tap on Text: "Flybuys"
[[18:22:12]] [SUCCESS] Screenshot refreshed successfully
[[18:22:12]] [SUCCESS] Screenshot refreshed successfully
[[18:22:12]] [SUCCESS] Screenshot refreshed
[[18:22:12]] [INFO] Refreshing screenshot...
[[18:22:12]] [INFO] Ds5GfNVb3x=pass
[[18:22:07]] [INFO] Ds5GfNVb3x=running
[[18:22:07]] [INFO] Executing action 331/643: Tap on element with accessibility_id: btnRemove
[[18:22:06]] [SUCCESS] Screenshot refreshed successfully
[[18:22:06]] [SUCCESS] Screenshot refreshed successfully
[[18:22:06]] [SUCCESS] Screenshot refreshed
[[18:22:06]] [INFO] Refreshing screenshot...
[[18:22:06]] [INFO] 3ZFgwFaiXp=pass
[[18:22:01]] [INFO] 3ZFgwFaiXp=running
[[18:22:01]] [INFO] Executing action 330/643: Tap on element with accessibility_id: Remove card
[[18:22:00]] [SUCCESS] Screenshot refreshed successfully
[[18:22:00]] [SUCCESS] Screenshot refreshed successfully
[[18:22:00]] [SUCCESS] Screenshot refreshed
[[18:22:00]] [INFO] Refreshing screenshot...
[[18:22:00]] [INFO] 40hnWPsQ9P=pass
[[18:21:55]] [INFO] 40hnWPsQ9P=running
[[18:21:55]] [INFO] Executing action 329/643: Tap on element with accessibility_id: btneditFlybuysCard
[[18:21:54]] [SUCCESS] Screenshot refreshed successfully
[[18:21:54]] [SUCCESS] Screenshot refreshed successfully
[[18:21:54]] [SUCCESS] Screenshot refreshed
[[18:21:54]] [INFO] Refreshing screenshot...
[[18:21:54]] [INFO] 40hnWPsQ9P=pass
[[18:21:50]] [INFO] 40hnWPsQ9P=running
[[18:21:50]] [INFO] Executing action 328/643: Wait till accessibility_id=btneditFlybuysCard
[[18:21:49]] [SUCCESS] Screenshot refreshed successfully
[[18:21:49]] [SUCCESS] Screenshot refreshed successfully
[[18:21:49]] [SUCCESS] Screenshot refreshed
[[18:21:49]] [INFO] Refreshing screenshot...
[[18:21:49]] [INFO] BracBsfa3Y=pass
[[18:21:45]] [INFO] BracBsfa3Y=running
[[18:21:45]] [INFO] Executing action 327/643: Tap on Text: "Flybuys"
[[18:21:45]] [SUCCESS] Screenshot refreshed successfully
[[18:21:45]] [SUCCESS] Screenshot refreshed successfully
[[18:21:45]] [SUCCESS] Screenshot refreshed
[[18:21:45]] [INFO] Refreshing screenshot...
[[18:21:45]] [INFO] MkTFxfzubv=pass
[[18:21:41]] [INFO] MkTFxfzubv=running
[[18:21:41]] [INFO] Executing action 326/643: Tap on image: env[device-back-img]
[[18:21:41]] [SUCCESS] Screenshot refreshed successfully
[[18:21:41]] [SUCCESS] Screenshot refreshed successfully
[[18:21:40]] [SUCCESS] Screenshot refreshed
[[18:21:40]] [INFO] Refreshing screenshot...
[[18:21:40]] [INFO] EO3cMmdUyM=pass
[[18:21:37]] [INFO] EO3cMmdUyM=running
[[18:21:37]] [INFO] Executing action 325/643: Tap on image: env[device-back-img]
[[18:21:37]] [SUCCESS] Screenshot refreshed successfully
[[18:21:37]] [SUCCESS] Screenshot refreshed successfully
[[18:21:36]] [SUCCESS] Screenshot refreshed
[[18:21:36]] [INFO] Refreshing screenshot...
[[18:21:36]] [INFO] napKDohf3Z=pass
[[18:21:32]] [INFO] napKDohf3Z=running
[[18:21:32]] [INFO] Executing action 324/643: Tap on Text: "payment"
[[18:21:32]] [SUCCESS] Screenshot refreshed successfully
[[18:21:32]] [SUCCESS] Screenshot refreshed successfully
[[18:21:32]] [SUCCESS] Screenshot refreshed
[[18:21:32]] [INFO] Refreshing screenshot...
[[18:21:32]] [INFO] ekqt95ZRol=pass
[[18:21:28]] [INFO] ekqt95ZRol=running
[[18:21:28]] [INFO] Executing action 323/643: Tap on image: env[device-back-img]
[[18:21:28]] [SUCCESS] Screenshot refreshed successfully
[[18:21:28]] [SUCCESS] Screenshot refreshed successfully
[[18:21:28]] [SUCCESS] Screenshot refreshed
[[18:21:28]] [INFO] Refreshing screenshot...
[[18:21:28]] [INFO] 20qUCJgpE9=pass
[[18:21:24]] [INFO] 20qUCJgpE9=running
[[18:21:24]] [INFO] Executing action 322/643: Tap on Text: "address"
[[18:21:23]] [SUCCESS] Screenshot refreshed successfully
[[18:21:23]] [SUCCESS] Screenshot refreshed successfully
[[18:21:23]] [SUCCESS] Screenshot refreshed
[[18:21:23]] [INFO] Refreshing screenshot...
[[18:21:23]] [INFO] 6HR2weiXoT=pass
[[18:21:20]] [INFO] 6HR2weiXoT=running
[[18:21:20]] [INFO] Executing action 321/643: Tap on image: env[device-back-img]
[[18:21:19]] [SUCCESS] Screenshot refreshed successfully
[[18:21:19]] [SUCCESS] Screenshot refreshed successfully
[[18:21:19]] [SUCCESS] Screenshot refreshed
[[18:21:19]] [INFO] Refreshing screenshot...
[[18:21:19]] [INFO] 3hOTINBVMf=pass
[[18:21:15]] [INFO] 3hOTINBVMf=running
[[18:21:15]] [INFO] Executing action 320/643: Tap on Text: "details"
[[18:21:15]] [SUCCESS] Screenshot refreshed successfully
[[18:21:15]] [SUCCESS] Screenshot refreshed successfully
[[18:21:15]] [SUCCESS] Screenshot refreshed
[[18:21:15]] [INFO] Refreshing screenshot...
[[18:21:15]] [INFO] yJi0WxnERj=pass
[[18:21:11]] [INFO] yJi0WxnERj=running
[[18:21:11]] [INFO] Executing action 319/643: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[18:21:11]] [SUCCESS] Screenshot refreshed successfully
[[18:21:11]] [SUCCESS] Screenshot refreshed successfully
[[18:21:10]] [SUCCESS] Screenshot refreshed
[[18:21:10]] [INFO] Refreshing screenshot...
[[18:21:10]] [INFO] PbfHAtFQPP=pass
[[18:21:07]] [INFO] PbfHAtFQPP=running
[[18:21:07]] [INFO] Executing action 318/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:21:06]] [SUCCESS] Screenshot refreshed successfully
[[18:21:06]] [SUCCESS] Screenshot refreshed successfully
[[18:21:06]] [SUCCESS] Screenshot refreshed
[[18:21:06]] [INFO] Refreshing screenshot...
[[18:21:06]] [INFO] 6qZnk86hGg=pass
[[18:21:02]] [INFO] 6qZnk86hGg=running
[[18:21:02]] [INFO] Executing action 317/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:21:02]] [SUCCESS] Screenshot refreshed successfully
[[18:21:02]] [SUCCESS] Screenshot refreshed successfully
[[18:21:02]] [SUCCESS] Screenshot refreshed
[[18:21:02]] [INFO] Refreshing screenshot...
[[18:21:02]] [INFO] FAvQgIuHc1=pass
[[18:20:57]] [INFO] FAvQgIuHc1=running
[[18:20:57]] [INFO] Executing action 316/643: Tap on Text: "Return"
[[18:20:57]] [SUCCESS] Screenshot refreshed successfully
[[18:20:57]] [SUCCESS] Screenshot refreshed successfully
[[18:20:57]] [SUCCESS] Screenshot refreshed
[[18:20:57]] [INFO] Refreshing screenshot...
[[18:20:57]] [INFO] vmc01sHkbr=pass
[[18:20:51]] [INFO] vmc01sHkbr=running
[[18:20:51]] [INFO] Executing action 315/643: Wait for 5 ms
[[18:20:50]] [SUCCESS] Screenshot refreshed successfully
[[18:20:50]] [SUCCESS] Screenshot refreshed successfully
[[18:20:50]] [SUCCESS] Screenshot refreshed
[[18:20:50]] [INFO] Refreshing screenshot...
[[18:20:50]] [INFO] zeu0wd1vcF=pass
[[18:20:37]] [INFO] zeu0wd1vcF=running
[[18:20:37]] [INFO] Executing action 314/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:20:37]] [SUCCESS] Screenshot refreshed successfully
[[18:20:37]] [SUCCESS] Screenshot refreshed successfully
[[18:20:37]] [SUCCESS] Screenshot refreshed
[[18:20:37]] [INFO] Refreshing screenshot...
[[18:20:37]] [INFO] OwWeZes4aT=pass
[[18:20:33]] [INFO] OwWeZes4aT=running
[[18:20:33]] [INFO] Executing action 313/643: Tap on image: env[device-back-img]
[[18:20:33]] [SUCCESS] Screenshot refreshed successfully
[[18:20:33]] [SUCCESS] Screenshot refreshed successfully
[[18:20:33]] [SUCCESS] Screenshot refreshed
[[18:20:33]] [INFO] Refreshing screenshot...
[[18:20:33]] [INFO] aAaTtUE92h=pass
[[18:20:27]] [INFO] aAaTtUE92h=running
[[18:20:27]] [INFO] Executing action 312/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[18:20:27]] [SUCCESS] Screenshot refreshed successfully
[[18:20:27]] [SUCCESS] Screenshot refreshed successfully
[[18:20:26]] [SUCCESS] Screenshot refreshed
[[18:20:26]] [INFO] Refreshing screenshot...
[[18:20:26]] [INFO] 9iOZGMqAZK=pass
[[18:20:22]] [INFO] 9iOZGMqAZK=running
[[18:20:22]] [INFO] Executing action 311/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[18:20:22]] [SUCCESS] Screenshot refreshed successfully
[[18:20:22]] [SUCCESS] Screenshot refreshed successfully
[[18:20:21]] [SUCCESS] Screenshot refreshed
[[18:20:21]] [INFO] Refreshing screenshot...
[[18:20:21]] [INFO] mRTYzOFRRw=pass
[[18:20:18]] [INFO] mRTYzOFRRw=running
[[18:20:18]] [INFO] Executing action 310/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[18:20:18]] [SUCCESS] Screenshot refreshed successfully
[[18:20:18]] [SUCCESS] Screenshot refreshed successfully
[[18:20:18]] [SUCCESS] Screenshot refreshed
[[18:20:18]] [INFO] Refreshing screenshot...
[[18:20:18]] [INFO] 7g6MFJSGIO=pass
[[18:20:14]] [INFO] 7g6MFJSGIO=running
[[18:20:14]] [INFO] Executing action 309/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[18:20:14]] [SUCCESS] Screenshot refreshed successfully
[[18:20:14]] [SUCCESS] Screenshot refreshed successfully
[[18:20:14]] [SUCCESS] Screenshot refreshed
[[18:20:14]] [INFO] Refreshing screenshot...
[[18:20:14]] [INFO] zNwyPagPE1=pass
[[18:20:07]] [INFO] zNwyPagPE1=running
[[18:20:07]] [INFO] Executing action 308/643: Wait for 5 ms
[[18:20:07]] [SUCCESS] Screenshot refreshed successfully
[[18:20:07]] [SUCCESS] Screenshot refreshed successfully
[[18:20:07]] [SUCCESS] Screenshot refreshed
[[18:20:07]] [INFO] Refreshing screenshot...
[[18:20:07]] [INFO] qXsL3wzg6J=pass
[[18:20:03]] [INFO] qXsL3wzg6J=running
[[18:20:03]] [INFO] Executing action 307/643: Tap on image: env[device-back-img]
[[18:20:03]] [SUCCESS] Screenshot refreshed successfully
[[18:20:03]] [SUCCESS] Screenshot refreshed successfully
[[18:20:03]] [SUCCESS] Screenshot refreshed
[[18:20:03]] [INFO] Refreshing screenshot...
[[18:20:03]] [INFO] YuuQe2KupX=pass
[[18:19:58]] [INFO] YuuQe2KupX=running
[[18:19:58]] [INFO] Executing action 306/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[18:19:58]] [SUCCESS] Screenshot refreshed successfully
[[18:19:58]] [SUCCESS] Screenshot refreshed successfully
[[18:19:58]] [SUCCESS] Screenshot refreshed
[[18:19:58]] [INFO] Refreshing screenshot...
[[18:19:58]] [INFO] g0PE7Mofye=pass
[[18:19:52]] [INFO] g0PE7Mofye=running
[[18:19:52]] [INFO] Executing action 305/643: Tap on element with accessibility_id: Print order details
[[18:19:51]] [SUCCESS] Screenshot refreshed successfully
[[18:19:51]] [SUCCESS] Screenshot refreshed successfully
[[18:19:51]] [SUCCESS] Screenshot refreshed
[[18:19:51]] [INFO] Refreshing screenshot...
[[18:19:51]] [INFO] GgQaBLWYkb=pass
[[18:19:47]] [INFO] GgQaBLWYkb=running
[[18:19:47]] [INFO] Executing action 304/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[18:19:47]] [SUCCESS] Screenshot refreshed successfully
[[18:19:47]] [SUCCESS] Screenshot refreshed successfully
[[18:19:47]] [SUCCESS] Screenshot refreshed
[[18:19:47]] [INFO] Refreshing screenshot...
[[18:19:47]] [INFO] f3OrHHzTFN=pass
[[18:19:31]] [INFO] f3OrHHzTFN=running
[[18:19:31]] [INFO] Executing action 303/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Email tax invoice"]" is visible
[[18:19:31]] [SUCCESS] Screenshot refreshed successfully
[[18:19:31]] [SUCCESS] Screenshot refreshed successfully
[[18:19:31]] [SUCCESS] Screenshot refreshed
[[18:19:31]] [INFO] Refreshing screenshot...
[[18:19:31]] [INFO] 7g6MFJSGIO=pass
[[18:19:27]] [INFO] 7g6MFJSGIO=running
[[18:19:27]] [INFO] Executing action 302/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[18:19:26]] [SUCCESS] Screenshot refreshed successfully
[[18:19:26]] [SUCCESS] Screenshot refreshed successfully
[[18:19:26]] [SUCCESS] Screenshot refreshed
[[18:19:26]] [INFO] Refreshing screenshot...
[[18:19:26]] [INFO] Z6g3sGuHTp=pass
[[18:19:20]] [INFO] Z6g3sGuHTp=running
[[18:19:20]] [INFO] Executing action 301/643: Wait for 5 ms
[[18:19:20]] [SUCCESS] Screenshot refreshed successfully
[[18:19:20]] [SUCCESS] Screenshot refreshed successfully
[[18:19:19]] [SUCCESS] Screenshot refreshed
[[18:19:19]] [INFO] Refreshing screenshot...
[[18:19:19]] [INFO] pFlYwTS53v=pass
[[18:19:15]] [INFO] pFlYwTS53v=running
[[18:19:15]] [INFO] Executing action 300/643: Tap on Text: "receipts"
[[18:19:15]] [SUCCESS] Screenshot refreshed successfully
[[18:19:15]] [SUCCESS] Screenshot refreshed successfully
[[18:19:15]] [SUCCESS] Screenshot refreshed
[[18:19:15]] [INFO] Refreshing screenshot...
[[18:19:15]] [INFO] V59u3l1wkM=pass
[[18:19:12]] [INFO] V59u3l1wkM=running
[[18:19:12]] [INFO] Executing action 299/643: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[18:19:11]] [SUCCESS] Screenshot refreshed successfully
[[18:19:11]] [SUCCESS] Screenshot refreshed successfully
[[18:19:11]] [SUCCESS] Screenshot refreshed
[[18:19:11]] [INFO] Refreshing screenshot...
[[18:19:11]] [INFO] sl3Wk1gK8X=pass
[[18:18:57]] [SUCCESS] Screenshot refreshed successfully
[[18:18:57]] [SUCCESS] Screenshot refreshed successfully
[[18:18:57]] [INFO] sl3Wk1gK8X=running
[[18:18:57]] [INFO] Executing action 298/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:18:57]] [SUCCESS] Screenshot refreshed
[[18:18:57]] [INFO] Refreshing screenshot...
[[18:18:57]] [SUCCESS] Screenshot refreshed successfully
[[18:18:57]] [SUCCESS] Screenshot refreshed successfully
[[18:18:57]] [SUCCESS] Screenshot refreshed
[[18:18:57]] [INFO] Refreshing screenshot...
[[18:18:52]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:18:52]] [SUCCESS] Screenshot refreshed successfully
[[18:18:52]] [SUCCESS] Screenshot refreshed successfully
[[18:18:52]] [SUCCESS] Screenshot refreshed
[[18:18:52]] [INFO] Refreshing screenshot...
[[18:18:48]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:18:48]] [SUCCESS] Screenshot refreshed successfully
[[18:18:48]] [SUCCESS] Screenshot refreshed successfully
[[18:18:47]] [SUCCESS] Screenshot refreshed
[[18:18:47]] [INFO] Refreshing screenshot...
[[18:18:43]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:18:42]] [SUCCESS] Screenshot refreshed successfully
[[18:18:42]] [SUCCESS] Screenshot refreshed successfully
[[18:18:42]] [SUCCESS] Screenshot refreshed
[[18:18:42]] [INFO] Refreshing screenshot...
[[18:18:38]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:18:38]] [SUCCESS] Screenshot refreshed successfully
[[18:18:38]] [SUCCESS] Screenshot refreshed successfully
[[18:18:38]] [SUCCESS] Screenshot refreshed
[[18:18:38]] [INFO] Refreshing screenshot...
[[18:18:33]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:18:32]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:18:32]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:18:32]] [INFO] vjK6GqOF3r=running
[[18:18:32]] [INFO] Executing action 297/643: Execute Test Case: Kmart-Signin (8 steps)
[[18:18:32]] [SUCCESS] Screenshot refreshed successfully
[[18:18:32]] [SUCCESS] Screenshot refreshed successfully
[[18:18:32]] [SUCCESS] Screenshot refreshed
[[18:18:32]] [INFO] Refreshing screenshot...
[[18:18:32]] [INFO] ly2oT3zqmf=pass
[[18:18:29]] [INFO] ly2oT3zqmf=running
[[18:18:29]] [INFO] Executing action 296/643: iOS Function: alert_accept
[[18:18:29]] [SUCCESS] Screenshot refreshed successfully
[[18:18:29]] [SUCCESS] Screenshot refreshed successfully
[[18:18:29]] [SUCCESS] Screenshot refreshed
[[18:18:29]] [INFO] Refreshing screenshot...
[[18:18:29]] [INFO] xAPeBnVHrT=pass
[[18:18:22]] [INFO] xAPeBnVHrT=running
[[18:18:22]] [INFO] Executing action 295/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:18:22]] [SUCCESS] Screenshot refreshed successfully
[[18:18:22]] [SUCCESS] Screenshot refreshed successfully
[[18:18:21]] [SUCCESS] Screenshot refreshed
[[18:18:21]] [INFO] Refreshing screenshot...
[[18:18:21]] [INFO] u6bRYZZFAv=pass
[[18:18:15]] [INFO] u6bRYZZFAv=running
[[18:18:15]] [INFO] Executing action 294/643: Wait for 5 ms
[[18:18:15]] [SUCCESS] Screenshot refreshed successfully
[[18:18:15]] [SUCCESS] Screenshot refreshed successfully
[[18:18:14]] [SUCCESS] Screenshot refreshed
[[18:18:14]] [INFO] Refreshing screenshot...
[[18:18:14]] [INFO] pjFNt3w5Fr=pass
[[18:18:01]] [SUCCESS] Screenshot refreshed successfully
[[18:18:01]] [SUCCESS] Screenshot refreshed successfully
[[18:18:01]] [INFO] pjFNt3w5Fr=running
[[18:18:01]] [INFO] Executing action 293/643: Restart app: env[appid]
[[18:18:01]] [SUCCESS] Screenshot refreshed
[[18:18:01]] [INFO] Refreshing screenshot...
[[18:18:01]] [SUCCESS] Screenshot refreshed successfully
[[18:18:01]] [SUCCESS] Screenshot refreshed successfully
[[18:18:00]] [SUCCESS] Screenshot refreshed
[[18:18:00]] [INFO] Refreshing screenshot...
[[18:17:58]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:17:58]] [SUCCESS] Screenshot refreshed successfully
[[18:17:58]] [SUCCESS] Screenshot refreshed successfully
[[18:17:58]] [SUCCESS] Screenshot refreshed
[[18:17:58]] [INFO] Refreshing screenshot...
[[18:17:45]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:17:45]] [SUCCESS] Screenshot refreshed successfully
[[18:17:45]] [SUCCESS] Screenshot refreshed successfully
[[18:17:45]] [SUCCESS] Screenshot refreshed
[[18:17:45]] [INFO] Refreshing screenshot...
[[18:17:41]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:17:41]] [SUCCESS] Screenshot refreshed successfully
[[18:17:41]] [SUCCESS] Screenshot refreshed successfully
[[18:17:41]] [SUCCESS] Screenshot refreshed
[[18:17:41]] [INFO] Refreshing screenshot...
[[18:17:37]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:17:37]] [SUCCESS] Screenshot refreshed successfully
[[18:17:37]] [SUCCESS] Screenshot refreshed successfully
[[18:17:36]] [SUCCESS] Screenshot refreshed
[[18:17:36]] [INFO] Refreshing screenshot...
[[18:17:30]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:17:30]] [SUCCESS] Screenshot refreshed successfully
[[18:17:30]] [SUCCESS] Screenshot refreshed successfully
[[18:17:29]] [SUCCESS] Screenshot refreshed
[[18:17:29]] [INFO] Refreshing screenshot...
[[18:17:24]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:17:24]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:17:24]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:17:24]] [INFO] PGvsG6rpU4=running
[[18:17:24]] [INFO] Executing action 292/643: cleanupSteps action
[[18:17:24]] [SUCCESS] Screenshot refreshed successfully
[[18:17:24]] [SUCCESS] Screenshot refreshed successfully
[[18:17:23]] [SUCCESS] Screenshot refreshed
[[18:17:23]] [INFO] Refreshing screenshot...
[[18:17:23]] [INFO] LzGkAcsQyE=pass
[[18:17:21]] [INFO] LzGkAcsQyE=running
[[18:17:21]] [INFO] Executing action 291/643: Terminate app: env[appid]
[[18:17:21]] [SUCCESS] Screenshot refreshed successfully
[[18:17:21]] [SUCCESS] Screenshot refreshed successfully
[[18:17:20]] [SUCCESS] Screenshot refreshed
[[18:17:20]] [INFO] Refreshing screenshot...
[[18:17:20]] [INFO] Bdhe5AoUlM=pass
[[18:17:16]] [INFO] Bdhe5AoUlM=running
[[18:17:16]] [INFO] Executing action 290/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:17:16]] [SUCCESS] Screenshot refreshed successfully
[[18:17:16]] [SUCCESS] Screenshot refreshed successfully
[[18:17:16]] [SUCCESS] Screenshot refreshed
[[18:17:16]] [INFO] Refreshing screenshot...
[[18:17:16]] [INFO] FciJcOsMsB=pass
[[18:17:09]] [INFO] FciJcOsMsB=running
[[18:17:09]] [INFO] Executing action 289/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:17:09]] [SUCCESS] Screenshot refreshed successfully
[[18:17:09]] [SUCCESS] Screenshot refreshed successfully
[[18:17:09]] [SUCCESS] Screenshot refreshed
[[18:17:09]] [INFO] Refreshing screenshot...
[[18:17:09]] [INFO] FARWZvOj0x=pass
[[18:17:05]] [INFO] FARWZvOj0x=running
[[18:17:05]] [INFO] Executing action 288/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:17:05]] [SUCCESS] Screenshot refreshed successfully
[[18:17:05]] [SUCCESS] Screenshot refreshed successfully
[[18:17:04]] [SUCCESS] Screenshot refreshed
[[18:17:04]] [INFO] Refreshing screenshot...
[[18:17:04]] [INFO] bZCkx4U9Gk=pass
[[18:17:00]] [INFO] bZCkx4U9Gk=running
[[18:17:00]] [INFO] Executing action 287/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:17:00]] [SUCCESS] Screenshot refreshed successfully
[[18:17:00]] [SUCCESS] Screenshot refreshed successfully
[[18:17:00]] [SUCCESS] Screenshot refreshed
[[18:17:00]] [INFO] Refreshing screenshot...
[[18:17:00]] [INFO] vwFwkK6ydQ=pass
[[18:16:55]] [INFO] vwFwkK6ydQ=running
[[18:16:55]] [INFO] Executing action 286/643: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[18:16:55]] [SUCCESS] Screenshot refreshed successfully
[[18:16:55]] [SUCCESS] Screenshot refreshed successfully
[[18:16:55]] [SUCCESS] Screenshot refreshed
[[18:16:55]] [INFO] Refreshing screenshot...
[[18:16:55]] [INFO] xLGm9FefWE=pass
[[18:16:51]] [INFO] xLGm9FefWE=running
[[18:16:51]] [INFO] Executing action 285/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[18:16:51]] [SUCCESS] Screenshot refreshed successfully
[[18:16:51]] [SUCCESS] Screenshot refreshed successfully
[[18:16:50]] [SUCCESS] Screenshot refreshed
[[18:16:50]] [INFO] Refreshing screenshot...
[[18:16:50]] [INFO] UtVRXwa86e=pass
[[18:16:44]] [INFO] UtVRXwa86e=running
[[18:16:44]] [INFO] Executing action 284/643: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Sign in with Google"]" is visible
[[18:16:44]] [SUCCESS] Screenshot refreshed successfully
[[18:16:44]] [SUCCESS] Screenshot refreshed successfully
[[18:16:43]] [SUCCESS] Screenshot refreshed
[[18:16:43]] [INFO] Refreshing screenshot...
[[18:16:43]] [INFO] SDtskxyVpg=pass
[[18:16:40]] [INFO] SDtskxyVpg=running
[[18:16:40]] [INFO] Executing action 283/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:16:40]] [SUCCESS] Screenshot refreshed successfully
[[18:16:40]] [SUCCESS] Screenshot refreshed successfully
[[18:16:39]] [SUCCESS] Screenshot refreshed
[[18:16:39]] [INFO] Refreshing screenshot...
[[18:16:39]] [INFO] 6HhScBaqQp=pass
[[18:16:37]] [INFO] 6HhScBaqQp=running
[[18:16:37]] [INFO] Executing action 282/643: iOS Function: alert_accept
[[18:16:37]] [SUCCESS] Screenshot refreshed successfully
[[18:16:37]] [SUCCESS] Screenshot refreshed successfully
[[18:16:36]] [SUCCESS] Screenshot refreshed
[[18:16:36]] [INFO] Refreshing screenshot...
[[18:16:36]] [INFO] quzlwPw42x=pass
[[18:16:31]] [INFO] quzlwPw42x=running
[[18:16:31]] [INFO] Executing action 281/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:16:30]] [SUCCESS] Screenshot refreshed successfully
[[18:16:30]] [SUCCESS] Screenshot refreshed successfully
[[18:16:30]] [SUCCESS] Screenshot refreshed
[[18:16:30]] [INFO] Refreshing screenshot...
[[18:16:30]] [INFO] jQYHQIvQ8l=pass
[[18:16:27]] [INFO] jQYHQIvQ8l=running
[[18:16:27]] [INFO] Executing action 280/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:16:26]] [SUCCESS] Screenshot refreshed successfully
[[18:16:26]] [SUCCESS] Screenshot refreshed successfully
[[18:16:26]] [SUCCESS] Screenshot refreshed
[[18:16:26]] [INFO] Refreshing screenshot...
[[18:16:26]] [INFO] ts3qyFxyMf=pass
[[18:16:22]] [INFO] ts3qyFxyMf=running
[[18:16:22]] [INFO] Executing action 279/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:16:22]] [SUCCESS] Screenshot refreshed successfully
[[18:16:22]] [SUCCESS] Screenshot refreshed successfully
[[18:16:22]] [SUCCESS] Screenshot refreshed
[[18:16:22]] [INFO] Refreshing screenshot...
[[18:16:22]] [INFO] FciJcOsMsB=pass
[[18:16:15]] [INFO] FciJcOsMsB=running
[[18:16:15]] [INFO] Executing action 278/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:16:15]] [SUCCESS] Screenshot refreshed successfully
[[18:16:15]] [SUCCESS] Screenshot refreshed successfully
[[18:16:15]] [SUCCESS] Screenshot refreshed
[[18:16:15]] [INFO] Refreshing screenshot...
[[18:16:15]] [INFO] CWkqGp5ndO=pass
[[18:16:11]] [INFO] CWkqGp5ndO=running
[[18:16:11]] [INFO] Executing action 277/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:16:11]] [SUCCESS] Screenshot refreshed successfully
[[18:16:11]] [SUCCESS] Screenshot refreshed successfully
[[18:16:10]] [SUCCESS] Screenshot refreshed
[[18:16:10]] [INFO] Refreshing screenshot...
[[18:16:10]] [INFO] KfMHchi8cx=pass
[[18:16:04]] [INFO] KfMHchi8cx=running
[[18:16:04]] [INFO] Executing action 276/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:16:04]] [SUCCESS] Screenshot refreshed successfully
[[18:16:04]] [SUCCESS] Screenshot refreshed successfully
[[18:16:04]] [SUCCESS] Screenshot refreshed
[[18:16:04]] [INFO] Refreshing screenshot...
[[18:16:04]] [INFO] zsVeGHiIgX=pass
[[18:16:01]] [INFO] zsVeGHiIgX=running
[[18:16:01]] [INFO] Executing action 275/643: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[18:16:00]] [SUCCESS] Screenshot refreshed successfully
[[18:16:00]] [SUCCESS] Screenshot refreshed successfully
[[18:16:00]] [SUCCESS] Screenshot refreshed
[[18:16:00]] [INFO] Refreshing screenshot...
[[18:16:00]] [INFO] 5nsUXQ5L7u=pass
[[18:15:57]] [INFO] 5nsUXQ5L7u=running
[[18:15:57]] [INFO] Executing action 274/643: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[18:15:57]] [SUCCESS] Screenshot refreshed successfully
[[18:15:57]] [SUCCESS] Screenshot refreshed successfully
[[18:15:57]] [SUCCESS] Screenshot refreshed
[[18:15:57]] [INFO] Refreshing screenshot...
[[18:15:57]] [INFO] iSckENpXrN=pass
[[18:15:54]] [INFO] iSckENpXrN=running
[[18:15:54]] [INFO] Executing action 273/643: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[18:15:54]] [SUCCESS] Screenshot refreshed successfully
[[18:15:54]] [SUCCESS] Screenshot refreshed successfully
[[18:15:53]] [SUCCESS] Screenshot refreshed
[[18:15:53]] [INFO] Refreshing screenshot...
[[18:15:53]] [INFO] J7BPGVnRJI=pass
[[18:15:51]] [INFO] J7BPGVnRJI=running
[[18:15:51]] [INFO] Executing action 272/643: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[18:15:50]] [SUCCESS] Screenshot refreshed successfully
[[18:15:50]] [SUCCESS] Screenshot refreshed successfully
[[18:15:50]] [SUCCESS] Screenshot refreshed
[[18:15:50]] [INFO] Refreshing screenshot...
[[18:15:50]] [INFO] 0pwZCYAtOv=pass
[[18:15:47]] [INFO] 0pwZCYAtOv=running
[[18:15:47]] [INFO] Executing action 271/643: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[18:15:47]] [SUCCESS] Screenshot refreshed successfully
[[18:15:47]] [SUCCESS] Screenshot refreshed successfully
[[18:15:47]] [SUCCESS] Screenshot refreshed
[[18:15:47]] [INFO] Refreshing screenshot...
[[18:15:47]] [INFO] soKM0KayFJ=pass
[[18:15:44]] [INFO] soKM0KayFJ=running
[[18:15:44]] [INFO] Executing action 270/643: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[18:15:43]] [SUCCESS] Screenshot refreshed successfully
[[18:15:43]] [SUCCESS] Screenshot refreshed successfully
[[18:15:43]] [SUCCESS] Screenshot refreshed
[[18:15:43]] [INFO] Refreshing screenshot...
[[18:15:43]] [INFO] hnH3ayslCh=pass
[[18:15:40]] [INFO] hnH3ayslCh=running
[[18:15:40]] [INFO] Executing action 269/643: Tap on Text: "Passcode"
[[18:15:40]] [SUCCESS] Screenshot refreshed successfully
[[18:15:40]] [SUCCESS] Screenshot refreshed successfully
[[18:15:40]] [SUCCESS] Screenshot refreshed
[[18:15:40]] [INFO] Refreshing screenshot...
[[18:15:40]] [INFO] CzVeOTdAX9=pass
[[18:15:28]] [INFO] CzVeOTdAX9=running
[[18:15:28]] [INFO] Executing action 268/643: Wait for 10 ms
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [SUCCESS] Screenshot refreshed successfully
[[18:15:28]] [SUCCESS] Screenshot refreshed
[[18:15:28]] [INFO] Refreshing screenshot...
[[18:15:28]] [INFO] NL2gtj6qIu=pass
[[18:15:24]] [INFO] NL2gtj6qIu=running
[[18:15:24]] [INFO] Executing action 267/643: Tap on Text: "Apple"
[[18:15:23]] [SUCCESS] Screenshot refreshed successfully
[[18:15:23]] [SUCCESS] Screenshot refreshed successfully
[[18:15:23]] [SUCCESS] Screenshot refreshed
[[18:15:23]] [INFO] Refreshing screenshot...
[[18:15:23]] [INFO] VsSlyhXuVD=pass
[[18:15:19]] [INFO] VsSlyhXuVD=running
[[18:15:19]] [INFO] Executing action 266/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:15:18]] [SUCCESS] Screenshot refreshed successfully
[[18:15:18]] [SUCCESS] Screenshot refreshed successfully
[[18:15:18]] [SUCCESS] Screenshot refreshed
[[18:15:18]] [INFO] Refreshing screenshot...
[[18:15:18]] [INFO] CJ88OgjKXp=pass
[[18:15:15]] [INFO] CJ88OgjKXp=running
[[18:15:15]] [INFO] Executing action 265/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:15:14]] [SUCCESS] Screenshot refreshed successfully
[[18:15:14]] [SUCCESS] Screenshot refreshed successfully
[[18:15:14]] [SUCCESS] Screenshot refreshed
[[18:15:14]] [INFO] Refreshing screenshot...
[[18:15:14]] [INFO] AYiwFSLTBD=pass
[[18:15:12]] [INFO] AYiwFSLTBD=running
[[18:15:12]] [INFO] Executing action 264/643: iOS Function: alert_accept
[[18:15:12]] [SUCCESS] Screenshot refreshed successfully
[[18:15:12]] [SUCCESS] Screenshot refreshed successfully
[[18:15:11]] [SUCCESS] Screenshot refreshed
[[18:15:11]] [INFO] Refreshing screenshot...
[[18:15:11]] [INFO] HJzOYZNnGr=pass
[[18:15:06]] [INFO] HJzOYZNnGr=running
[[18:15:06]] [INFO] Executing action 263/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:15:05]] [SUCCESS] Screenshot refreshed successfully
[[18:15:05]] [SUCCESS] Screenshot refreshed successfully
[[18:15:05]] [SUCCESS] Screenshot refreshed
[[18:15:05]] [INFO] Refreshing screenshot...
[[18:15:05]] [INFO] taf19mtrUT=pass
[[18:15:02]] [INFO] taf19mtrUT=running
[[18:15:02]] [INFO] Executing action 262/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:15:01]] [SUCCESS] Screenshot refreshed successfully
[[18:15:01]] [SUCCESS] Screenshot refreshed successfully
[[18:15:01]] [SUCCESS] Screenshot refreshed
[[18:15:01]] [INFO] Refreshing screenshot...
[[18:15:01]] [INFO] oiPcknTonJ=pass
[[18:14:57]] [INFO] oiPcknTonJ=running
[[18:14:57]] [INFO] Executing action 261/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:14:57]] [SUCCESS] Screenshot refreshed successfully
[[18:14:57]] [SUCCESS] Screenshot refreshed successfully
[[18:14:57]] [SUCCESS] Screenshot refreshed
[[18:14:57]] [INFO] Refreshing screenshot...
[[18:14:57]] [INFO] FciJcOsMsB=pass
[[18:14:51]] [INFO] FciJcOsMsB=running
[[18:14:51]] [INFO] Executing action 260/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:14:51]] [SUCCESS] Screenshot refreshed successfully
[[18:14:51]] [SUCCESS] Screenshot refreshed successfully
[[18:14:51]] [SUCCESS] Screenshot refreshed
[[18:14:51]] [INFO] Refreshing screenshot...
[[18:14:51]] [INFO] 2qOXZcEmK8=pass
[[18:14:47]] [INFO] 2qOXZcEmK8=running
[[18:14:47]] [INFO] Executing action 259/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:14:47]] [SUCCESS] Screenshot refreshed successfully
[[18:14:47]] [SUCCESS] Screenshot refreshed successfully
[[18:14:46]] [SUCCESS] Screenshot refreshed
[[18:14:46]] [INFO] Refreshing screenshot...
[[18:14:46]] [INFO] M6HdLxu76S=pass
[[18:14:42]] [INFO] M6HdLxu76S=running
[[18:14:42]] [INFO] Executing action 258/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:14:42]] [SUCCESS] Screenshot refreshed successfully
[[18:14:42]] [SUCCESS] Screenshot refreshed successfully
[[18:14:42]] [SUCCESS] Screenshot refreshed
[[18:14:42]] [INFO] Refreshing screenshot...
[[18:14:42]] [INFO] 6wYIn0igez=pass
[[18:14:37]] [INFO] 6wYIn0igez=running
[[18:14:37]] [INFO] Executing action 257/643: iOS Function: text - Text: "Wonderbaby@6"
[[18:14:37]] [SUCCESS] Screenshot refreshed successfully
[[18:14:37]] [SUCCESS] Screenshot refreshed successfully
[[18:14:37]] [SUCCESS] Screenshot refreshed
[[18:14:37]] [INFO] Refreshing screenshot...
[[18:14:37]] [INFO] DaVBARRwft=pass
[[18:14:33]] [INFO] DaVBARRwft=running
[[18:14:33]] [INFO] Executing action 256/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[18:14:32]] [SUCCESS] Screenshot refreshed successfully
[[18:14:32]] [SUCCESS] Screenshot refreshed successfully
[[18:14:32]] [SUCCESS] Screenshot refreshed
[[18:14:32]] [INFO] Refreshing screenshot...
[[18:14:32]] [INFO] 2fmkjdQPtJ=pass
[[18:14:29]] [INFO] 2fmkjdQPtJ=running
[[18:14:29]] [INFO] Executing action 255/643: Tap on image: captha-chkbox-op-ios.png
[[18:14:28]] [SUCCESS] Screenshot refreshed successfully
[[18:14:28]] [SUCCESS] Screenshot refreshed successfully
[[18:14:28]] [SUCCESS] Screenshot refreshed
[[18:14:28]] [INFO] Refreshing screenshot...
[[18:14:28]] [INFO] Gb2Do6AtSN=pass
[[18:14:23]] [INFO] Gb2Do6AtSN=running
[[18:14:23]] [INFO] Executing action 254/643: iOS Function: text - Text: "<EMAIL>"
[[18:14:23]] [SUCCESS] Screenshot refreshed successfully
[[18:14:23]] [SUCCESS] Screenshot refreshed successfully
[[18:14:23]] [SUCCESS] Screenshot refreshed
[[18:14:23]] [INFO] Refreshing screenshot...
[[18:14:23]] [INFO] y8ZMTkG38M=pass
[[18:14:19]] [INFO] y8ZMTkG38M=running
[[18:14:19]] [INFO] Executing action 253/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[18:14:18]] [SUCCESS] Screenshot refreshed successfully
[[18:14:18]] [SUCCESS] Screenshot refreshed successfully
[[18:14:18]] [SUCCESS] Screenshot refreshed
[[18:14:18]] [INFO] Refreshing screenshot...
[[18:14:18]] [INFO] UUhQjmzfO2=pass
[[18:14:14]] [INFO] UUhQjmzfO2=running
[[18:14:14]] [INFO] Executing action 252/643: Tap on Text: "OnePass"
[[18:14:14]] [SUCCESS] Screenshot refreshed successfully
[[18:14:14]] [SUCCESS] Screenshot refreshed successfully
[[18:14:14]] [SUCCESS] Screenshot refreshed
[[18:14:14]] [INFO] Refreshing screenshot...
[[18:14:14]] [INFO] NCyuT8W5Xz=pass
[[18:14:10]] [INFO] NCyuT8W5Xz=running
[[18:14:10]] [INFO] Executing action 251/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:14:10]] [SUCCESS] Screenshot refreshed successfully
[[18:14:10]] [SUCCESS] Screenshot refreshed successfully
[[18:14:10]] [SUCCESS] Screenshot refreshed
[[18:14:10]] [INFO] Refreshing screenshot...
[[18:14:10]] [INFO] 2kwu2VBmuZ=pass
[[18:14:07]] [INFO] 2kwu2VBmuZ=running
[[18:14:07]] [INFO] Executing action 250/643: iOS Function: alert_accept
[[18:14:07]] [SUCCESS] Screenshot refreshed successfully
[[18:14:07]] [SUCCESS] Screenshot refreshed successfully
[[18:14:07]] [SUCCESS] Screenshot refreshed
[[18:14:07]] [INFO] Refreshing screenshot...
[[18:14:07]] [INFO] cJDpd7aK3d=pass
[[18:14:01]] [INFO] cJDpd7aK3d=running
[[18:14:01]] [INFO] Executing action 249/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:14:01]] [SUCCESS] Screenshot refreshed successfully
[[18:14:01]] [SUCCESS] Screenshot refreshed successfully
[[18:14:00]] [SUCCESS] Screenshot refreshed
[[18:14:00]] [INFO] Refreshing screenshot...
[[18:14:00]] [INFO] FlEukNkjlS=pass
[[18:13:57]] [INFO] FlEukNkjlS=running
[[18:13:57]] [INFO] Executing action 248/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:13:57]] [SUCCESS] Screenshot refreshed successfully
[[18:13:57]] [SUCCESS] Screenshot refreshed successfully
[[18:13:56]] [SUCCESS] Screenshot refreshed
[[18:13:56]] [INFO] Refreshing screenshot...
[[18:13:56]] [INFO] LlRfimKPrn=pass
[[18:13:53]] [INFO] LlRfimKPrn=running
[[18:13:53]] [INFO] Executing action 247/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:13:52]] [SUCCESS] Screenshot refreshed successfully
[[18:13:52]] [SUCCESS] Screenshot refreshed successfully
[[18:13:52]] [SUCCESS] Screenshot refreshed
[[18:13:52]] [INFO] Refreshing screenshot...
[[18:13:52]] [INFO] FciJcOsMsB=pass
[[18:13:46]] [INFO] FciJcOsMsB=running
[[18:13:46]] [INFO] Executing action 246/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:13:45]] [SUCCESS] Screenshot refreshed successfully
[[18:13:45]] [SUCCESS] Screenshot refreshed successfully
[[18:13:45]] [SUCCESS] Screenshot refreshed
[[18:13:45]] [INFO] Refreshing screenshot...
[[18:13:45]] [INFO] 08NzsvhQXK=pass
[[18:13:41]] [INFO] 08NzsvhQXK=running
[[18:13:41]] [INFO] Executing action 245/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:13:41]] [SUCCESS] Screenshot refreshed successfully
[[18:13:41]] [SUCCESS] Screenshot refreshed successfully
[[18:13:41]] [SUCCESS] Screenshot refreshed
[[18:13:41]] [INFO] Refreshing screenshot...
[[18:13:41]] [INFO] IsGWxLFpIn=pass
[[18:13:37]] [INFO] IsGWxLFpIn=running
[[18:13:37]] [INFO] Executing action 244/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:13:36]] [SUCCESS] Screenshot refreshed successfully
[[18:13:36]] [SUCCESS] Screenshot refreshed successfully
[[18:13:36]] [SUCCESS] Screenshot refreshed
[[18:13:36]] [INFO] Refreshing screenshot...
[[18:13:36]] [INFO] dyECdbRifp=pass
[[18:13:32]] [INFO] dyECdbRifp=running
[[18:13:32]] [INFO] Executing action 243/643: iOS Function: text - Text: "Wonderbaby@5"
[[18:13:31]] [SUCCESS] Screenshot refreshed successfully
[[18:13:31]] [SUCCESS] Screenshot refreshed successfully
[[18:13:31]] [SUCCESS] Screenshot refreshed
[[18:13:31]] [INFO] Refreshing screenshot...
[[18:13:31]] [INFO] I5bRbYY1hD=pass
[[18:13:27]] [INFO] I5bRbYY1hD=running
[[18:13:27]] [INFO] Executing action 242/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:13:27]] [SUCCESS] Screenshot refreshed successfully
[[18:13:27]] [SUCCESS] Screenshot refreshed successfully
[[18:13:27]] [SUCCESS] Screenshot refreshed
[[18:13:27]] [INFO] Refreshing screenshot...
[[18:13:27]] [INFO] WMl5g82CCq=pass
[[18:13:22]] [INFO] WMl5g82CCq=running
[[18:13:22]] [INFO] Executing action 241/643: iOS Function: text - Text: "<EMAIL>"
[[18:13:22]] [SUCCESS] Screenshot refreshed successfully
[[18:13:22]] [SUCCESS] Screenshot refreshed successfully
[[18:13:22]] [SUCCESS] Screenshot refreshed
[[18:13:22]] [INFO] Refreshing screenshot...
[[18:13:22]] [INFO] 8OsQmoVYqW=pass
[[18:13:18]] [INFO] 8OsQmoVYqW=running
[[18:13:18]] [INFO] Executing action 240/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:13:17]] [SUCCESS] Screenshot refreshed successfully
[[18:13:17]] [SUCCESS] Screenshot refreshed successfully
[[18:13:17]] [SUCCESS] Screenshot refreshed
[[18:13:17]] [INFO] Refreshing screenshot...
[[18:13:17]] [INFO] ImienLpJEN=pass
[[18:13:14]] [INFO] ImienLpJEN=running
[[18:13:14]] [INFO] Executing action 239/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:13:13]] [SUCCESS] Screenshot refreshed successfully
[[18:13:13]] [SUCCESS] Screenshot refreshed successfully
[[18:13:13]] [SUCCESS] Screenshot refreshed
[[18:13:13]] [INFO] Refreshing screenshot...
[[18:13:13]] [INFO] q4hPXCBtx4=pass
[[18:13:11]] [INFO] q4hPXCBtx4=running
[[18:13:11]] [INFO] Executing action 238/643: iOS Function: alert_accept
[[18:13:10]] [SUCCESS] Screenshot refreshed successfully
[[18:13:10]] [SUCCESS] Screenshot refreshed successfully
[[18:13:10]] [SUCCESS] Screenshot refreshed
[[18:13:10]] [INFO] Refreshing screenshot...
[[18:13:10]] [INFO] 2cTZvK1psn=pass
[[18:13:01]] [INFO] 2cTZvK1psn=running
[[18:13:01]] [INFO] Executing action 237/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:13:01]] [SUCCESS] Screenshot refreshed successfully
[[18:13:01]] [SUCCESS] Screenshot refreshed successfully
[[18:13:00]] [SUCCESS] Screenshot refreshed
[[18:13:00]] [INFO] Refreshing screenshot...
[[18:13:00]] [INFO] Vxt7QOYeDD=pass
[[18:12:56]] [INFO] Vxt7QOYeDD=running
[[18:12:56]] [INFO] Executing action 236/643: Restart app: env[appid]
[[18:12:54]] [INFO] === RETRYING TEST CASE: KmartProdSignin_20250426221008.json (Attempt 3 of 3) ===
[[18:12:54]] [INFO] M6HdLxu76S=fail
[[18:12:54]] [ERROR] Action 258 failed: Element not found: xpath='//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]'
[[18:12:37]] [INFO] M6HdLxu76S=running
[[18:12:37]] [INFO] Executing action 258/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:12:37]] [SUCCESS] Screenshot refreshed successfully
[[18:12:37]] [SUCCESS] Screenshot refreshed successfully
[[18:12:36]] [SUCCESS] Screenshot refreshed
[[18:12:36]] [INFO] Refreshing screenshot...
[[18:12:36]] [INFO] 6wYIn0igez=pass
[[18:12:32]] [INFO] 6wYIn0igez=running
[[18:12:32]] [INFO] Executing action 257/643: iOS Function: text - Text: "Wonderbaby@6"
[[18:12:32]] [SUCCESS] Screenshot refreshed successfully
[[18:12:32]] [SUCCESS] Screenshot refreshed successfully
[[18:12:32]] [SUCCESS] Screenshot refreshed
[[18:12:32]] [INFO] Refreshing screenshot...
[[18:12:32]] [INFO] DaVBARRwft=pass
[[18:12:27]] [INFO] DaVBARRwft=running
[[18:12:27]] [INFO] Executing action 256/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[18:12:27]] [SUCCESS] Screenshot refreshed successfully
[[18:12:27]] [SUCCESS] Screenshot refreshed successfully
[[18:12:27]] [SUCCESS] Screenshot refreshed
[[18:12:27]] [INFO] Refreshing screenshot...
[[18:12:27]] [INFO] 2fmkjdQPtJ=pass
[[18:12:23]] [INFO] 2fmkjdQPtJ=running
[[18:12:23]] [INFO] Executing action 255/643: Tap on image: captha-chkbox-op-ios.png
[[18:12:23]] [SUCCESS] Screenshot refreshed successfully
[[18:12:23]] [SUCCESS] Screenshot refreshed successfully
[[18:12:23]] [SUCCESS] Screenshot refreshed
[[18:12:23]] [INFO] Refreshing screenshot...
[[18:12:23]] [INFO] Gb2Do6AtSN=pass
[[18:12:18]] [INFO] Gb2Do6AtSN=running
[[18:12:18]] [INFO] Executing action 254/643: iOS Function: text - Text: "<EMAIL>"
[[18:12:18]] [SUCCESS] Screenshot refreshed successfully
[[18:12:18]] [SUCCESS] Screenshot refreshed successfully
[[18:12:17]] [SUCCESS] Screenshot refreshed
[[18:12:17]] [INFO] Refreshing screenshot...
[[18:12:17]] [INFO] y8ZMTkG38M=pass
[[18:12:13]] [INFO] y8ZMTkG38M=running
[[18:12:13]] [INFO] Executing action 253/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[18:12:13]] [SUCCESS] Screenshot refreshed successfully
[[18:12:13]] [SUCCESS] Screenshot refreshed successfully
[[18:12:13]] [SUCCESS] Screenshot refreshed
[[18:12:13]] [INFO] Refreshing screenshot...
[[18:12:13]] [INFO] UUhQjmzfO2=pass
[[18:12:09]] [INFO] UUhQjmzfO2=running
[[18:12:09]] [INFO] Executing action 252/643: Tap on Text: "OnePass"
[[18:12:08]] [SUCCESS] Screenshot refreshed successfully
[[18:12:08]] [SUCCESS] Screenshot refreshed successfully
[[18:12:08]] [SUCCESS] Screenshot refreshed
[[18:12:08]] [INFO] Refreshing screenshot...
[[18:12:08]] [INFO] NCyuT8W5Xz=pass
[[18:12:05]] [INFO] NCyuT8W5Xz=running
[[18:12:05]] [INFO] Executing action 251/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:12:04]] [SUCCESS] Screenshot refreshed successfully
[[18:12:04]] [SUCCESS] Screenshot refreshed successfully
[[18:12:04]] [SUCCESS] Screenshot refreshed
[[18:12:04]] [INFO] Refreshing screenshot...
[[18:12:04]] [INFO] 2kwu2VBmuZ=pass
[[18:12:02]] [INFO] 2kwu2VBmuZ=running
[[18:12:02]] [INFO] Executing action 250/643: iOS Function: alert_accept
[[18:12:01]] [SUCCESS] Screenshot refreshed successfully
[[18:12:01]] [SUCCESS] Screenshot refreshed successfully
[[18:12:01]] [SUCCESS] Screenshot refreshed
[[18:12:01]] [INFO] Refreshing screenshot...
[[18:12:01]] [INFO] cJDpd7aK3d=pass
[[18:11:55]] [INFO] cJDpd7aK3d=running
[[18:11:55]] [INFO] Executing action 249/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:11:55]] [SUCCESS] Screenshot refreshed successfully
[[18:11:55]] [SUCCESS] Screenshot refreshed successfully
[[18:11:55]] [SUCCESS] Screenshot refreshed
[[18:11:55]] [INFO] Refreshing screenshot...
[[18:11:55]] [INFO] FlEukNkjlS=pass
[[18:11:51]] [INFO] FlEukNkjlS=running
[[18:11:51]] [INFO] Executing action 248/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:11:51]] [SUCCESS] Screenshot refreshed successfully
[[18:11:51]] [SUCCESS] Screenshot refreshed successfully
[[18:11:51]] [SUCCESS] Screenshot refreshed
[[18:11:51]] [INFO] Refreshing screenshot...
[[18:11:51]] [INFO] LlRfimKPrn=pass
[[18:11:47]] [INFO] LlRfimKPrn=running
[[18:11:47]] [INFO] Executing action 247/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:11:47]] [SUCCESS] Screenshot refreshed successfully
[[18:11:47]] [SUCCESS] Screenshot refreshed successfully
[[18:11:47]] [SUCCESS] Screenshot refreshed
[[18:11:47]] [INFO] Refreshing screenshot...
[[18:11:47]] [INFO] FciJcOsMsB=pass
[[18:11:40]] [INFO] FciJcOsMsB=running
[[18:11:40]] [INFO] Executing action 246/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:11:40]] [SUCCESS] Screenshot refreshed successfully
[[18:11:40]] [SUCCESS] Screenshot refreshed successfully
[[18:11:39]] [SUCCESS] Screenshot refreshed
[[18:11:39]] [INFO] Refreshing screenshot...
[[18:11:39]] [INFO] 08NzsvhQXK=pass
[[18:11:36]] [INFO] 08NzsvhQXK=running
[[18:11:36]] [INFO] Executing action 245/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:11:35]] [SUCCESS] Screenshot refreshed successfully
[[18:11:35]] [SUCCESS] Screenshot refreshed successfully
[[18:11:35]] [SUCCESS] Screenshot refreshed
[[18:11:35]] [INFO] Refreshing screenshot...
[[18:11:35]] [INFO] IsGWxLFpIn=pass
[[18:11:32]] [INFO] IsGWxLFpIn=running
[[18:11:32]] [INFO] Executing action 244/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:11:32]] [SUCCESS] Screenshot refreshed successfully
[[18:11:32]] [SUCCESS] Screenshot refreshed successfully
[[18:11:32]] [SUCCESS] Screenshot refreshed
[[18:11:32]] [INFO] Refreshing screenshot...
[[18:11:32]] [INFO] dyECdbRifp=pass
[[18:11:27]] [INFO] dyECdbRifp=running
[[18:11:27]] [INFO] Executing action 243/643: iOS Function: text - Text: "Wonderbaby@5"
[[18:11:27]] [SUCCESS] Screenshot refreshed successfully
[[18:11:27]] [SUCCESS] Screenshot refreshed successfully
[[18:11:27]] [SUCCESS] Screenshot refreshed
[[18:11:27]] [INFO] Refreshing screenshot...
[[18:11:27]] [INFO] I5bRbYY1hD=pass
[[18:11:23]] [INFO] I5bRbYY1hD=running
[[18:11:23]] [INFO] Executing action 242/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:11:23]] [SUCCESS] Screenshot refreshed successfully
[[18:11:23]] [SUCCESS] Screenshot refreshed successfully
[[18:11:22]] [SUCCESS] Screenshot refreshed
[[18:11:22]] [INFO] Refreshing screenshot...
[[18:11:22]] [INFO] WMl5g82CCq=pass
[[18:11:18]] [INFO] WMl5g82CCq=running
[[18:11:18]] [INFO] Executing action 241/643: iOS Function: text - Text: "<EMAIL>"
[[18:11:18]] [SUCCESS] Screenshot refreshed successfully
[[18:11:18]] [SUCCESS] Screenshot refreshed successfully
[[18:11:17]] [SUCCESS] Screenshot refreshed
[[18:11:17]] [INFO] Refreshing screenshot...
[[18:11:17]] [INFO] 8OsQmoVYqW=pass
[[18:11:13]] [INFO] 8OsQmoVYqW=running
[[18:11:13]] [INFO] Executing action 240/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:11:13]] [SUCCESS] Screenshot refreshed successfully
[[18:11:13]] [SUCCESS] Screenshot refreshed successfully
[[18:11:13]] [SUCCESS] Screenshot refreshed
[[18:11:13]] [INFO] Refreshing screenshot...
[[18:11:13]] [INFO] ImienLpJEN=pass
[[18:11:09]] [INFO] ImienLpJEN=running
[[18:11:09]] [INFO] Executing action 239/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:11:09]] [SUCCESS] Screenshot refreshed successfully
[[18:11:09]] [SUCCESS] Screenshot refreshed successfully
[[18:11:09]] [SUCCESS] Screenshot refreshed
[[18:11:09]] [INFO] Refreshing screenshot...
[[18:11:09]] [INFO] q4hPXCBtx4=pass
[[18:11:07]] [INFO] q4hPXCBtx4=running
[[18:11:07]] [INFO] Executing action 238/643: iOS Function: alert_accept
[[18:11:06]] [SUCCESS] Screenshot refreshed successfully
[[18:11:06]] [SUCCESS] Screenshot refreshed successfully
[[18:11:06]] [SUCCESS] Screenshot refreshed
[[18:11:06]] [INFO] Refreshing screenshot...
[[18:11:06]] [INFO] 2cTZvK1psn=pass
[[18:10:58]] [INFO] 2cTZvK1psn=running
[[18:10:58]] [INFO] Executing action 237/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:10:58]] [SUCCESS] Screenshot refreshed successfully
[[18:10:58]] [SUCCESS] Screenshot refreshed successfully
[[18:10:57]] [SUCCESS] Screenshot refreshed
[[18:10:57]] [INFO] Refreshing screenshot...
[[18:10:57]] [INFO] Vxt7QOYeDD=pass
[[18:10:53]] [INFO] Vxt7QOYeDD=running
[[18:10:53]] [INFO] Executing action 236/643: Restart app: env[appid]
[[18:10:51]] [INFO] === RETRYING TEST CASE: KmartProdSignin_20250426221008.json (Attempt 2 of 3) ===
[[18:10:51]] [INFO] M6HdLxu76S=fail
[[18:10:51]] [ERROR] Action 258 failed: Element not found: xpath='//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]'
[[18:10:34]] [INFO] M6HdLxu76S=running
[[18:10:34]] [INFO] Executing action 258/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:10:34]] [SUCCESS] Screenshot refreshed successfully
[[18:10:34]] [SUCCESS] Screenshot refreshed successfully
[[18:10:33]] [SUCCESS] Screenshot refreshed
[[18:10:33]] [INFO] Refreshing screenshot...
[[18:10:33]] [INFO] 6wYIn0igez=pass
[[18:10:29]] [INFO] 6wYIn0igez=running
[[18:10:29]] [INFO] Executing action 257/643: iOS Function: text - Text: "Wonderbaby@6"
[[18:10:29]] [SUCCESS] Screenshot refreshed successfully
[[18:10:29]] [SUCCESS] Screenshot refreshed successfully
[[18:10:28]] [SUCCESS] Screenshot refreshed
[[18:10:28]] [INFO] Refreshing screenshot...
[[18:10:28]] [INFO] DaVBARRwft=pass
[[18:10:24]] [INFO] DaVBARRwft=running
[[18:10:24]] [INFO] Executing action 256/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[18:10:24]] [SUCCESS] Screenshot refreshed successfully
[[18:10:24]] [SUCCESS] Screenshot refreshed successfully
[[18:10:24]] [SUCCESS] Screenshot refreshed
[[18:10:24]] [INFO] Refreshing screenshot...
[[18:10:24]] [INFO] 2fmkjdQPtJ=pass
[[18:10:20]] [INFO] 2fmkjdQPtJ=running
[[18:10:20]] [INFO] Executing action 255/643: Tap on image: captha-chkbox-op-ios.png
[[18:10:20]] [SUCCESS] Screenshot refreshed successfully
[[18:10:20]] [SUCCESS] Screenshot refreshed successfully
[[18:10:20]] [SUCCESS] Screenshot refreshed
[[18:10:20]] [INFO] Refreshing screenshot...
[[18:10:20]] [INFO] Gb2Do6AtSN=pass
[[18:10:15]] [INFO] Gb2Do6AtSN=running
[[18:10:15]] [INFO] Executing action 254/643: iOS Function: text - Text: "<EMAIL>"
[[18:10:15]] [SUCCESS] Screenshot refreshed successfully
[[18:10:15]] [SUCCESS] Screenshot refreshed successfully
[[18:10:14]] [SUCCESS] Screenshot refreshed
[[18:10:14]] [INFO] Refreshing screenshot...
[[18:10:14]] [INFO] y8ZMTkG38M=pass
[[18:10:10]] [INFO] y8ZMTkG38M=running
[[18:10:10]] [INFO] Executing action 253/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[18:10:10]] [SUCCESS] Screenshot refreshed successfully
[[18:10:10]] [SUCCESS] Screenshot refreshed successfully
[[18:10:10]] [SUCCESS] Screenshot refreshed
[[18:10:10]] [INFO] Refreshing screenshot...
[[18:10:10]] [INFO] UUhQjmzfO2=pass
[[18:10:06]] [INFO] UUhQjmzfO2=running
[[18:10:06]] [INFO] Executing action 252/643: Tap on Text: "OnePass"
[[18:10:05]] [SUCCESS] Screenshot refreshed successfully
[[18:10:05]] [SUCCESS] Screenshot refreshed successfully
[[18:10:05]] [SUCCESS] Screenshot refreshed
[[18:10:05]] [INFO] Refreshing screenshot...
[[18:10:05]] [INFO] NCyuT8W5Xz=pass
[[18:10:02]] [INFO] NCyuT8W5Xz=running
[[18:10:02]] [INFO] Executing action 251/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:10:01]] [SUCCESS] Screenshot refreshed successfully
[[18:10:01]] [SUCCESS] Screenshot refreshed successfully
[[18:10:01]] [SUCCESS] Screenshot refreshed
[[18:10:01]] [INFO] Refreshing screenshot...
[[18:10:01]] [INFO] 2kwu2VBmuZ=pass
[[18:09:59]] [INFO] 2kwu2VBmuZ=running
[[18:09:59]] [INFO] Executing action 250/643: iOS Function: alert_accept
[[18:09:58]] [SUCCESS] Screenshot refreshed successfully
[[18:09:58]] [SUCCESS] Screenshot refreshed successfully
[[18:09:58]] [SUCCESS] Screenshot refreshed
[[18:09:58]] [INFO] Refreshing screenshot...
[[18:09:58]] [INFO] cJDpd7aK3d=pass
[[18:09:53]] [INFO] cJDpd7aK3d=running
[[18:09:53]] [INFO] Executing action 249/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:09:52]] [SUCCESS] Screenshot refreshed successfully
[[18:09:52]] [SUCCESS] Screenshot refreshed successfully
[[18:09:52]] [SUCCESS] Screenshot refreshed
[[18:09:52]] [INFO] Refreshing screenshot...
[[18:09:52]] [INFO] FlEukNkjlS=pass
[[18:09:49]] [INFO] FlEukNkjlS=running
[[18:09:49]] [INFO] Executing action 248/643: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[18:09:48]] [SUCCESS] Screenshot refreshed successfully
[[18:09:48]] [SUCCESS] Screenshot refreshed successfully
[[18:09:48]] [SUCCESS] Screenshot refreshed
[[18:09:48]] [INFO] Refreshing screenshot...
[[18:09:48]] [INFO] LlRfimKPrn=pass
[[18:09:44]] [INFO] LlRfimKPrn=running
[[18:09:44]] [INFO] Executing action 247/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:09:44]] [SUCCESS] Screenshot refreshed successfully
[[18:09:44]] [SUCCESS] Screenshot refreshed successfully
[[18:09:44]] [SUCCESS] Screenshot refreshed
[[18:09:44]] [INFO] Refreshing screenshot...
[[18:09:44]] [INFO] FciJcOsMsB=pass
[[18:09:37]] [INFO] FciJcOsMsB=running
[[18:09:37]] [INFO] Executing action 246/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:09:37]] [SUCCESS] Screenshot refreshed successfully
[[18:09:37]] [SUCCESS] Screenshot refreshed successfully
[[18:09:37]] [SUCCESS] Screenshot refreshed
[[18:09:37]] [INFO] Refreshing screenshot...
[[18:09:37]] [INFO] 08NzsvhQXK=pass
[[18:09:33]] [INFO] 08NzsvhQXK=running
[[18:09:33]] [INFO] Executing action 245/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:09:33]] [SUCCESS] Screenshot refreshed successfully
[[18:09:33]] [SUCCESS] Screenshot refreshed successfully
[[18:09:32]] [SUCCESS] Screenshot refreshed
[[18:09:32]] [INFO] Refreshing screenshot...
[[18:09:32]] [INFO] IsGWxLFpIn=pass
[[18:09:28]] [INFO] IsGWxLFpIn=running
[[18:09:28]] [INFO] Executing action 244/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:09:28]] [SUCCESS] Screenshot refreshed successfully
[[18:09:28]] [SUCCESS] Screenshot refreshed successfully
[[18:09:28]] [SUCCESS] Screenshot refreshed
[[18:09:28]] [INFO] Refreshing screenshot...
[[18:09:28]] [INFO] dyECdbRifp=pass
[[18:09:23]] [INFO] dyECdbRifp=running
[[18:09:23]] [INFO] Executing action 243/643: iOS Function: text - Text: "Wonderbaby@5"
[[18:09:23]] [SUCCESS] Screenshot refreshed successfully
[[18:09:23]] [SUCCESS] Screenshot refreshed successfully
[[18:09:23]] [SUCCESS] Screenshot refreshed
[[18:09:23]] [INFO] Refreshing screenshot...
[[18:09:23]] [INFO] I5bRbYY1hD=pass
[[18:09:19]] [INFO] I5bRbYY1hD=running
[[18:09:19]] [INFO] Executing action 242/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:09:18]] [SUCCESS] Screenshot refreshed successfully
[[18:09:18]] [SUCCESS] Screenshot refreshed successfully
[[18:09:18]] [SUCCESS] Screenshot refreshed
[[18:09:18]] [INFO] Refreshing screenshot...
[[18:09:18]] [INFO] WMl5g82CCq=pass
[[18:09:14]] [INFO] WMl5g82CCq=running
[[18:09:14]] [INFO] Executing action 241/643: iOS Function: text - Text: "<EMAIL>"
[[18:09:13]] [SUCCESS] Screenshot refreshed successfully
[[18:09:13]] [SUCCESS] Screenshot refreshed successfully
[[18:09:13]] [SUCCESS] Screenshot refreshed
[[18:09:13]] [INFO] Refreshing screenshot...
[[18:09:13]] [INFO] 8OsQmoVYqW=pass
[[18:09:09]] [INFO] 8OsQmoVYqW=running
[[18:09:09]] [INFO] Executing action 240/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:09:09]] [SUCCESS] Screenshot refreshed successfully
[[18:09:09]] [SUCCESS] Screenshot refreshed successfully
[[18:09:09]] [SUCCESS] Screenshot refreshed
[[18:09:09]] [INFO] Refreshing screenshot...
[[18:09:09]] [INFO] ImienLpJEN=pass
[[18:09:05]] [INFO] ImienLpJEN=running
[[18:09:05]] [INFO] Executing action 239/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:09:05]] [SUCCESS] Screenshot refreshed successfully
[[18:09:05]] [SUCCESS] Screenshot refreshed successfully
[[18:09:05]] [SUCCESS] Screenshot refreshed
[[18:09:05]] [INFO] Refreshing screenshot...
[[18:09:05]] [INFO] q4hPXCBtx4=pass
[[18:09:02]] [INFO] q4hPXCBtx4=running
[[18:09:02]] [INFO] Executing action 238/643: iOS Function: alert_accept
[[18:09:02]] [SUCCESS] Screenshot refreshed successfully
[[18:09:02]] [SUCCESS] Screenshot refreshed successfully
[[18:09:02]] [SUCCESS] Screenshot refreshed
[[18:09:02]] [INFO] Refreshing screenshot...
[[18:09:02]] [INFO] 2cTZvK1psn=pass
[[18:08:53]] [INFO] 2cTZvK1psn=running
[[18:08:53]] [INFO] Executing action 237/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:08:53]] [SUCCESS] Screenshot refreshed successfully
[[18:08:53]] [SUCCESS] Screenshot refreshed successfully
[[18:08:53]] [SUCCESS] Screenshot refreshed
[[18:08:53]] [INFO] Refreshing screenshot...
[[18:08:53]] [INFO] Vxt7QOYeDD=pass
[[18:08:40]] [SUCCESS] Screenshot refreshed successfully
[[18:08:40]] [SUCCESS] Screenshot refreshed successfully
[[18:08:39]] [INFO] Vxt7QOYeDD=running
[[18:08:39]] [INFO] Executing action 236/643: Restart app: env[appid]
[[18:08:39]] [SUCCESS] Screenshot refreshed
[[18:08:39]] [INFO] Refreshing screenshot...
[[18:08:39]] [SUCCESS] Screenshot refreshed successfully
[[18:08:39]] [SUCCESS] Screenshot refreshed successfully
[[18:08:39]] [SUCCESS] Screenshot refreshed
[[18:08:39]] [INFO] Refreshing screenshot...
[[18:08:36]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:08:36]] [SUCCESS] Screenshot refreshed successfully
[[18:08:36]] [SUCCESS] Screenshot refreshed successfully
[[18:08:36]] [SUCCESS] Screenshot refreshed
[[18:08:36]] [INFO] Refreshing screenshot...
[[18:08:24]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:08:23]] [SUCCESS] Screenshot refreshed successfully
[[18:08:23]] [SUCCESS] Screenshot refreshed successfully
[[18:08:23]] [SUCCESS] Screenshot refreshed
[[18:08:23]] [INFO] Refreshing screenshot...
[[18:08:19]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:08:19]] [SUCCESS] Screenshot refreshed successfully
[[18:08:19]] [SUCCESS] Screenshot refreshed successfully
[[18:08:19]] [SUCCESS] Screenshot refreshed
[[18:08:19]] [INFO] Refreshing screenshot...
[[18:08:15]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:08:15]] [SUCCESS] Screenshot refreshed successfully
[[18:08:15]] [SUCCESS] Screenshot refreshed successfully
[[18:08:15]] [SUCCESS] Screenshot refreshed
[[18:08:15]] [INFO] Refreshing screenshot...
[[18:08:08]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:08:08]] [SUCCESS] Screenshot refreshed successfully
[[18:08:08]] [SUCCESS] Screenshot refreshed successfully
[[18:08:08]] [SUCCESS] Screenshot refreshed
[[18:08:08]] [INFO] Refreshing screenshot...
[[18:08:01]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:08:01]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:08:01]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:08:01]] [INFO] DYWpUY7xB6=running
[[18:08:01]] [INFO] Executing action 235/643: cleanupSteps action
[[18:08:01]] [SUCCESS] Screenshot refreshed successfully
[[18:08:01]] [SUCCESS] Screenshot refreshed successfully
[[18:08:01]] [SUCCESS] Screenshot refreshed
[[18:08:01]] [INFO] Refreshing screenshot...
[[18:08:01]] [INFO] OyUowAaBzD=pass
[[18:07:57]] [INFO] OyUowAaBzD=running
[[18:07:57]] [INFO] Executing action 234/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:07:57]] [SUCCESS] Screenshot refreshed successfully
[[18:07:57]] [SUCCESS] Screenshot refreshed successfully
[[18:07:56]] [SUCCESS] Screenshot refreshed
[[18:07:56]] [INFO] Refreshing screenshot...
[[18:07:56]] [INFO] Ob26qqcA0p=pass
[[18:07:50]] [INFO] Ob26qqcA0p=running
[[18:07:50]] [INFO] Executing action 233/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:07:49]] [SUCCESS] Screenshot refreshed successfully
[[18:07:49]] [SUCCESS] Screenshot refreshed successfully
[[18:07:49]] [SUCCESS] Screenshot refreshed
[[18:07:49]] [INFO] Refreshing screenshot...
[[18:07:49]] [INFO] k3mu9Mt7Ec=pass
[[18:07:46]] [INFO] k3mu9Mt7Ec=running
[[18:07:46]] [INFO] Executing action 232/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:07:45]] [SUCCESS] Screenshot refreshed successfully
[[18:07:45]] [SUCCESS] Screenshot refreshed successfully
[[18:07:45]] [SUCCESS] Screenshot refreshed
[[18:07:45]] [INFO] Refreshing screenshot...
[[18:07:45]] [INFO] yhmzeynQyu=pass
[[18:07:41]] [INFO] yhmzeynQyu=running
[[18:07:41]] [INFO] Executing action 231/643: Tap on Text: "Remove"
[[18:07:41]] [SUCCESS] Screenshot refreshed successfully
[[18:07:41]] [SUCCESS] Screenshot refreshed successfully
[[18:07:41]] [SUCCESS] Screenshot refreshed
[[18:07:41]] [INFO] Refreshing screenshot...
[[18:07:41]] [INFO] zCHZwhvc44=pass
[[18:07:35]] [INFO] zCHZwhvc44=running
[[18:07:35]] [INFO] Executing action 230/643: ifThenSteps action
[[18:07:35]] [SUCCESS] Screenshot refreshed successfully
[[18:07:35]] [SUCCESS] Screenshot refreshed successfully
[[18:07:35]] [SUCCESS] Screenshot refreshed
[[18:07:35]] [INFO] Refreshing screenshot...
[[18:07:35]] [INFO] yhmzeynQyu=pass
[[18:07:31]] [INFO] yhmzeynQyu=running
[[18:07:31]] [INFO] Executing action 229/643: Tap on Text: "Remove"
[[18:07:31]] [SUCCESS] Screenshot refreshed successfully
[[18:07:31]] [SUCCESS] Screenshot refreshed successfully
[[18:07:31]] [SUCCESS] Screenshot refreshed
[[18:07:31]] [INFO] Refreshing screenshot...
[[18:07:31]] [INFO] zCHZwhvc44=pass
[[18:07:25]] [INFO] zCHZwhvc44=running
[[18:07:25]] [INFO] Executing action 228/643: ifThenSteps action
[[18:07:25]] [SUCCESS] Screenshot refreshed successfully
[[18:07:25]] [SUCCESS] Screenshot refreshed successfully
[[18:07:25]] [SUCCESS] Screenshot refreshed
[[18:07:25]] [INFO] Refreshing screenshot...
[[18:07:25]] [INFO] F1olhgKhUt=pass
[[18:07:20]] [INFO] F1olhgKhUt=running
[[18:07:20]] [INFO] Executing action 227/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:07:20]] [SUCCESS] Screenshot refreshed successfully
[[18:07:20]] [SUCCESS] Screenshot refreshed successfully
[[18:07:20]] [SUCCESS] Screenshot refreshed
[[18:07:20]] [INFO] Refreshing screenshot...
[[18:07:20]] [INFO] 8umPSX0vrr=pass
[[18:07:16]] [INFO] 8umPSX0vrr=running
[[18:07:16]] [INFO] Executing action 226/643: Tap on image: banner-close-updated.png
[[18:07:16]] [SUCCESS] Screenshot refreshed successfully
[[18:07:16]] [SUCCESS] Screenshot refreshed successfully
[[18:07:16]] [SUCCESS] Screenshot refreshed
[[18:07:16]] [INFO] Refreshing screenshot...
[[18:07:16]] [INFO] pr9o8Zsm5p=pass
[[18:07:12]] [INFO] pr9o8Zsm5p=running
[[18:07:12]] [INFO] Executing action 225/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[18:07:12]] [SUCCESS] Screenshot refreshed successfully
[[18:07:12]] [SUCCESS] Screenshot refreshed successfully
[[18:07:11]] [SUCCESS] Screenshot refreshed
[[18:07:11]] [INFO] Refreshing screenshot...
[[18:07:11]] [INFO] Qbg9bipTGs=pass
[[18:07:08]] [INFO] Qbg9bipTGs=running
[[18:07:08]] [INFO] Executing action 224/643: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[18:07:08]] [SUCCESS] Screenshot refreshed successfully
[[18:07:08]] [SUCCESS] Screenshot refreshed successfully
[[18:07:08]] [SUCCESS] Screenshot refreshed
[[18:07:08]] [INFO] Refreshing screenshot...
[[18:07:08]] [INFO] Ob26qqcA0p=pass
[[18:07:03]] [INFO] Ob26qqcA0p=running
[[18:07:03]] [INFO] Executing action 223/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:07:03]] [SUCCESS] Screenshot refreshed successfully
[[18:07:03]] [SUCCESS] Screenshot refreshed successfully
[[18:07:03]] [SUCCESS] Screenshot refreshed
[[18:07:03]] [INFO] Refreshing screenshot...
[[18:07:03]] [INFO] ByviEQxEgr=pass
[[18:06:51]] [INFO] ByviEQxEgr=running
[[18:06:51]] [INFO] Executing action 222/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:06:51]] [SUCCESS] Screenshot refreshed successfully
[[18:06:51]] [SUCCESS] Screenshot refreshed successfully
[[18:06:50]] [SUCCESS] Screenshot refreshed
[[18:06:50]] [INFO] Refreshing screenshot...
[[18:06:50]] [INFO] lWIRxRm6HE=pass
[[18:06:46]] [INFO] lWIRxRm6HE=running
[[18:06:46]] [INFO] Executing action 221/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:06:46]] [SUCCESS] Screenshot refreshed successfully
[[18:06:46]] [SUCCESS] Screenshot refreshed successfully
[[18:06:46]] [SUCCESS] Screenshot refreshed
[[18:06:46]] [INFO] Refreshing screenshot...
[[18:06:46]] [INFO] uOt2cFGhGr=pass
[[18:06:42]] [INFO] uOt2cFGhGr=running
[[18:06:42]] [INFO] Executing action 220/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:06:41]] [SUCCESS] Screenshot refreshed successfully
[[18:06:41]] [SUCCESS] Screenshot refreshed successfully
[[18:06:41]] [SUCCESS] Screenshot refreshed
[[18:06:41]] [INFO] Refreshing screenshot...
[[18:06:41]] [INFO] Q0fomJIDoQ=pass
[[18:06:38]] [INFO] Q0fomJIDoQ=running
[[18:06:38]] [INFO] Executing action 219/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[18:06:37]] [SUCCESS] Screenshot refreshed successfully
[[18:06:37]] [SUCCESS] Screenshot refreshed successfully
[[18:06:37]] [SUCCESS] Screenshot refreshed
[[18:06:37]] [INFO] Refreshing screenshot...
[[18:06:37]] [INFO] yhmzeynQyu=pass
[[18:06:33]] [INFO] yhmzeynQyu=running
[[18:06:33]] [INFO] Executing action 218/643: Tap on Text: "Remove"
[[18:06:33]] [SUCCESS] Screenshot refreshed successfully
[[18:06:33]] [SUCCESS] Screenshot refreshed successfully
[[18:06:33]] [SUCCESS] Screenshot refreshed
[[18:06:33]] [INFO] Refreshing screenshot...
[[18:06:33]] [INFO] Q0fomJIDoQ=pass
[[18:06:29]] [INFO] Q0fomJIDoQ=running
[[18:06:29]] [INFO] Executing action 217/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[18:06:29]] [SUCCESS] Screenshot refreshed successfully
[[18:06:29]] [SUCCESS] Screenshot refreshed successfully
[[18:06:29]] [SUCCESS] Screenshot refreshed
[[18:06:29]] [INFO] Refreshing screenshot...
[[18:06:29]] [INFO] y4i304JeJj=pass
[[18:06:25]] [INFO] y4i304JeJj=running
[[18:06:25]] [INFO] Executing action 216/643: Tap on Text: "Move"
[[18:06:25]] [SUCCESS] Screenshot refreshed successfully
[[18:06:25]] [SUCCESS] Screenshot refreshed successfully
[[18:06:24]] [SUCCESS] Screenshot refreshed
[[18:06:24]] [INFO] Refreshing screenshot...
[[18:06:24]] [INFO] Q0fomJIDoQ=pass
[[18:06:21]] [INFO] Q0fomJIDoQ=running
[[18:06:21]] [INFO] Executing action 215/643: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:06:20]] [SUCCESS] Screenshot refreshed successfully
[[18:06:20]] [SUCCESS] Screenshot refreshed successfully
[[18:06:20]] [SUCCESS] Screenshot refreshed
[[18:06:20]] [INFO] Refreshing screenshot...
[[18:06:20]] [INFO] Q0fomJIDoQ=pass
[[18:06:17]] [INFO] Q0fomJIDoQ=running
[[18:06:17]] [INFO] Executing action 214/643: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[18:06:17]] [SUCCESS] Screenshot refreshed successfully
[[18:06:17]] [SUCCESS] Screenshot refreshed successfully
[[18:06:17]] [SUCCESS] Screenshot refreshed
[[18:06:17]] [INFO] Refreshing screenshot...
[[18:06:17]] [INFO] F1olhgKhUt=pass
[[18:06:13]] [INFO] F1olhgKhUt=running
[[18:06:13]] [INFO] Executing action 213/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[18:06:12]] [SUCCESS] Screenshot refreshed successfully
[[18:06:12]] [SUCCESS] Screenshot refreshed successfully
[[18:06:12]] [SUCCESS] Screenshot refreshed
[[18:06:12]] [INFO] Refreshing screenshot...
[[18:06:12]] [INFO] WbxRVpWtjw=pass
[[18:06:08]] [INFO] WbxRVpWtjw=running
[[18:06:08]] [INFO] Executing action 212/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:06:08]] [SUCCESS] Screenshot refreshed successfully
[[18:06:08]] [SUCCESS] Screenshot refreshed successfully
[[18:06:07]] [SUCCESS] Screenshot refreshed
[[18:06:07]] [INFO] Refreshing screenshot...
[[18:06:07]] [INFO] H3IAmq3r3i=pass
[[18:06:00]] [INFO] H3IAmq3r3i=running
[[18:06:00]] [INFO] Executing action 211/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:06:00]] [SUCCESS] Screenshot refreshed successfully
[[18:06:00]] [SUCCESS] Screenshot refreshed successfully
[[18:06:00]] [SUCCESS] Screenshot refreshed
[[18:06:00]] [INFO] Refreshing screenshot...
[[18:06:00]] [INFO] uOt2cFGhGr=pass
[[18:05:54]] [INFO] uOt2cFGhGr=running
[[18:05:54]] [INFO] Executing action 210/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:05:54]] [SUCCESS] Screenshot refreshed successfully
[[18:05:54]] [SUCCESS] Screenshot refreshed successfully
[[18:05:53]] [SUCCESS] Screenshot refreshed
[[18:05:53]] [INFO] Refreshing screenshot...
[[18:05:53]] [INFO] eLxHVWKeDQ=pass
[[18:05:50]] [INFO] eLxHVWKeDQ=running
[[18:05:50]] [INFO] Executing action 209/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:05:49]] [SUCCESS] Screenshot refreshed successfully
[[18:05:49]] [SUCCESS] Screenshot refreshed successfully
[[18:05:49]] [SUCCESS] Screenshot refreshed
[[18:05:49]] [INFO] Refreshing screenshot...
[[18:05:49]] [INFO] ghzdMuwrHj=pass
[[18:05:45]] [INFO] ghzdMuwrHj=running
[[18:05:45]] [INFO] Executing action 208/643: iOS Function: text - Text: "P_43386093"
[[18:05:45]] [SUCCESS] Screenshot refreshed successfully
[[18:05:45]] [SUCCESS] Screenshot refreshed successfully
[[18:05:45]] [SUCCESS] Screenshot refreshed
[[18:05:45]] [INFO] Refreshing screenshot...
[[18:05:45]] [INFO] fMzoZJg9I7=pass
[[18:05:40]] [INFO] fMzoZJg9I7=running
[[18:05:40]] [INFO] Executing action 207/643: Tap on Text: "Find"
[[18:05:40]] [SUCCESS] Screenshot refreshed successfully
[[18:05:40]] [SUCCESS] Screenshot refreshed successfully
[[18:05:39]] [SUCCESS] Screenshot refreshed
[[18:05:39]] [INFO] Refreshing screenshot...
[[18:05:39]] [INFO] j1JjmfPRaE=pass
[[18:05:35]] [INFO] j1JjmfPRaE=running
[[18:05:35]] [INFO] Executing action 206/643: Restart app: env[appid]
[[18:05:35]] [SUCCESS] Screenshot refreshed successfully
[[18:05:35]] [SUCCESS] Screenshot refreshed successfully
[[18:05:34]] [SUCCESS] Screenshot refreshed
[[18:05:34]] [INFO] Refreshing screenshot...
[[18:05:34]] [INFO] WbxRVpWtjw=pass
[[18:05:30]] [INFO] WbxRVpWtjw=running
[[18:05:30]] [INFO] Executing action 205/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:05:30]] [SUCCESS] Screenshot refreshed successfully
[[18:05:30]] [SUCCESS] Screenshot refreshed successfully
[[18:05:30]] [SUCCESS] Screenshot refreshed
[[18:05:30]] [INFO] Refreshing screenshot...
[[18:05:30]] [INFO] H3IAmq3r3i=pass
[[18:05:23]] [INFO] H3IAmq3r3i=running
[[18:05:23]] [INFO] Executing action 204/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:05:23]] [SUCCESS] Screenshot refreshed successfully
[[18:05:23]] [SUCCESS] Screenshot refreshed successfully
[[18:05:22]] [SUCCESS] Screenshot refreshed
[[18:05:22]] [INFO] Refreshing screenshot...
[[18:05:22]] [INFO] ITHvSyXXmu=pass
[[18:05:18]] [INFO] ITHvSyXXmu=running
[[18:05:18]] [INFO] Executing action 203/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:05:18]] [SUCCESS] Screenshot refreshed successfully
[[18:05:18]] [SUCCESS] Screenshot refreshed successfully
[[18:05:18]] [SUCCESS] Screenshot refreshed
[[18:05:18]] [INFO] Refreshing screenshot...
[[18:05:18]] [INFO] eLxHVWKeDQ=pass
[[18:05:02]] [INFO] eLxHVWKeDQ=running
[[18:05:02]] [INFO] Executing action 202/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[18:05:02]] [SUCCESS] Screenshot refreshed successfully
[[18:05:02]] [SUCCESS] Screenshot refreshed successfully
[[18:05:02]] [SUCCESS] Screenshot refreshed
[[18:05:02]] [INFO] Refreshing screenshot...
[[18:05:02]] [INFO] WbxRVpWtjw=pass
[[18:04:58]] [INFO] WbxRVpWtjw=running
[[18:04:58]] [INFO] Executing action 201/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[18:04:57]] [SUCCESS] Screenshot refreshed successfully
[[18:04:57]] [SUCCESS] Screenshot refreshed successfully
[[18:04:57]] [SUCCESS] Screenshot refreshed
[[18:04:57]] [INFO] Refreshing screenshot...
[[18:04:57]] [INFO] H3IAmq3r3i=pass
[[18:04:50]] [INFO] H3IAmq3r3i=running
[[18:04:50]] [INFO] Executing action 200/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"to wishlist")]" is visible
[[18:04:50]] [SUCCESS] Screenshot refreshed successfully
[[18:04:50]] [SUCCESS] Screenshot refreshed successfully
[[18:04:50]] [SUCCESS] Screenshot refreshed
[[18:04:50]] [INFO] Refreshing screenshot...
[[18:04:50]] [INFO] ITHvSyXXmu=pass
[[18:04:46]] [INFO] ITHvSyXXmu=running
[[18:04:46]] [INFO] Executing action 199/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[18:04:45]] [SUCCESS] Screenshot refreshed successfully
[[18:04:45]] [SUCCESS] Screenshot refreshed successfully
[[18:04:45]] [SUCCESS] Screenshot refreshed
[[18:04:45]] [INFO] Refreshing screenshot...
[[18:04:45]] [INFO] eLxHVWKeDQ=pass
[[18:04:41]] [INFO] eLxHVWKeDQ=running
[[18:04:41]] [INFO] Executing action 198/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[18:04:41]] [SUCCESS] Screenshot refreshed successfully
[[18:04:41]] [SUCCESS] Screenshot refreshed successfully
[[18:04:41]] [SUCCESS] Screenshot refreshed
[[18:04:41]] [INFO] Refreshing screenshot...
[[18:04:41]] [INFO] nAB6Q8LAdv=pass
[[18:04:37]] [INFO] nAB6Q8LAdv=running
[[18:04:37]] [INFO] Executing action 197/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:04:37]] [SUCCESS] Screenshot refreshed successfully
[[18:04:37]] [SUCCESS] Screenshot refreshed successfully
[[18:04:37]] [SUCCESS] Screenshot refreshed
[[18:04:37]] [INFO] Refreshing screenshot...
[[18:04:37]] [INFO] sc2KH9bG6H=pass
[[18:04:33]] [INFO] sc2KH9bG6H=running
[[18:04:33]] [INFO] Executing action 196/643: iOS Function: text - Text: "Uno card"
[[18:04:33]] [SUCCESS] Screenshot refreshed successfully
[[18:04:33]] [SUCCESS] Screenshot refreshed successfully
[[18:04:32]] [SUCCESS] Screenshot refreshed
[[18:04:32]] [INFO] Refreshing screenshot...
[[18:04:32]] [INFO] rqLJpAP0mA=pass
[[18:04:28]] [INFO] rqLJpAP0mA=running
[[18:04:28]] [INFO] Executing action 195/643: Tap on Text: "Find"
[[18:04:27]] [SUCCESS] Screenshot refreshed successfully
[[18:04:27]] [SUCCESS] Screenshot refreshed successfully
[[18:04:27]] [SUCCESS] Screenshot refreshed
[[18:04:27]] [INFO] Refreshing screenshot...
[[18:04:27]] [INFO] yiKyF5FJwN=pass
[[18:04:17]] [INFO] yiKyF5FJwN=running
[[18:04:17]] [INFO] Executing action 194/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[18:04:16]] [SUCCESS] Screenshot refreshed successfully
[[18:04:16]] [SUCCESS] Screenshot refreshed successfully
[[18:04:16]] [SUCCESS] Screenshot refreshed
[[18:04:16]] [INFO] Refreshing screenshot...
[[18:04:16]] [INFO] sTtseHOKfa=pass
[[18:04:12]] [INFO] sTtseHOKfa=running
[[18:04:12]] [INFO] Executing action 193/643: iOS Function: text - Text: "Wonderbaby@5"
[[18:04:12]] [SUCCESS] Screenshot refreshed successfully
[[18:04:12]] [SUCCESS] Screenshot refreshed successfully
[[18:04:11]] [SUCCESS] Screenshot refreshed
[[18:04:11]] [INFO] Refreshing screenshot...
[[18:04:11]] [INFO] T3MmUw30SF=pass
[[18:04:07]] [INFO] T3MmUw30SF=running
[[18:04:07]] [INFO] Executing action 192/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:04:07]] [SUCCESS] Screenshot refreshed successfully
[[18:04:07]] [SUCCESS] Screenshot refreshed successfully
[[18:04:07]] [SUCCESS] Screenshot refreshed
[[18:04:07]] [INFO] Refreshing screenshot...
[[18:04:07]] [INFO] PPIBJbaXNx=pass
[[18:04:02]] [INFO] PPIBJbaXNx=running
[[18:04:02]] [INFO] Executing action 191/643: iOS Function: text - Text: "<EMAIL>"
[[18:04:02]] [SUCCESS] Screenshot refreshed successfully
[[18:04:02]] [SUCCESS] Screenshot refreshed successfully
[[18:04:02]] [SUCCESS] Screenshot refreshed
[[18:04:02]] [INFO] Refreshing screenshot...
[[18:04:02]] [INFO] LDkFLWks00=pass
[[18:03:58]] [INFO] LDkFLWks00=running
[[18:03:58]] [INFO] Executing action 190/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:03:58]] [SUCCESS] Screenshot refreshed successfully
[[18:03:58]] [SUCCESS] Screenshot refreshed successfully
[[18:03:57]] [SUCCESS] Screenshot refreshed
[[18:03:57]] [INFO] Refreshing screenshot...
[[18:03:57]] [INFO] 3caMBvQX7k=pass
[[18:03:54]] [INFO] 3caMBvQX7k=running
[[18:03:54]] [INFO] Executing action 189/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:03:54]] [SUCCESS] Screenshot refreshed successfully
[[18:03:54]] [SUCCESS] Screenshot refreshed successfully
[[18:03:54]] [SUCCESS] Screenshot refreshed
[[18:03:54]] [INFO] Refreshing screenshot...
[[18:03:54]] [INFO] yUJyVO5Wev=pass
[[18:03:51]] [INFO] yUJyVO5Wev=running
[[18:03:51]] [INFO] Executing action 188/643: iOS Function: alert_accept
[[18:03:51]] [SUCCESS] Screenshot refreshed successfully
[[18:03:51]] [SUCCESS] Screenshot refreshed successfully
[[18:03:51]] [SUCCESS] Screenshot refreshed
[[18:03:51]] [INFO] Refreshing screenshot...
[[18:03:51]] [INFO] rkL0oz4kiL=pass
[[18:03:43]] [INFO] rkL0oz4kiL=running
[[18:03:43]] [INFO] Executing action 187/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[18:03:42]] [SUCCESS] Screenshot refreshed successfully
[[18:03:42]] [SUCCESS] Screenshot refreshed successfully
[[18:03:42]] [SUCCESS] Screenshot refreshed
[[18:03:42]] [INFO] Refreshing screenshot...
[[18:03:42]] [INFO] HotUJOd6oB=pass
[[18:03:29]] [SUCCESS] Screenshot refreshed successfully
[[18:03:29]] [SUCCESS] Screenshot refreshed successfully
[[18:03:29]] [INFO] HotUJOd6oB=running
[[18:03:29]] [INFO] Executing action 186/643: Restart app: env[appid]
[[18:03:29]] [SUCCESS] Screenshot refreshed
[[18:03:29]] [INFO] Refreshing screenshot...
[[18:03:28]] [SUCCESS] Screenshot refreshed successfully
[[18:03:28]] [SUCCESS] Screenshot refreshed successfully
[[18:03:28]] [SUCCESS] Screenshot refreshed
[[18:03:28]] [INFO] Refreshing screenshot...
[[18:03:26]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[18:03:25]] [SUCCESS] Screenshot refreshed successfully
[[18:03:25]] [SUCCESS] Screenshot refreshed successfully
[[18:03:25]] [SUCCESS] Screenshot refreshed
[[18:03:25]] [INFO] Refreshing screenshot...
[[18:03:13]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[18:03:13]] [SUCCESS] Screenshot refreshed successfully
[[18:03:13]] [SUCCESS] Screenshot refreshed successfully
[[18:03:12]] [SUCCESS] Screenshot refreshed
[[18:03:12]] [INFO] Refreshing screenshot...
[[18:03:09]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[18:03:09]] [SUCCESS] Screenshot refreshed successfully
[[18:03:09]] [SUCCESS] Screenshot refreshed successfully
[[18:03:08]] [SUCCESS] Screenshot refreshed
[[18:03:08]] [INFO] Refreshing screenshot...
[[18:03:05]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:03:04]] [SUCCESS] Screenshot refreshed successfully
[[18:03:04]] [SUCCESS] Screenshot refreshed successfully
[[18:03:04]] [SUCCESS] Screenshot refreshed
[[18:03:04]] [INFO] Refreshing screenshot...
[[18:02:58]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[18:02:58]] [SUCCESS] Screenshot refreshed successfully
[[18:02:58]] [SUCCESS] Screenshot refreshed successfully
[[18:02:57]] [SUCCESS] Screenshot refreshed
[[18:02:57]] [INFO] Refreshing screenshot...
[[18:02:51]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[18:02:51]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[18:02:51]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[18:02:51]] [INFO] IR7wnjW7C8=running
[[18:02:51]] [INFO] Executing action 185/643: cleanupSteps action
[[18:02:50]] [SUCCESS] Screenshot refreshed successfully
[[18:02:50]] [SUCCESS] Screenshot refreshed successfully
[[18:02:50]] [SUCCESS] Screenshot refreshed
[[18:02:50]] [INFO] Refreshing screenshot...
[[18:02:50]] [INFO] 7WYExJTqjp=pass
[[18:02:46]] [INFO] 7WYExJTqjp=running
[[18:02:46]] [INFO] Executing action 184/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[18:02:46]] [SUCCESS] Screenshot refreshed successfully
[[18:02:46]] [SUCCESS] Screenshot refreshed successfully
[[18:02:46]] [SUCCESS] Screenshot refreshed
[[18:02:46]] [INFO] Refreshing screenshot...
[[18:02:46]] [INFO] 4WfPFN961S=pass
[[18:02:39]] [INFO] 4WfPFN961S=running
[[18:02:39]] [INFO] Executing action 183/643: Swipe from (50%, 70%) to (50%, 30%)
[[18:02:39]] [SUCCESS] Screenshot refreshed successfully
[[18:02:39]] [SUCCESS] Screenshot refreshed successfully
[[18:02:39]] [SUCCESS] Screenshot refreshed
[[18:02:39]] [INFO] Refreshing screenshot...
[[18:02:39]] [INFO] NurQsFoMkE=pass
[[18:02:35]] [INFO] NurQsFoMkE=running
[[18:02:35]] [INFO] Executing action 182/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[18:02:35]] [SUCCESS] Screenshot refreshed successfully
[[18:02:35]] [SUCCESS] Screenshot refreshed successfully
[[18:02:34]] [SUCCESS] Screenshot refreshed
[[18:02:34]] [INFO] Refreshing screenshot...
[[18:02:34]] [INFO] CkfAScJNq8=pass
[[18:02:31]] [INFO] CkfAScJNq8=running
[[18:02:31]] [INFO] Executing action 181/643: Tap on image: env[closebtnimage]
[[18:02:30]] [SUCCESS] Screenshot refreshed successfully
[[18:02:30]] [SUCCESS] Screenshot refreshed successfully
[[18:02:30]] [SUCCESS] Screenshot refreshed
[[18:02:30]] [INFO] Refreshing screenshot...
[[18:02:30]] [INFO] 1NWfFsDiTQ=pass
[[18:02:15]] [INFO] 1NWfFsDiTQ=running
[[18:02:15]] [INFO] Executing action 180/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[18:02:15]] [SUCCESS] Screenshot refreshed successfully
[[18:02:15]] [SUCCESS] Screenshot refreshed successfully
[[18:02:15]] [SUCCESS] Screenshot refreshed
[[18:02:15]] [INFO] Refreshing screenshot...
[[18:02:15]] [INFO] tufIibCj03=pass
[[18:02:11]] [INFO] tufIibCj03=running
[[18:02:11]] [INFO] Executing action 179/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[18:02:11]] [SUCCESS] Screenshot refreshed successfully
[[18:02:11]] [SUCCESS] Screenshot refreshed successfully
[[18:02:10]] [SUCCESS] Screenshot refreshed
[[18:02:10]] [INFO] Refreshing screenshot...
[[18:02:10]] [INFO] uNbKV4slh0=pass
[[18:01:59]] [SUCCESS] Screenshot refreshed successfully
[[18:01:59]] [SUCCESS] Screenshot refreshed successfully
[[18:01:58]] [INFO] uNbKV4slh0=running
[[18:01:58]] [INFO] Executing action 178/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:01:58]] [SUCCESS] Screenshot refreshed
[[18:01:58]] [INFO] Refreshing screenshot...
[[18:01:58]] [SUCCESS] Screenshot refreshed successfully
[[18:01:58]] [SUCCESS] Screenshot refreshed successfully
[[18:01:58]] [SUCCESS] Screenshot refreshed
[[18:01:58]] [INFO] Refreshing screenshot...
[[18:01:54]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[18:01:53]] [SUCCESS] Screenshot refreshed successfully
[[18:01:53]] [SUCCESS] Screenshot refreshed successfully
[[18:01:53]] [SUCCESS] Screenshot refreshed
[[18:01:53]] [INFO] Refreshing screenshot...
[[18:01:49]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[18:01:49]] [SUCCESS] Screenshot refreshed successfully
[[18:01:49]] [SUCCESS] Screenshot refreshed successfully
[[18:01:49]] [SUCCESS] Screenshot refreshed
[[18:01:49]] [INFO] Refreshing screenshot...
[[18:01:44]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[18:01:44]] [SUCCESS] Screenshot refreshed successfully
[[18:01:44]] [SUCCESS] Screenshot refreshed successfully
[[18:01:44]] [SUCCESS] Screenshot refreshed
[[18:01:44]] [INFO] Refreshing screenshot...
[[18:01:40]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[18:01:39]] [SUCCESS] Screenshot refreshed successfully
[[18:01:39]] [SUCCESS] Screenshot refreshed successfully
[[18:01:39]] [SUCCESS] Screenshot refreshed
[[18:01:39]] [INFO] Refreshing screenshot...
[[18:01:34]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[18:01:34]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[18:01:34]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[18:01:34]] [INFO] L5CIZqzpQK=running
[[18:01:34]] [INFO] Executing action 177/643: Execute Test Case: Kmart-Signin (5 steps)
[[18:01:34]] [SUCCESS] Screenshot refreshed successfully
[[18:01:34]] [SUCCESS] Screenshot refreshed successfully
[[18:01:33]] [SUCCESS] Screenshot refreshed
[[18:01:33]] [INFO] Refreshing screenshot...
[[18:01:33]] [INFO] q9ZiyYoE5B=pass
[[18:01:31]] [INFO] q9ZiyYoE5B=running
[[18:01:31]] [INFO] Executing action 176/643: iOS Function: alert_accept
[[18:01:31]] [SUCCESS] Screenshot refreshed successfully
[[18:01:31]] [SUCCESS] Screenshot refreshed successfully
[[18:01:30]] [SUCCESS] Screenshot refreshed
[[18:01:30]] [INFO] Refreshing screenshot...
[[18:01:30]] [INFO] STEdg5jOU8=pass
[[18:01:26]] [INFO] STEdg5jOU8=running
[[18:01:26]] [INFO] Executing action 175/643: Tap on Text: "in"
[[18:01:26]] [SUCCESS] Screenshot refreshed successfully
[[18:01:26]] [SUCCESS] Screenshot refreshed successfully
[[18:01:26]] [SUCCESS] Screenshot refreshed
[[18:01:26]] [INFO] Refreshing screenshot...
[[18:01:26]] [INFO] LDH2hlTZT9=pass
[[18:01:20]] [INFO] LDH2hlTZT9=running
[[18:01:20]] [INFO] Executing action 174/643: Wait for 5 ms
[[18:01:19]] [SUCCESS] Screenshot refreshed successfully
[[18:01:19]] [SUCCESS] Screenshot refreshed successfully
[[18:01:19]] [SUCCESS] Screenshot refreshed
[[18:01:19]] [INFO] Refreshing screenshot...
[[18:01:19]] [INFO] 5Dk9h5bQWl=pass
[[18:01:13]] [INFO] 5Dk9h5bQWl=running
[[18:01:13]] [INFO] Executing action 173/643: Tap on element with accessibility_id: Continue to details
[[18:01:13]] [SUCCESS] Screenshot refreshed successfully
[[18:01:13]] [SUCCESS] Screenshot refreshed successfully
[[18:01:13]] [SUCCESS] Screenshot refreshed
[[18:01:13]] [INFO] Refreshing screenshot...
[[18:01:13]] [INFO] VMzFZ2uTwl=pass
[[18:00:59]] [SUCCESS] Screenshot refreshed successfully
[[18:00:59]] [SUCCESS] Screenshot refreshed successfully
[[18:00:59]] [INFO] VMzFZ2uTwl=running
[[18:00:59]] [INFO] Executing action 172/643: Swipe up till element accessibilityid: "Continue to details" is visible
[[18:00:59]] [SUCCESS] Screenshot refreshed
[[18:00:59]] [INFO] Refreshing screenshot...
[[18:00:58]] [SUCCESS] Screenshot refreshed successfully
[[18:00:58]] [SUCCESS] Screenshot refreshed successfully
[[18:00:58]] [SUCCESS] Screenshot refreshed
[[18:00:58]] [INFO] Refreshing screenshot...
[[18:00:55]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[18:00:55]] [SUCCESS] Screenshot refreshed successfully
[[18:00:55]] [SUCCESS] Screenshot refreshed successfully
[[18:00:54]] [SUCCESS] Screenshot refreshed
[[18:00:54]] [INFO] Refreshing screenshot...
[[18:00:43]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[18:00:42]] [SUCCESS] Screenshot refreshed successfully
[[18:00:42]] [SUCCESS] Screenshot refreshed successfully
[[18:00:42]] [SUCCESS] Screenshot refreshed
[[18:00:42]] [INFO] Refreshing screenshot...
[[18:00:36]] [INFO] Executing Multi Step action step 6/8: Wait for 5 ms
[[18:00:36]] [SUCCESS] Screenshot refreshed successfully
[[18:00:36]] [SUCCESS] Screenshot refreshed successfully
[[18:00:35]] [SUCCESS] Screenshot refreshed
[[18:00:35]] [INFO] Refreshing screenshot...
[[18:00:32]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[18:00:31]] [SUCCESS] Screenshot refreshed successfully
[[18:00:31]] [SUCCESS] Screenshot refreshed successfully
[[18:00:31]] [SUCCESS] Screenshot refreshed
[[18:00:31]] [INFO] Refreshing screenshot...
[[18:00:27]] [INFO] Executing Multi Step action step 4/8: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[18:00:27]] [SUCCESS] Screenshot refreshed successfully
[[18:00:27]] [SUCCESS] Screenshot refreshed successfully
[[18:00:27]] [SUCCESS] Screenshot refreshed
[[18:00:27]] [INFO] Refreshing screenshot...
[[18:00:23]] [INFO] Executing Multi Step action step 3/8: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[18:00:23]] [SUCCESS] Screenshot refreshed successfully
[[18:00:23]] [SUCCESS] Screenshot refreshed successfully
[[18:00:23]] [SUCCESS] Screenshot refreshed
[[18:00:23]] [INFO] Refreshing screenshot...
[[18:00:19]] [INFO] Executing Multi Step action step 2/8: iOS Function: text - Text: "Notebook"
[[18:00:19]] [SUCCESS] Screenshot refreshed successfully
[[18:00:19]] [SUCCESS] Screenshot refreshed successfully
[[18:00:19]] [SUCCESS] Screenshot refreshed
[[18:00:19]] [INFO] Refreshing screenshot...
[[18:00:12]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[18:00:12]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)
[[18:00:12]] [INFO] Loading steps for multiStep action: Search and Add (Notebooks)
[[18:00:12]] [INFO] 1XUKKmBanM=running
[[18:00:12]] [INFO] Executing action 171/643: Execute Test Case: Search and Add (Notebooks) (8 steps)
[[18:00:12]] [SUCCESS] Screenshot refreshed successfully
[[18:00:12]] [SUCCESS] Screenshot refreshed successfully
[[18:00:11]] [SUCCESS] Screenshot refreshed
[[18:00:11]] [INFO] Refreshing screenshot...
[[18:00:11]] [INFO] NurQsFoMkE=pass
[[18:00:07]] [INFO] NurQsFoMkE=running
[[18:00:07]] [INFO] Executing action 170/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[18:00:07]] [SUCCESS] Screenshot refreshed successfully
[[18:00:07]] [SUCCESS] Screenshot refreshed successfully
[[18:00:07]] [SUCCESS] Screenshot refreshed
[[18:00:07]] [INFO] Refreshing screenshot...
[[18:00:07]] [INFO] 7QpmNS6hif=pass
[[18:00:02]] [INFO] 7QpmNS6hif=running
[[18:00:02]] [INFO] Executing action 169/643: Restart app: env[appid]
[[18:00:02]] [SUCCESS] Screenshot refreshed successfully
[[18:00:02]] [SUCCESS] Screenshot refreshed successfully
[[18:00:02]] [SUCCESS] Screenshot refreshed
[[18:00:02]] [INFO] Refreshing screenshot...
[[18:00:02]] [INFO] 7WYExJTqjp=pass
[[17:59:58]] [INFO] 7WYExJTqjp=running
[[17:59:58]] [INFO] Executing action 168/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:59:57]] [SUCCESS] Screenshot refreshed successfully
[[17:59:57]] [SUCCESS] Screenshot refreshed successfully
[[17:59:57]] [SUCCESS] Screenshot refreshed
[[17:59:57]] [INFO] Refreshing screenshot...
[[17:59:57]] [INFO] 4WfPFN961S=pass
[[17:59:51]] [INFO] 4WfPFN961S=running
[[17:59:51]] [INFO] Executing action 167/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:59:50]] [SUCCESS] Screenshot refreshed successfully
[[17:59:50]] [SUCCESS] Screenshot refreshed successfully
[[17:59:50]] [SUCCESS] Screenshot refreshed
[[17:59:50]] [INFO] Refreshing screenshot...
[[17:59:50]] [INFO] NurQsFoMkE=pass
[[17:59:46]] [INFO] NurQsFoMkE=running
[[17:59:46]] [INFO] Executing action 166/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:59:46]] [SUCCESS] Screenshot refreshed successfully
[[17:59:46]] [SUCCESS] Screenshot refreshed successfully
[[17:59:45]] [SUCCESS] Screenshot refreshed
[[17:59:45]] [INFO] Refreshing screenshot...
[[17:59:45]] [INFO] CkfAScJNq8=pass
[[17:59:42]] [INFO] CkfAScJNq8=running
[[17:59:42]] [INFO] Executing action 165/643: Tap on image: env[closebtnimage]
[[17:59:42]] [SUCCESS] Screenshot refreshed successfully
[[17:59:42]] [SUCCESS] Screenshot refreshed successfully
[[17:59:41]] [SUCCESS] Screenshot refreshed
[[17:59:41]] [INFO] Refreshing screenshot...
[[17:59:41]] [INFO] 1NWfFsDiTQ=pass
[[17:59:26]] [INFO] 1NWfFsDiTQ=running
[[17:59:26]] [INFO] Executing action 164/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:59:26]] [SUCCESS] Screenshot refreshed successfully
[[17:59:26]] [SUCCESS] Screenshot refreshed successfully
[[17:59:26]] [SUCCESS] Screenshot refreshed
[[17:59:26]] [INFO] Refreshing screenshot...
[[17:59:26]] [INFO] tufIibCj03=pass
[[17:59:22]] [INFO] tufIibCj03=running
[[17:59:22]] [INFO] Executing action 163/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[17:59:22]] [SUCCESS] Screenshot refreshed successfully
[[17:59:22]] [SUCCESS] Screenshot refreshed successfully
[[17:59:21]] [SUCCESS] Screenshot refreshed
[[17:59:21]] [INFO] Refreshing screenshot...
[[17:59:21]] [INFO] g8u66qfKkX=pass
[[17:59:18]] [INFO] g8u66qfKkX=running
[[17:59:18]] [INFO] Executing action 162/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[17:59:18]] [SUCCESS] Screenshot refreshed successfully
[[17:59:18]] [SUCCESS] Screenshot refreshed successfully
[[17:59:18]] [SUCCESS] Screenshot refreshed
[[17:59:18]] [INFO] Refreshing screenshot...
[[17:59:18]] [INFO] mg4S62Rdtq=pass
[[17:59:06]] [INFO] mg4S62Rdtq=running
[[17:59:06]] [INFO] Executing action 161/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:59:05]] [SUCCESS] Screenshot refreshed successfully
[[17:59:05]] [SUCCESS] Screenshot refreshed successfully
[[17:59:05]] [SUCCESS] Screenshot refreshed
[[17:59:05]] [INFO] Refreshing screenshot...
[[17:59:05]] [INFO] pCPTAtSZbf=pass
[[17:59:01]] [INFO] pCPTAtSZbf=running
[[17:59:01]] [INFO] Executing action 160/643: iOS Function: text - Text: "Wonderbaby@5"
[[17:59:01]] [SUCCESS] Screenshot refreshed successfully
[[17:59:01]] [SUCCESS] Screenshot refreshed successfully
[[17:59:01]] [SUCCESS] Screenshot refreshed
[[17:59:01]] [INFO] Refreshing screenshot...
[[17:59:01]] [INFO] DaVBARRwft=pass
[[17:58:57]] [INFO] DaVBARRwft=running
[[17:58:57]] [INFO] Executing action 159/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[17:58:56]] [SUCCESS] Screenshot refreshed successfully
[[17:58:56]] [SUCCESS] Screenshot refreshed successfully
[[17:58:56]] [SUCCESS] Screenshot refreshed
[[17:58:56]] [INFO] Refreshing screenshot...
[[17:58:56]] [INFO] e1RoZWCZJb=pass
[[17:58:52]] [INFO] e1RoZWCZJb=running
[[17:58:52]] [INFO] Executing action 158/643: iOS Function: text - Text: "<EMAIL>"
[[17:58:51]] [SUCCESS] Screenshot refreshed successfully
[[17:58:51]] [SUCCESS] Screenshot refreshed successfully
[[17:58:51]] [SUCCESS] Screenshot refreshed
[[17:58:51]] [INFO] Refreshing screenshot...
[[17:58:51]] [INFO] 50Z2jrodNd=pass
[[17:58:47]] [INFO] 50Z2jrodNd=running
[[17:58:47]] [INFO] Executing action 157/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:58:47]] [SUCCESS] Screenshot refreshed successfully
[[17:58:47]] [SUCCESS] Screenshot refreshed successfully
[[17:58:47]] [SUCCESS] Screenshot refreshed
[[17:58:47]] [INFO] Refreshing screenshot...
[[17:58:47]] [INFO] q9ZiyYoE5B=pass
[[17:58:44]] [INFO] q9ZiyYoE5B=running
[[17:58:44]] [INFO] Executing action 156/643: iOS Function: alert_accept
[[17:58:44]] [SUCCESS] Screenshot refreshed successfully
[[17:58:44]] [SUCCESS] Screenshot refreshed successfully
[[17:58:44]] [SUCCESS] Screenshot refreshed
[[17:58:44]] [INFO] Refreshing screenshot...
[[17:58:44]] [INFO] 6PL8P3rT57=pass
[[17:58:40]] [INFO] 6PL8P3rT57=running
[[17:58:40]] [INFO] Executing action 155/643: Tap on Text: "Sign"
[[17:58:39]] [SUCCESS] Screenshot refreshed successfully
[[17:58:39]] [SUCCESS] Screenshot refreshed successfully
[[17:58:39]] [SUCCESS] Screenshot refreshed
[[17:58:39]] [INFO] Refreshing screenshot...
[[17:58:39]] [INFO] 2YGctqXNED=pass
[[17:58:33]] [INFO] 2YGctqXNED=running
[[17:58:33]] [INFO] Executing action 154/643: Tap on element with accessibility_id: Continue to details
[[17:58:33]] [SUCCESS] Screenshot refreshed successfully
[[17:58:33]] [SUCCESS] Screenshot refreshed successfully
[[17:58:33]] [SUCCESS] Screenshot refreshed
[[17:58:33]] [INFO] Refreshing screenshot...
[[17:58:33]] [INFO] 2YGctqXNED=pass
[[17:58:25]] [INFO] 2YGctqXNED=running
[[17:58:25]] [INFO] Executing action 153/643: Swipe up till element accessibilityid: "Continue to details" is visible
[[17:58:24]] [SUCCESS] Screenshot refreshed successfully
[[17:58:24]] [SUCCESS] Screenshot refreshed successfully
[[17:58:24]] [SUCCESS] Screenshot refreshed
[[17:58:24]] [INFO] Refreshing screenshot...
[[17:58:24]] [INFO] tufIibCj03=pass
[[17:58:20]] [INFO] tufIibCj03=running
[[17:58:20]] [INFO] Executing action 152/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[17:58:20]] [SUCCESS] Screenshot refreshed successfully
[[17:58:20]] [SUCCESS] Screenshot refreshed successfully
[[17:58:20]] [SUCCESS] Screenshot refreshed
[[17:58:20]] [INFO] Refreshing screenshot...
[[17:58:20]] [INFO] g8u66qfKkX=pass
[[17:58:17]] [INFO] g8u66qfKkX=running
[[17:58:17]] [INFO] Executing action 151/643: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[17:58:16]] [SUCCESS] Screenshot refreshed successfully
[[17:58:16]] [SUCCESS] Screenshot refreshed successfully
[[17:58:16]] [SUCCESS] Screenshot refreshed
[[17:58:16]] [INFO] Refreshing screenshot...
[[17:58:16]] [INFO] ZBXuV4sJUR=pass
[[17:58:04]] [INFO] ZBXuV4sJUR=running
[[17:58:04]] [INFO] Executing action 150/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:58:04]] [SUCCESS] Screenshot refreshed successfully
[[17:58:04]] [SUCCESS] Screenshot refreshed successfully
[[17:58:04]] [SUCCESS] Screenshot refreshed
[[17:58:04]] [INFO] Refreshing screenshot...
[[17:58:04]] [INFO] XryN8qR1DX=pass
[[17:58:00]] [INFO] XryN8qR1DX=running
[[17:58:00]] [INFO] Executing action 149/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:58:00]] [SUCCESS] Screenshot refreshed successfully
[[17:58:00]] [SUCCESS] Screenshot refreshed successfully
[[17:57:59]] [SUCCESS] Screenshot refreshed
[[17:57:59]] [INFO] Refreshing screenshot...
[[17:57:59]] [INFO] XcWXIMtv1E=pass
[[17:57:53]] [INFO] XcWXIMtv1E=running
[[17:57:53]] [INFO] Executing action 148/643: Wait for 5 ms
[[17:57:53]] [SUCCESS] Screenshot refreshed successfully
[[17:57:53]] [SUCCESS] Screenshot refreshed successfully
[[17:57:52]] [SUCCESS] Screenshot refreshed
[[17:57:52]] [INFO] Refreshing screenshot...
[[17:57:52]] [INFO] S1cQQxksEj=pass
[[17:57:46]] [INFO] S1cQQxksEj=running
[[17:57:46]] [INFO] Executing action 147/643: Tap on element with accessibility_id: Add to bag
[[17:57:45]] [SUCCESS] Screenshot refreshed successfully
[[17:57:45]] [SUCCESS] Screenshot refreshed successfully
[[17:57:45]] [SUCCESS] Screenshot refreshed
[[17:57:45]] [INFO] Refreshing screenshot...
[[17:57:45]] [INFO] K2w9XUGwnb=pass
[[17:57:37]] [INFO] K2w9XUGwnb=running
[[17:57:37]] [INFO] Executing action 146/643: Swipe up till element accessibility_id: "Add to bag" is visible
[[17:57:37]] [SUCCESS] Screenshot refreshed successfully
[[17:57:37]] [SUCCESS] Screenshot refreshed successfully
[[17:57:36]] [SUCCESS] Screenshot refreshed
[[17:57:36]] [INFO] Refreshing screenshot...
[[17:57:36]] [INFO] BTYxjEaZEk=pass
[[17:57:33]] [INFO] BTYxjEaZEk=running
[[17:57:33]] [INFO] Executing action 145/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:57:32]] [SUCCESS] Screenshot refreshed successfully
[[17:57:32]] [SUCCESS] Screenshot refreshed successfully
[[17:57:32]] [SUCCESS] Screenshot refreshed
[[17:57:32]] [INFO] Refreshing screenshot...
[[17:57:32]] [INFO] YC6bBrKQgq=pass
[[17:57:29]] [INFO] YC6bBrKQgq=running
[[17:57:29]] [INFO] Executing action 144/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:57:28]] [SUCCESS] Screenshot refreshed successfully
[[17:57:28]] [SUCCESS] Screenshot refreshed successfully
[[17:57:28]] [SUCCESS] Screenshot refreshed
[[17:57:28]] [INFO] Refreshing screenshot...
[[17:57:28]] [INFO] aRgHcQcLDP=pass
[[17:57:24]] [INFO] aRgHcQcLDP=running
[[17:57:24]] [INFO] Executing action 143/643: iOS Function: text - Text: "uno card"
[[17:57:24]] [SUCCESS] Screenshot refreshed successfully
[[17:57:24]] [SUCCESS] Screenshot refreshed successfully
[[17:57:24]] [SUCCESS] Screenshot refreshed
[[17:57:24]] [INFO] Refreshing screenshot...
[[17:57:24]] [INFO] 4PZC1vVWJW=pass
[[17:57:19]] [INFO] 4PZC1vVWJW=running
[[17:57:19]] [INFO] Executing action 142/643: Tap on Text: "Find"
[[17:57:19]] [SUCCESS] Screenshot refreshed successfully
[[17:57:19]] [SUCCESS] Screenshot refreshed successfully
[[17:57:19]] [SUCCESS] Screenshot refreshed
[[17:57:19]] [INFO] Refreshing screenshot...
[[17:57:19]] [INFO] XryN8qR1DX=pass
[[17:57:15]] [INFO] XryN8qR1DX=running
[[17:57:15]] [INFO] Executing action 141/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[17:57:14]] [SUCCESS] Screenshot refreshed successfully
[[17:57:14]] [SUCCESS] Screenshot refreshed successfully
[[17:57:14]] [SUCCESS] Screenshot refreshed
[[17:57:14]] [INFO] Refreshing screenshot...
[[17:57:14]] [INFO] 7WYExJTqjp=pass
[[17:57:10]] [INFO] 7WYExJTqjp=running
[[17:57:10]] [INFO] Executing action 140/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:57:10]] [SUCCESS] Screenshot refreshed successfully
[[17:57:10]] [SUCCESS] Screenshot refreshed successfully
[[17:57:10]] [SUCCESS] Screenshot refreshed
[[17:57:10]] [INFO] Refreshing screenshot...
[[17:57:10]] [INFO] 4WfPFN961S=pass
[[17:57:03]] [INFO] 4WfPFN961S=running
[[17:57:03]] [INFO] Executing action 139/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:57:03]] [SUCCESS] Screenshot refreshed successfully
[[17:57:03]] [SUCCESS] Screenshot refreshed successfully
[[17:57:03]] [SUCCESS] Screenshot refreshed
[[17:57:03]] [INFO] Refreshing screenshot...
[[17:57:03]] [INFO] NurQsFoMkE=pass
[[17:56:58]] [SUCCESS] Screenshot refreshed successfully
[[17:56:58]] [SUCCESS] Screenshot refreshed successfully
[[17:56:58]] [INFO] NurQsFoMkE=running
[[17:56:58]] [INFO] Executing action 138/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:56:58]] [SUCCESS] Screenshot refreshed
[[17:56:58]] [INFO] Refreshing screenshot...
[[17:56:57]] [SUCCESS] Screenshot refreshed successfully
[[17:56:57]] [SUCCESS] Screenshot refreshed successfully
[[17:56:57]] [SUCCESS] Screenshot refreshed
[[17:56:57]] [INFO] Refreshing screenshot...
[[17:56:53]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[17:56:53]] [SUCCESS] Screenshot refreshed successfully
[[17:56:53]] [SUCCESS] Screenshot refreshed successfully
[[17:56:52]] [SUCCESS] Screenshot refreshed
[[17:56:52]] [INFO] Refreshing screenshot...
[[17:56:49]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:56:48]] [SUCCESS] Screenshot refreshed successfully
[[17:56:48]] [SUCCESS] Screenshot refreshed successfully
[[17:56:48]] [SUCCESS] Screenshot refreshed
[[17:56:48]] [INFO] Refreshing screenshot...
[[17:56:44]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[17:56:43]] [SUCCESS] Screenshot refreshed successfully
[[17:56:43]] [SUCCESS] Screenshot refreshed successfully
[[17:56:43]] [SUCCESS] Screenshot refreshed
[[17:56:43]] [INFO] Refreshing screenshot...
[[17:56:39]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:56:39]] [SUCCESS] Screenshot refreshed successfully
[[17:56:39]] [SUCCESS] Screenshot refreshed successfully
[[17:56:39]] [SUCCESS] Screenshot refreshed
[[17:56:39]] [INFO] Refreshing screenshot...
[[17:56:33]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:56:33]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[17:56:33]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[17:56:33]] [INFO] APqAlKbucp=running
[[17:56:33]] [INFO] Executing action 137/643: Execute Test Case: Kmart-Signin (5 steps)
[[17:56:33]] [SUCCESS] Screenshot refreshed successfully
[[17:56:33]] [SUCCESS] Screenshot refreshed successfully
[[17:56:33]] [SUCCESS] Screenshot refreshed
[[17:56:33]] [INFO] Refreshing screenshot...
[[17:56:33]] [INFO] byEe7qbCpq=pass
[[17:56:30]] [INFO] byEe7qbCpq=running
[[17:56:30]] [INFO] Executing action 136/643: iOS Function: alert_accept
[[17:56:30]] [SUCCESS] Screenshot refreshed successfully
[[17:56:30]] [SUCCESS] Screenshot refreshed successfully
[[17:56:30]] [SUCCESS] Screenshot refreshed
[[17:56:30]] [INFO] Refreshing screenshot...
[[17:56:30]] [INFO] L6wTorOX8B=pass
[[17:56:26]] [INFO] L6wTorOX8B=running
[[17:56:26]] [INFO] Executing action 135/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[17:56:26]] [SUCCESS] Screenshot refreshed successfully
[[17:56:26]] [SUCCESS] Screenshot refreshed successfully
[[17:56:26]] [SUCCESS] Screenshot refreshed
[[17:56:26]] [INFO] Refreshing screenshot...
[[17:56:26]] [INFO] XryN8qR1DX=pass
[[17:56:22]] [INFO] XryN8qR1DX=running
[[17:56:22]] [INFO] Executing action 134/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:56:21]] [SUCCESS] Screenshot refreshed successfully
[[17:56:21]] [SUCCESS] Screenshot refreshed successfully
[[17:56:21]] [SUCCESS] Screenshot refreshed
[[17:56:21]] [INFO] Refreshing screenshot...
[[17:56:21]] [INFO] lCSewtjn1z=pass
[[17:56:17]] [INFO] lCSewtjn1z=running
[[17:56:17]] [INFO] Executing action 133/643: Restart app: env[appid]
[[17:56:16]] [SUCCESS] Screenshot refreshed successfully
[[17:56:16]] [SUCCESS] Screenshot refreshed successfully
[[17:56:16]] [SUCCESS] Screenshot refreshed
[[17:56:16]] [INFO] Refreshing screenshot...
[[17:56:16]] [INFO] IJh702cxG0=pass
[[17:56:13]] [INFO] IJh702cxG0=running
[[17:56:13]] [INFO] Executing action 132/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:56:12]] [SUCCESS] Screenshot refreshed successfully
[[17:56:12]] [SUCCESS] Screenshot refreshed successfully
[[17:56:12]] [SUCCESS] Screenshot refreshed
[[17:56:12]] [INFO] Refreshing screenshot...
[[17:56:12]] [INFO] 4WfPFN961S=pass
[[17:56:05]] [INFO] 4WfPFN961S=running
[[17:56:05]] [INFO] Executing action 131/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:56:05]] [SUCCESS] Screenshot refreshed successfully
[[17:56:05]] [SUCCESS] Screenshot refreshed successfully
[[17:56:05]] [SUCCESS] Screenshot refreshed
[[17:56:05]] [INFO] Refreshing screenshot...
[[17:56:05]] [INFO] AOcOOSuOsB=pass
[[17:56:01]] [INFO] AOcOOSuOsB=running
[[17:56:01]] [INFO] Executing action 130/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:56:01]] [SUCCESS] Screenshot refreshed successfully
[[17:56:01]] [SUCCESS] Screenshot refreshed successfully
[[17:56:00]] [SUCCESS] Screenshot refreshed
[[17:56:00]] [INFO] Refreshing screenshot...
[[17:56:00]] [INFO] AOcOOSuOsB=pass
[[17:55:53]] [INFO] AOcOOSuOsB=running
[[17:55:53]] [INFO] Executing action 129/643: Wait till xpath=//XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:55:53]] [SUCCESS] Screenshot refreshed successfully
[[17:55:53]] [SUCCESS] Screenshot refreshed successfully
[[17:55:53]] [SUCCESS] Screenshot refreshed
[[17:55:53]] [INFO] Refreshing screenshot...
[[17:55:53]] [INFO] N2yjynioko=pass
[[17:55:49]] [INFO] N2yjynioko=running
[[17:55:49]] [INFO] Executing action 128/643: iOS Function: text - Text: "Wonderbaby@5"
[[17:55:48]] [SUCCESS] Screenshot refreshed successfully
[[17:55:48]] [SUCCESS] Screenshot refreshed successfully
[[17:55:48]] [SUCCESS] Screenshot refreshed
[[17:55:48]] [INFO] Refreshing screenshot...
[[17:55:48]] [INFO] SHaIduBnay=pass
[[17:55:44]] [INFO] SHaIduBnay=running
[[17:55:44]] [INFO] Executing action 127/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[contains(@name,"Password")]
[[17:55:44]] [SUCCESS] Screenshot refreshed successfully
[[17:55:44]] [SUCCESS] Screenshot refreshed successfully
[[17:55:44]] [SUCCESS] Screenshot refreshed
[[17:55:44]] [INFO] Refreshing screenshot...
[[17:55:44]] [INFO] 3XIsWUF1Nj=pass
[[17:55:40]] [INFO] 3XIsWUF1Nj=running
[[17:55:40]] [INFO] Executing action 126/643: Tap on image: captha-chkbox-op-ios.png
[[17:55:40]] [SUCCESS] Screenshot refreshed successfully
[[17:55:40]] [SUCCESS] Screenshot refreshed successfully
[[17:55:39]] [SUCCESS] Screenshot refreshed
[[17:55:39]] [INFO] Refreshing screenshot...
[[17:55:39]] [INFO] wuIMlAwYVA=pass
[[17:55:35]] [INFO] wuIMlAwYVA=running
[[17:55:35]] [INFO] Executing action 125/643: iOS Function: text - Text: "env[uname1]"
[[17:55:35]] [SUCCESS] Screenshot refreshed successfully
[[17:55:35]] [SUCCESS] Screenshot refreshed successfully
[[17:55:34]] [SUCCESS] Screenshot refreshed
[[17:55:34]] [INFO] Refreshing screenshot...
[[17:55:34]] [INFO] 50Z2jrodNd=pass
[[17:55:30]] [INFO] 50Z2jrodNd=running
[[17:55:30]] [INFO] Executing action 124/643: Tap on element with xpath: //XCUIElementTypeTextField[contains(@name,"Email")]
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:30]] [SUCCESS] Screenshot refreshed successfully
[[17:55:30]] [SUCCESS] Screenshot refreshed
[[17:55:30]] [INFO] Refreshing screenshot...
[[17:55:30]] [INFO] VK2oI6mXSB=pass
[[17:55:25]] [INFO] VK2oI6mXSB=running
[[17:55:25]] [INFO] Executing action 123/643: Wait till xpath=//XCUIElementTypeTextField[contains(@name,"Email")]
[[17:55:24]] [SUCCESS] Screenshot refreshed successfully
[[17:55:24]] [SUCCESS] Screenshot refreshed successfully
[[17:55:24]] [SUCCESS] Screenshot refreshed
[[17:55:24]] [INFO] Refreshing screenshot...
[[17:55:24]] [INFO] q9ZiyYoE5B=pass
[[17:55:22]] [INFO] q9ZiyYoE5B=running
[[17:55:22]] [INFO] Executing action 122/643: iOS Function: alert_accept
[[17:55:22]] [SUCCESS] Screenshot refreshed successfully
[[17:55:22]] [SUCCESS] Screenshot refreshed successfully
[[17:55:21]] [SUCCESS] Screenshot refreshed
[[17:55:21]] [INFO] Refreshing screenshot...
[[17:55:21]] [INFO] 4PZC1vVWJW=pass
[[17:55:17]] [INFO] 4PZC1vVWJW=running
[[17:55:17]] [INFO] Executing action 121/643: Tap on Text: "Sign"
[[17:55:17]] [SUCCESS] Screenshot refreshed successfully
[[17:55:17]] [SUCCESS] Screenshot refreshed successfully
[[17:55:16]] [SUCCESS] Screenshot refreshed
[[17:55:16]] [INFO] Refreshing screenshot...
[[17:55:16]] [INFO] mcscWdhpn2=pass
[[17:55:00]] [INFO] mcscWdhpn2=running
[[17:55:00]] [INFO] Executing action 120/643: Swipe up till element xpath: "//XCUIElementTypeStaticText[@name="Already a member?"]" is visible
[[17:55:00]] [SUCCESS] Screenshot refreshed successfully
[[17:55:00]] [SUCCESS] Screenshot refreshed successfully
[[17:55:00]] [SUCCESS] Screenshot refreshed
[[17:55:00]] [INFO] Refreshing screenshot...
[[17:55:00]] [INFO] 6zUBxjSFym=pass
[[17:54:56]] [INFO] 6zUBxjSFym=running
[[17:54:56]] [INFO] Executing action 119/643: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[17:54:56]] [SUCCESS] Screenshot refreshed successfully
[[17:54:56]] [SUCCESS] Screenshot refreshed successfully
[[17:54:56]] [SUCCESS] Screenshot refreshed
[[17:54:56]] [INFO] Refreshing screenshot...
[[17:54:56]] [INFO] BTYxjEaZEk=pass
[[17:54:52]] [INFO] BTYxjEaZEk=running
[[17:54:52]] [INFO] Executing action 118/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:54:51]] [SUCCESS] Screenshot refreshed successfully
[[17:54:51]] [SUCCESS] Screenshot refreshed successfully
[[17:54:51]] [SUCCESS] Screenshot refreshed
[[17:54:51]] [INFO] Refreshing screenshot...
[[17:54:51]] [INFO] YC6bBrKQgq=pass
[[17:54:48]] [INFO] YC6bBrKQgq=running
[[17:54:48]] [INFO] Executing action 117/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:54:47]] [SUCCESS] Screenshot refreshed successfully
[[17:54:47]] [SUCCESS] Screenshot refreshed successfully
[[17:54:47]] [SUCCESS] Screenshot refreshed
[[17:54:47]] [INFO] Refreshing screenshot...
[[17:54:47]] [INFO] aRgHcQcLDP=pass
[[17:54:43]] [INFO] aRgHcQcLDP=running
[[17:54:43]] [INFO] Executing action 116/643: iOS Function: text - Text: "uno card"
[[17:54:43]] [SUCCESS] Screenshot refreshed successfully
[[17:54:43]] [SUCCESS] Screenshot refreshed successfully
[[17:54:42]] [SUCCESS] Screenshot refreshed
[[17:54:42]] [INFO] Refreshing screenshot...
[[17:54:42]] [INFO] 4PZC1vVWJW=pass
[[17:54:38]] [INFO] 4PZC1vVWJW=running
[[17:54:38]] [INFO] Executing action 115/643: Tap on Text: "Find"
[[17:54:38]] [SUCCESS] Screenshot refreshed successfully
[[17:54:38]] [SUCCESS] Screenshot refreshed successfully
[[17:54:37]] [SUCCESS] Screenshot refreshed
[[17:54:37]] [INFO] Refreshing screenshot...
[[17:54:37]] [INFO] lCSewtjn1z=pass
[[17:54:33]] [INFO] lCSewtjn1z=running
[[17:54:33]] [INFO] Executing action 114/643: Restart app: env[appid]
[[17:54:32]] [SUCCESS] Screenshot refreshed successfully
[[17:54:32]] [SUCCESS] Screenshot refreshed successfully
[[17:54:32]] [SUCCESS] Screenshot refreshed
[[17:54:32]] [INFO] Refreshing screenshot...
[[17:54:32]] [INFO] A1Wz7p1iVG=pass
[[17:54:28]] [INFO] A1Wz7p1iVG=running
[[17:54:28]] [INFO] Executing action 113/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:54:28]] [SUCCESS] Screenshot refreshed successfully
[[17:54:28]] [SUCCESS] Screenshot refreshed successfully
[[17:54:28]] [SUCCESS] Screenshot refreshed
[[17:54:28]] [INFO] Refreshing screenshot...
[[17:54:28]] [INFO] ehyLmdZWP2=pass
[[17:54:21]] [INFO] ehyLmdZWP2=running
[[17:54:21]] [INFO] Executing action 112/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:54:21]] [SUCCESS] Screenshot refreshed successfully
[[17:54:21]] [SUCCESS] Screenshot refreshed successfully
[[17:54:21]] [SUCCESS] Screenshot refreshed
[[17:54:21]] [INFO] Refreshing screenshot...
[[17:54:21]] [INFO] ydRnBBO1vR=pass
[[17:54:17]] [INFO] ydRnBBO1vR=running
[[17:54:17]] [INFO] Executing action 111/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:54:16]] [SUCCESS] Screenshot refreshed successfully
[[17:54:16]] [SUCCESS] Screenshot refreshed successfully
[[17:54:16]] [SUCCESS] Screenshot refreshed
[[17:54:16]] [INFO] Refreshing screenshot...
[[17:54:16]] [INFO] quZwUwj3a8=pass
[[17:54:13]] [INFO] quZwUwj3a8=running
[[17:54:13]] [INFO] Executing action 110/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[17:54:13]] [SUCCESS] Screenshot refreshed successfully
[[17:54:13]] [SUCCESS] Screenshot refreshed successfully
[[17:54:12]] [SUCCESS] Screenshot refreshed
[[17:54:12]] [INFO] Refreshing screenshot...
[[17:54:12]] [INFO] FHRlQXe58T=pass
[[17:54:09]] [INFO] FHRlQXe58T=running
[[17:54:09]] [INFO] Executing action 109/643: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[17:54:08]] [SUCCESS] Screenshot refreshed successfully
[[17:54:08]] [SUCCESS] Screenshot refreshed successfully
[[17:54:08]] [SUCCESS] Screenshot refreshed
[[17:54:08]] [INFO] Refreshing screenshot...
[[17:54:08]] [INFO] 8uojw2klHA=pass
[[17:54:04]] [INFO] 8uojw2klHA=running
[[17:54:04]] [INFO] Executing action 108/643: iOS Function: text - Text: "env[pwd]"
[[17:54:04]] [SUCCESS] Screenshot refreshed successfully
[[17:54:04]] [SUCCESS] Screenshot refreshed successfully
[[17:54:04]] [SUCCESS] Screenshot refreshed
[[17:54:04]] [INFO] Refreshing screenshot...
[[17:54:04]] [INFO] SHaIduBnay=pass
[[17:54:00]] [INFO] SHaIduBnay=running
[[17:54:00]] [INFO] Executing action 107/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:53:59]] [SUCCESS] Screenshot refreshed successfully
[[17:53:59]] [SUCCESS] Screenshot refreshed successfully
[[17:53:59]] [SUCCESS] Screenshot refreshed
[[17:53:59]] [INFO] Refreshing screenshot...
[[17:53:59]] [INFO] TGoXyeQtB7=pass
[[17:53:55]] [INFO] TGoXyeQtB7=running
[[17:53:55]] [INFO] Executing action 106/643: iOS Function: text - Text: "env[uname]"
[[17:53:54]] [SUCCESS] Screenshot refreshed successfully
[[17:53:54]] [SUCCESS] Screenshot refreshed successfully
[[17:53:54]] [SUCCESS] Screenshot refreshed
[[17:53:54]] [INFO] Refreshing screenshot...
[[17:53:54]] [INFO] rLCI6NVxSc=pass
[[17:53:50]] [INFO] rLCI6NVxSc=running
[[17:53:50]] [INFO] Executing action 105/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:53:50]] [SUCCESS] Screenshot refreshed successfully
[[17:53:50]] [SUCCESS] Screenshot refreshed successfully
[[17:53:50]] [SUCCESS] Screenshot refreshed
[[17:53:50]] [INFO] Refreshing screenshot...
[[17:53:50]] [INFO] 6mHVWI3j5e=pass
[[17:53:46]] [INFO] 6mHVWI3j5e=running
[[17:53:46]] [INFO] Executing action 104/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:53:46]] [SUCCESS] Screenshot refreshed successfully
[[17:53:46]] [SUCCESS] Screenshot refreshed successfully
[[17:53:46]] [SUCCESS] Screenshot refreshed
[[17:53:46]] [INFO] Refreshing screenshot...
[[17:53:46]] [INFO] rJVGLpLWM3=pass
[[17:53:43]] [INFO] rJVGLpLWM3=running
[[17:53:43]] [INFO] Executing action 103/643: iOS Function: alert_accept
[[17:53:43]] [SUCCESS] Screenshot refreshed successfully
[[17:53:43]] [SUCCESS] Screenshot refreshed successfully
[[17:53:43]] [SUCCESS] Screenshot refreshed
[[17:53:43]] [INFO] Refreshing screenshot...
[[17:53:43]] [INFO] WlISsMf9QA=pass
[[17:53:39]] [INFO] WlISsMf9QA=running
[[17:53:39]] [INFO] Executing action 102/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[17:53:39]] [SUCCESS] Screenshot refreshed successfully
[[17:53:39]] [SUCCESS] Screenshot refreshed successfully
[[17:53:39]] [SUCCESS] Screenshot refreshed
[[17:53:39]] [INFO] Refreshing screenshot...
[[17:53:39]] [INFO] IvqPpScAJa=pass
[[17:53:35]] [INFO] IvqPpScAJa=running
[[17:53:35]] [INFO] Executing action 101/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[17:53:35]] [SUCCESS] Screenshot refreshed successfully
[[17:53:35]] [SUCCESS] Screenshot refreshed successfully
[[17:53:34]] [SUCCESS] Screenshot refreshed
[[17:53:34]] [INFO] Refreshing screenshot...
[[17:53:34]] [INFO] bGo3feCwBQ=pass
[[17:53:31]] [INFO] bGo3feCwBQ=running
[[17:53:31]] [INFO] Executing action 100/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:53:30]] [SUCCESS] Screenshot refreshed successfully
[[17:53:30]] [SUCCESS] Screenshot refreshed successfully
[[17:53:30]] [SUCCESS] Screenshot refreshed
[[17:53:30]] [INFO] Refreshing screenshot...
[[17:53:30]] [INFO] 4WfPFN961S=pass
[[17:53:23]] [INFO] 4WfPFN961S=running
[[17:53:23]] [INFO] Executing action 99/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:53:23]] [SUCCESS] Screenshot refreshed successfully
[[17:53:23]] [SUCCESS] Screenshot refreshed successfully
[[17:53:23]] [SUCCESS] Screenshot refreshed
[[17:53:23]] [INFO] Refreshing screenshot...
[[17:53:23]] [INFO] F0gZF1jEnT=pass
[[17:53:19]] [INFO] F0gZF1jEnT=running
[[17:53:19]] [INFO] Executing action 98/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:53:19]] [SUCCESS] Screenshot refreshed successfully
[[17:53:19]] [SUCCESS] Screenshot refreshed successfully
[[17:53:19]] [SUCCESS] Screenshot refreshed
[[17:53:19]] [INFO] Refreshing screenshot...
[[17:53:19]] [INFO] EDHl0X27Wi=pass
[[17:53:14]] [INFO] EDHl0X27Wi=running
[[17:53:14]] [INFO] Executing action 97/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[17:53:14]] [SUCCESS] Screenshot refreshed successfully
[[17:53:14]] [SUCCESS] Screenshot refreshed successfully
[[17:53:14]] [SUCCESS] Screenshot refreshed
[[17:53:14]] [INFO] Refreshing screenshot...
[[17:53:14]] [INFO] j8NXU87gV3=pass
[[17:53:09]] [INFO] j8NXU87gV3=running
[[17:53:09]] [INFO] Executing action 96/643: iOS Function: text - Text: "env[pwd]"
[[17:53:09]] [SUCCESS] Screenshot refreshed successfully
[[17:53:09]] [SUCCESS] Screenshot refreshed successfully
[[17:53:09]] [SUCCESS] Screenshot refreshed
[[17:53:09]] [INFO] Refreshing screenshot...
[[17:53:09]] [INFO] dpVaKL19uc=pass
[[17:53:05]] [INFO] dpVaKL19uc=running
[[17:53:05]] [INFO] Executing action 95/643: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:53:04]] [SUCCESS] Screenshot refreshed successfully
[[17:53:04]] [SUCCESS] Screenshot refreshed successfully
[[17:53:04]] [SUCCESS] Screenshot refreshed
[[17:53:04]] [INFO] Refreshing screenshot...
[[17:53:04]] [INFO] eOm1WExcrK=pass
[[17:53:00]] [INFO] eOm1WExcrK=running
[[17:53:00]] [INFO] Executing action 94/643: iOS Function: text - Text: "env[uname]"
[[17:52:59]] [SUCCESS] Screenshot refreshed successfully
[[17:52:59]] [SUCCESS] Screenshot refreshed successfully
[[17:52:59]] [SUCCESS] Screenshot refreshed
[[17:52:59]] [INFO] Refreshing screenshot...
[[17:52:59]] [INFO] 50Z2jrodNd=pass
[[17:52:55]] [INFO] 50Z2jrodNd=running
[[17:52:55]] [INFO] Executing action 93/643: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:52:55]] [SUCCESS] Screenshot refreshed successfully
[[17:52:55]] [SUCCESS] Screenshot refreshed successfully
[[17:52:55]] [SUCCESS] Screenshot refreshed
[[17:52:55]] [INFO] Refreshing screenshot...
[[17:52:55]] [INFO] eJnHS9n9VL=pass
[[17:52:51]] [INFO] eJnHS9n9VL=running
[[17:52:51]] [INFO] Executing action 92/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:52:51]] [SUCCESS] Screenshot refreshed successfully
[[17:52:51]] [SUCCESS] Screenshot refreshed successfully
[[17:52:51]] [SUCCESS] Screenshot refreshed
[[17:52:51]] [INFO] Refreshing screenshot...
[[17:52:51]] [INFO] XuLgjNG74w=pass
[[17:52:48]] [INFO] XuLgjNG74w=running
[[17:52:48]] [INFO] Executing action 91/643: iOS Function: alert_accept
[[17:52:48]] [SUCCESS] Screenshot refreshed successfully
[[17:52:48]] [SUCCESS] Screenshot refreshed successfully
[[17:52:48]] [SUCCESS] Screenshot refreshed
[[17:52:48]] [INFO] Refreshing screenshot...
[[17:52:48]] [INFO] qA1ap4n1m4=pass
[[17:52:41]] [INFO] qA1ap4n1m4=running
[[17:52:41]] [INFO] Executing action 90/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:52:41]] [SUCCESS] Screenshot refreshed successfully
[[17:52:41]] [SUCCESS] Screenshot refreshed successfully
[[17:52:41]] [SUCCESS] Screenshot refreshed
[[17:52:41]] [INFO] Refreshing screenshot...
[[17:52:41]] [INFO] JXFxYCr98V=pass
[[17:52:27]] [SUCCESS] Screenshot refreshed successfully
[[17:52:27]] [SUCCESS] Screenshot refreshed successfully
[[17:52:27]] [INFO] JXFxYCr98V=running
[[17:52:27]] [INFO] Executing action 89/643: Restart app: env[appid]
[[17:52:27]] [SUCCESS] Screenshot refreshed
[[17:52:27]] [INFO] Refreshing screenshot...
[[17:52:27]] [SUCCESS] Screenshot refreshed successfully
[[17:52:27]] [SUCCESS] Screenshot refreshed successfully
[[17:52:27]] [SUCCESS] Screenshot refreshed
[[17:52:27]] [INFO] Refreshing screenshot...
[[17:52:24]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[17:52:24]] [SUCCESS] Screenshot refreshed successfully
[[17:52:24]] [SUCCESS] Screenshot refreshed successfully
[[17:52:24]] [SUCCESS] Screenshot refreshed
[[17:52:24]] [INFO] Refreshing screenshot...
[[17:52:11]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[17:52:11]] [SUCCESS] Screenshot refreshed successfully
[[17:52:11]] [SUCCESS] Screenshot refreshed successfully
[[17:52:11]] [SUCCESS] Screenshot refreshed
[[17:52:11]] [INFO] Refreshing screenshot...
[[17:52:07]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[17:52:07]] [SUCCESS] Screenshot refreshed successfully
[[17:52:07]] [SUCCESS] Screenshot refreshed successfully
[[17:52:07]] [SUCCESS] Screenshot refreshed
[[17:52:07]] [INFO] Refreshing screenshot...
[[17:52:03]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:52:03]] [SUCCESS] Screenshot refreshed successfully
[[17:52:03]] [SUCCESS] Screenshot refreshed successfully
[[17:52:02]] [SUCCESS] Screenshot refreshed
[[17:52:02]] [INFO] Refreshing screenshot...
[[17:51:56]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[17:51:56]] [SUCCESS] Screenshot refreshed successfully
[[17:51:56]] [SUCCESS] Screenshot refreshed successfully
[[17:51:56]] [SUCCESS] Screenshot refreshed
[[17:51:56]] [INFO] Refreshing screenshot...
[[17:51:49]] [SUCCESS] Screenshot refreshed successfully
[[17:51:49]] [SUCCESS] Screenshot refreshed successfully
[[17:51:49]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[17:51:49]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[17:51:49]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[17:51:49]] [INFO] hbIlJIWlVN=running
[[17:51:49]] [INFO] Executing action 88/643: cleanupSteps action
[[17:51:49]] [SUCCESS] Screenshot refreshed
[[17:51:49]] [INFO] Refreshing screenshot...
[[17:51:49]] [SUCCESS] Screenshot refreshed successfully
[[17:51:49]] [SUCCESS] Screenshot refreshed successfully
[[17:51:48]] [SUCCESS] Screenshot refreshed
[[17:51:48]] [INFO] Refreshing screenshot...
[[17:51:45]] [INFO] Executing Multi Step action step 36/36: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[17:51:44]] [SUCCESS] Screenshot refreshed successfully
[[17:51:44]] [SUCCESS] Screenshot refreshed successfully
[[17:51:44]] [SUCCESS] Screenshot refreshed
[[17:51:44]] [INFO] Refreshing screenshot...
[[17:51:41]] [INFO] Executing Multi Step action step 35/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:51:40]] [SUCCESS] Screenshot refreshed successfully
[[17:51:40]] [SUCCESS] Screenshot refreshed successfully
[[17:51:40]] [SUCCESS] Screenshot refreshed
[[17:51:40]] [INFO] Refreshing screenshot...
[[17:51:28]] [INFO] Executing Multi Step action step 34/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:51:28]] [SUCCESS] Screenshot refreshed successfully
[[17:51:28]] [SUCCESS] Screenshot refreshed successfully
[[17:51:28]] [SUCCESS] Screenshot refreshed
[[17:51:28]] [INFO] Refreshing screenshot...
[[17:51:24]] [INFO] Executing Multi Step action step 33/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:51:24]] [SUCCESS] Screenshot refreshed successfully
[[17:51:24]] [SUCCESS] Screenshot refreshed successfully
[[17:51:23]] [SUCCESS] Screenshot refreshed
[[17:51:23]] [INFO] Refreshing screenshot...
[[17:51:19]] [INFO] Executing Multi Step action step 32/36: Tap on image: banner-close-updated.png
[[17:51:19]] [SUCCESS] Screenshot refreshed successfully
[[17:51:19]] [SUCCESS] Screenshot refreshed successfully
[[17:51:19]] [SUCCESS] Screenshot refreshed
[[17:51:19]] [INFO] Refreshing screenshot...
[[17:51:09]] [INFO] Executing Multi Step action step 31/36: Swipe from (50%, 70%) to (50%, 30%)
[[17:51:09]] [SUCCESS] Screenshot refreshed successfully
[[17:51:09]] [SUCCESS] Screenshot refreshed successfully
[[17:51:08]] [SUCCESS] Screenshot refreshed
[[17:51:08]] [INFO] Refreshing screenshot...
[[17:51:05]] [INFO] Executing Multi Step action step 30/36: Tap on image: env[delivery-address-img]
[[17:51:04]] [SUCCESS] Screenshot refreshed successfully
[[17:51:04]] [SUCCESS] Screenshot refreshed successfully
[[17:51:04]] [SUCCESS] Screenshot refreshed
[[17:51:04]] [INFO] Refreshing screenshot...
[[17:51:00]] [INFO] Executing Multi Step action step 29/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[17:51:00]] [SUCCESS] Screenshot refreshed successfully
[[17:51:00]] [SUCCESS] Screenshot refreshed successfully
[[17:51:00]] [SUCCESS] Screenshot refreshed
[[17:51:00]] [INFO] Refreshing screenshot...
[[17:50:53]] [INFO] Executing Multi Step action step 28/36: Tap and Type at (54, 314): "305 238 Flinders"
[[17:50:53]] [SUCCESS] Screenshot refreshed successfully
[[17:50:53]] [SUCCESS] Screenshot refreshed successfully
[[17:50:53]] [SUCCESS] Screenshot refreshed
[[17:50:53]] [INFO] Refreshing screenshot...
[[17:50:48]] [INFO] Executing Multi Step action step 27/36: Tap on Text: "address"
[[17:50:47]] [SUCCESS] Screenshot refreshed successfully
[[17:50:47]] [SUCCESS] Screenshot refreshed successfully
[[17:50:47]] [SUCCESS] Screenshot refreshed
[[17:50:47]] [INFO] Refreshing screenshot...
[[17:50:43]] [INFO] Executing Multi Step action step 26/36: iOS Function: text - Text: " "
[[17:50:43]] [SUCCESS] Screenshot refreshed successfully
[[17:50:43]] [SUCCESS] Screenshot refreshed successfully
[[17:50:43]] [SUCCESS] Screenshot refreshed
[[17:50:43]] [INFO] Refreshing screenshot...
[[17:50:39]] [INFO] Executing Multi Step action step 25/36: textClear action
[[17:50:39]] [SUCCESS] Screenshot refreshed successfully
[[17:50:39]] [SUCCESS] Screenshot refreshed successfully
[[17:50:38]] [SUCCESS] Screenshot refreshed
[[17:50:38]] [INFO] Refreshing screenshot...
[[17:50:35]] [INFO] Executing Multi Step action step 24/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[17:50:34]] [SUCCESS] Screenshot refreshed successfully
[[17:50:34]] [SUCCESS] Screenshot refreshed successfully
[[17:50:34]] [SUCCESS] Screenshot refreshed
[[17:50:34]] [INFO] Refreshing screenshot...
[[17:50:30]] [INFO] Executing Multi Step action step 23/36: textClear action
[[17:50:30]] [SUCCESS] Screenshot refreshed successfully
[[17:50:30]] [SUCCESS] Screenshot refreshed successfully
[[17:50:29]] [SUCCESS] Screenshot refreshed
[[17:50:29]] [INFO] Refreshing screenshot...
[[17:50:25]] [INFO] Executing Multi Step action step 22/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:50:25]] [SUCCESS] Screenshot refreshed successfully
[[17:50:25]] [SUCCESS] Screenshot refreshed successfully
[[17:50:25]] [SUCCESS] Screenshot refreshed
[[17:50:25]] [INFO] Refreshing screenshot...
[[17:50:21]] [INFO] Executing Multi Step action step 21/36: textClear action
[[17:50:20]] [SUCCESS] Screenshot refreshed successfully
[[17:50:20]] [SUCCESS] Screenshot refreshed successfully
[[17:50:20]] [SUCCESS] Screenshot refreshed
[[17:50:20]] [INFO] Refreshing screenshot...
[[17:50:17]] [INFO] Executing Multi Step action step 20/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[17:50:16]] [SUCCESS] Screenshot refreshed successfully
[[17:50:16]] [SUCCESS] Screenshot refreshed successfully
[[17:50:16]] [SUCCESS] Screenshot refreshed
[[17:50:16]] [INFO] Refreshing screenshot...
[[17:50:12]] [INFO] Executing Multi Step action step 19/36: textClear action
[[17:50:12]] [SUCCESS] Screenshot refreshed successfully
[[17:50:12]] [SUCCESS] Screenshot refreshed successfully
[[17:50:11]] [SUCCESS] Screenshot refreshed
[[17:50:11]] [INFO] Refreshing screenshot...
[[17:50:08]] [INFO] Executing Multi Step action step 18/36: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[17:50:08]] [SUCCESS] Screenshot refreshed successfully
[[17:50:08]] [SUCCESS] Screenshot refreshed successfully
[[17:50:07]] [SUCCESS] Screenshot refreshed
[[17:50:07]] [INFO] Refreshing screenshot...
[[17:50:04]] [INFO] Executing Multi Step action step 17/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[17:50:03]] [SUCCESS] Screenshot refreshed successfully
[[17:50:03]] [SUCCESS] Screenshot refreshed successfully
[[17:50:03]] [SUCCESS] Screenshot refreshed
[[17:50:03]] [INFO] Refreshing screenshot...
[[17:49:44]] [INFO] Executing Multi Step action step 16/36: Swipe up till element xpath: "//XCUIElementTypeButton[@name="Continue to details"]" is visible
[[17:49:44]] [SUCCESS] Screenshot refreshed successfully
[[17:49:44]] [SUCCESS] Screenshot refreshed successfully
[[17:49:43]] [SUCCESS] Screenshot refreshed
[[17:49:43]] [INFO] Refreshing screenshot...
[[17:49:40]] [INFO] Executing Multi Step action step 15/36: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[17:49:39]] [SUCCESS] Screenshot refreshed successfully
[[17:49:39]] [SUCCESS] Screenshot refreshed successfully
[[17:49:39]] [SUCCESS] Screenshot refreshed
[[17:49:39]] [INFO] Refreshing screenshot...
[[17:49:36]] [INFO] Executing Multi Step action step 14/36: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[17:49:36]] [SUCCESS] Screenshot refreshed successfully
[[17:49:36]] [SUCCESS] Screenshot refreshed successfully
[[17:49:35]] [SUCCESS] Screenshot refreshed
[[17:49:35]] [INFO] Refreshing screenshot...
[[17:49:23]] [INFO] Executing Multi Step action step 13/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:49:23]] [SUCCESS] Screenshot refreshed successfully
[[17:49:23]] [SUCCESS] Screenshot refreshed successfully
[[17:49:23]] [SUCCESS] Screenshot refreshed
[[17:49:23]] [INFO] Refreshing screenshot...
[[17:49:19]] [INFO] Executing Multi Step action step 12/36: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:49:19]] [SUCCESS] Screenshot refreshed successfully
[[17:49:19]] [SUCCESS] Screenshot refreshed successfully
[[17:49:18]] [SUCCESS] Screenshot refreshed
[[17:49:18]] [INFO] Refreshing screenshot...
[[17:49:12]] [INFO] Executing Multi Step action step 11/36: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[17:49:12]] [SUCCESS] Screenshot refreshed successfully
[[17:49:12]] [SUCCESS] Screenshot refreshed successfully
[[17:49:12]] [SUCCESS] Screenshot refreshed
[[17:49:12]] [INFO] Refreshing screenshot...
[[17:49:08]] [INFO] Executing Multi Step action step 10/36: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:49:08]] [SUCCESS] Screenshot refreshed successfully
[[17:49:08]] [SUCCESS] Screenshot refreshed successfully
[[17:49:08]] [SUCCESS] Screenshot refreshed
[[17:49:08]] [INFO] Refreshing screenshot...
[[17:49:04]] [INFO] Executing Multi Step action step 9/36: iOS Function: text - Text: "Uno card"
[[17:49:03]] [SUCCESS] Screenshot refreshed successfully
[[17:49:03]] [SUCCESS] Screenshot refreshed successfully
[[17:49:03]] [SUCCESS] Screenshot refreshed
[[17:49:03]] [INFO] Refreshing screenshot...
[[17:48:58]] [INFO] Executing Multi Step action step 8/36: Tap on Text: "Find"
[[17:48:58]] [SUCCESS] Screenshot refreshed successfully
[[17:48:58]] [SUCCESS] Screenshot refreshed successfully
[[17:48:58]] [SUCCESS] Screenshot refreshed
[[17:48:58]] [INFO] Refreshing screenshot...
[[17:48:46]] [INFO] Executing Multi Step action step 7/36: Tap if locator exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]"
[[17:48:46]] [SUCCESS] Screenshot refreshed successfully
[[17:48:46]] [SUCCESS] Screenshot refreshed successfully
[[17:48:46]] [SUCCESS] Screenshot refreshed
[[17:48:46]] [INFO] Refreshing screenshot...
[[17:48:34]] [INFO] Executing Multi Step action step 6/36: Tap if locator exists: accessibility_id="btnUpdate"
[[17:48:34]] [SUCCESS] Screenshot refreshed successfully
[[17:48:34]] [SUCCESS] Screenshot refreshed successfully
[[17:48:33]] [SUCCESS] Screenshot refreshed
[[17:48:33]] [INFO] Refreshing screenshot...
[[17:48:22]] [INFO] Executing Multi Step action step 5/36: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[17:48:22]] [SUCCESS] Screenshot refreshed successfully
[[17:48:22]] [SUCCESS] Screenshot refreshed successfully
[[17:48:21]] [SUCCESS] Screenshot refreshed
[[17:48:21]] [INFO] Refreshing screenshot...
[[17:48:17]] [INFO] Executing Multi Step action step 4/36: Tap on Text: "Save"
[[17:48:17]] [SUCCESS] Screenshot refreshed successfully
[[17:48:17]] [SUCCESS] Screenshot refreshed successfully
[[17:48:17]] [SUCCESS] Screenshot refreshed
[[17:48:17]] [INFO] Refreshing screenshot...
[[17:48:11]] [INFO] Executing Multi Step action step 3/36: Tap on element with accessibility_id: btnCurrentLocationButton
[[17:48:11]] [SUCCESS] Screenshot refreshed successfully
[[17:48:11]] [SUCCESS] Screenshot refreshed successfully
[[17:48:11]] [SUCCESS] Screenshot refreshed
[[17:48:11]] [INFO] Refreshing screenshot...
[[17:48:06]] [INFO] Executing Multi Step action step 2/36: Wait till accessibility_id=btnCurrentLocationButton
[[17:48:06]] [SUCCESS] Screenshot refreshed successfully
[[17:48:06]] [SUCCESS] Screenshot refreshed successfully
[[17:48:06]] [SUCCESS] Screenshot refreshed
[[17:48:06]] [INFO] Refreshing screenshot...
[[17:47:59]] [INFO] Executing Multi Step action step 1/36: Tap on Text: "Edit"
[[17:47:59]] [INFO] Loaded 36 steps from test case: Delivery  Buy
[[17:47:59]] [INFO] Loading steps for multiStep action: Delivery  Buy
[[17:47:59]] [INFO] 8ZYdW2lMKv=running
[[17:47:59]] [INFO] Executing action 87/643: Execute Test Case: Delivery  Buy (36 steps)
[[17:47:59]] [SUCCESS] Screenshot refreshed successfully
[[17:47:59]] [SUCCESS] Screenshot refreshed successfully
[[17:47:59]] [SUCCESS] Screenshot refreshed
[[17:47:59]] [INFO] Refreshing screenshot...
[[17:47:59]] [INFO] cKNu2QoRC1=pass
[[17:47:55]] [INFO] cKNu2QoRC1=running
[[17:47:55]] [INFO] Executing action 86/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[17:47:55]] [SUCCESS] Screenshot refreshed successfully
[[17:47:55]] [SUCCESS] Screenshot refreshed successfully
[[17:47:55]] [SUCCESS] Screenshot refreshed
[[17:47:55]] [INFO] Refreshing screenshot...
[[17:47:55]] [INFO] OyUowAaBzD=pass
[[17:47:51]] [INFO] OyUowAaBzD=running
[[17:47:51]] [INFO] Executing action 85/643: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[17:47:51]] [SUCCESS] Screenshot refreshed successfully
[[17:47:51]] [SUCCESS] Screenshot refreshed successfully
[[17:47:50]] [SUCCESS] Screenshot refreshed
[[17:47:50]] [INFO] Refreshing screenshot...
[[17:47:50]] [INFO] Ob26qqcA0p=pass
[[17:47:44]] [INFO] Ob26qqcA0p=running
[[17:47:44]] [INFO] Executing action 84/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:47:43]] [SUCCESS] Screenshot refreshed successfully
[[17:47:43]] [SUCCESS] Screenshot refreshed successfully
[[17:47:43]] [SUCCESS] Screenshot refreshed
[[17:47:43]] [INFO] Refreshing screenshot...
[[17:47:43]] [INFO] k3mu9Mt7Ec=pass
[[17:47:39]] [INFO] k3mu9Mt7Ec=running
[[17:47:39]] [INFO] Executing action 83/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:47:39]] [SUCCESS] Screenshot refreshed successfully
[[17:47:39]] [SUCCESS] Screenshot refreshed successfully
[[17:47:39]] [SUCCESS] Screenshot refreshed
[[17:47:39]] [INFO] Refreshing screenshot...
[[17:47:39]] [INFO] 8umPSX0vrr=pass
[[17:47:35]] [INFO] 8umPSX0vrr=running
[[17:47:35]] [INFO] Executing action 82/643: Tap on image: banner-close-updated.png
[[17:47:35]] [SUCCESS] Screenshot refreshed successfully
[[17:47:35]] [SUCCESS] Screenshot refreshed successfully
[[17:47:35]] [SUCCESS] Screenshot refreshed
[[17:47:35]] [INFO] Refreshing screenshot...
[[17:47:35]] [INFO] pr9o8Zsm5p=pass
[[17:47:31]] [INFO] pr9o8Zsm5p=running
[[17:47:31]] [INFO] Executing action 81/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:47:31]] [SUCCESS] Screenshot refreshed successfully
[[17:47:31]] [SUCCESS] Screenshot refreshed successfully
[[17:47:30]] [SUCCESS] Screenshot refreshed
[[17:47:30]] [INFO] Refreshing screenshot...
[[17:47:30]] [INFO] XCynRs6gJ3=pass
[[17:47:23]] [INFO] XCynRs6gJ3=running
[[17:47:23]] [INFO] Executing action 80/643: Swipe up till element xpath: "//XCUIElementTypeButton[contains(@name,"Remove")]" is visible
[[17:47:23]] [SUCCESS] Screenshot refreshed successfully
[[17:47:23]] [SUCCESS] Screenshot refreshed successfully
[[17:47:22]] [SUCCESS] Screenshot refreshed
[[17:47:22]] [INFO] Refreshing screenshot...
[[17:47:22]] [INFO] UnxZdeLmYu=pass
[[17:47:11]] [INFO] UnxZdeLmYu=running
[[17:47:11]] [INFO] Executing action 79/643: Wait for 10 ms
[[17:47:11]] [SUCCESS] Screenshot refreshed successfully
[[17:47:11]] [SUCCESS] Screenshot refreshed successfully
[[17:47:11]] [SUCCESS] Screenshot refreshed
[[17:47:11]] [INFO] Refreshing screenshot...
[[17:47:11]] [INFO] qjj0i3rcUh=pass
[[17:47:07]] [INFO] qjj0i3rcUh=running
[[17:47:07]] [INFO] Executing action 78/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[17:47:06]] [SUCCESS] Screenshot refreshed successfully
[[17:47:06]] [SUCCESS] Screenshot refreshed successfully
[[17:47:06]] [SUCCESS] Screenshot refreshed
[[17:47:06]] [INFO] Refreshing screenshot...
[[17:47:06]] [INFO] 42Jm6o7r1t=pass
[[17:46:54]] [INFO] 42Jm6o7r1t=running
[[17:46:54]] [INFO] Executing action 77/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:46:54]] [SUCCESS] Screenshot refreshed successfully
[[17:46:54]] [SUCCESS] Screenshot refreshed successfully
[[17:46:54]] [SUCCESS] Screenshot refreshed
[[17:46:54]] [INFO] Refreshing screenshot...
[[17:46:54]] [INFO] lWIRxRm6HE=pass
[[17:46:50]] [INFO] lWIRxRm6HE=running
[[17:46:50]] [INFO] Executing action 76/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:46:50]] [SUCCESS] Screenshot refreshed successfully
[[17:46:50]] [SUCCESS] Screenshot refreshed successfully
[[17:46:50]] [SUCCESS] Screenshot refreshed
[[17:46:50]] [INFO] Refreshing screenshot...
[[17:46:50]] [INFO] Q0fomJIDoQ=pass
[[17:46:46]] [INFO] Q0fomJIDoQ=running
[[17:46:46]] [INFO] Executing action 75/643: Tap on image: banner-close-updated.png
[[17:46:46]] [SUCCESS] Screenshot refreshed successfully
[[17:46:46]] [SUCCESS] Screenshot refreshed successfully
[[17:46:45]] [SUCCESS] Screenshot refreshed
[[17:46:45]] [INFO] Refreshing screenshot...
[[17:46:45]] [INFO] 7SpDO20tS2=pass
[[17:46:34]] [INFO] 7SpDO20tS2=running
[[17:46:34]] [INFO] Executing action 74/643: Wait for 10 ms
[[17:46:34]] [SUCCESS] Screenshot refreshed successfully
[[17:46:34]] [SUCCESS] Screenshot refreshed successfully
[[17:46:34]] [SUCCESS] Screenshot refreshed
[[17:46:34]] [INFO] Refreshing screenshot...
[[17:46:34]] [INFO] FKZs2qCWoU=pass
[[17:46:29]] [INFO] FKZs2qCWoU=running
[[17:46:29]] [INFO] Executing action 73/643: Tap on Text: "Tarneit"
[[17:46:29]] [SUCCESS] Screenshot refreshed successfully
[[17:46:29]] [SUCCESS] Screenshot refreshed successfully
[[17:46:29]] [SUCCESS] Screenshot refreshed
[[17:46:29]] [INFO] Refreshing screenshot...
[[17:46:29]] [INFO] Qbg9bipTGs=pass
[[17:46:25]] [INFO] Qbg9bipTGs=running
[[17:46:25]] [INFO] Executing action 72/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:46:24]] [SUCCESS] Screenshot refreshed successfully
[[17:46:24]] [SUCCESS] Screenshot refreshed successfully
[[17:46:24]] [SUCCESS] Screenshot refreshed
[[17:46:24]] [INFO] Refreshing screenshot...
[[17:46:24]] [INFO] qjj0i3rcUh=pass
[[17:46:21]] [INFO] qjj0i3rcUh=running
[[17:46:21]] [INFO] Executing action 71/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[17:46:20]] [SUCCESS] Screenshot refreshed successfully
[[17:46:20]] [SUCCESS] Screenshot refreshed successfully
[[17:46:20]] [SUCCESS] Screenshot refreshed
[[17:46:20]] [INFO] Refreshing screenshot...
[[17:46:20]] [INFO] uM5FOSrU5U=pass
[[17:46:17]] [INFO] uM5FOSrU5U=running
[[17:46:17]] [INFO] Executing action 70/643: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[17:46:17]] [SUCCESS] Screenshot refreshed successfully
[[17:46:17]] [SUCCESS] Screenshot refreshed successfully
[[17:46:17]] [SUCCESS] Screenshot refreshed
[[17:46:17]] [INFO] Refreshing screenshot...
[[17:46:17]] [INFO] QB2bKb0SsP=pass
[[17:46:05]] [INFO] QB2bKb0SsP=running
[[17:46:05]] [INFO] Executing action 69/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:46:05]] [SUCCESS] Screenshot refreshed successfully
[[17:46:05]] [SUCCESS] Screenshot refreshed successfully
[[17:46:04]] [SUCCESS] Screenshot refreshed
[[17:46:04]] [INFO] Refreshing screenshot...
[[17:46:04]] [INFO] F1olhgKhUt=pass
[[17:46:01]] [INFO] F1olhgKhUt=running
[[17:46:01]] [INFO] Executing action 68/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:46:00]] [SUCCESS] Screenshot refreshed successfully
[[17:46:00]] [SUCCESS] Screenshot refreshed successfully
[[17:46:00]] [SUCCESS] Screenshot refreshed
[[17:46:00]] [INFO] Refreshing screenshot...
[[17:46:00]] [INFO] jY0oPjKbuS=pass
[[17:45:57]] [INFO] jY0oPjKbuS=running
[[17:45:57]] [INFO] Executing action 67/643: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[17:45:57]] [SUCCESS] Screenshot refreshed successfully
[[17:45:57]] [SUCCESS] Screenshot refreshed successfully
[[17:45:57]] [SUCCESS] Screenshot refreshed
[[17:45:57]] [INFO] Refreshing screenshot...
[[17:45:57]] [INFO] FnrbyHq7bU=pass
[[17:45:50]] [INFO] FnrbyHq7bU=running
[[17:45:50]] [INFO] Executing action 66/643: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[17:45:50]] [SUCCESS] Screenshot refreshed successfully
[[17:45:50]] [SUCCESS] Screenshot refreshed successfully
[[17:45:50]] [SUCCESS] Screenshot refreshed
[[17:45:50]] [INFO] Refreshing screenshot...
[[17:45:50]] [INFO] nAB6Q8LAdv=pass
[[17:45:46]] [INFO] nAB6Q8LAdv=running
[[17:45:46]] [INFO] Executing action 65/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:45:46]] [SUCCESS] Screenshot refreshed successfully
[[17:45:46]] [SUCCESS] Screenshot refreshed successfully
[[17:45:46]] [SUCCESS] Screenshot refreshed
[[17:45:46]] [INFO] Refreshing screenshot...
[[17:45:46]] [INFO] sc2KH9bG6H=pass
[[17:45:42]] [INFO] sc2KH9bG6H=running
[[17:45:42]] [INFO] Executing action 64/643: iOS Function: text - Text: "Uno card"
[[17:45:42]] [SUCCESS] Screenshot refreshed successfully
[[17:45:42]] [SUCCESS] Screenshot refreshed successfully
[[17:45:42]] [SUCCESS] Screenshot refreshed
[[17:45:42]] [INFO] Refreshing screenshot...
[[17:45:42]] [INFO] ZBXCQNlT8z=pass
[[17:45:37]] [INFO] ZBXCQNlT8z=running
[[17:45:37]] [INFO] Executing action 63/643: Tap on Text: "Find"
[[17:45:37]] [SUCCESS] Screenshot refreshed successfully
[[17:45:37]] [SUCCESS] Screenshot refreshed successfully
[[17:45:36]] [SUCCESS] Screenshot refreshed
[[17:45:36]] [INFO] Refreshing screenshot...
[[17:45:36]] [INFO] HYl6Z7Gvqz=pass
[[17:45:31]] [SUCCESS] Screenshot refreshed successfully
[[17:45:31]] [SUCCESS] Screenshot refreshed successfully
[[17:45:30]] [INFO] HYl6Z7Gvqz=running
[[17:45:30]] [INFO] Executing action 62/643: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[17:45:30]] [SUCCESS] Screenshot refreshed
[[17:45:30]] [INFO] Refreshing screenshot...
[[17:45:30]] [SUCCESS] Screenshot refreshed successfully
[[17:45:30]] [SUCCESS] Screenshot refreshed successfully
[[17:45:30]] [SUCCESS] Screenshot refreshed
[[17:45:30]] [INFO] Refreshing screenshot...
[[17:45:25]] [INFO] Executing Multi Step action step 5/5: iOS Function: text - Text: "Wonderbaby@5"
[[17:45:25]] [SUCCESS] Screenshot refreshed successfully
[[17:45:25]] [SUCCESS] Screenshot refreshed successfully
[[17:45:25]] [SUCCESS] Screenshot refreshed
[[17:45:25]] [INFO] Refreshing screenshot...
[[17:45:21]] [INFO] Executing Multi Step action step 4/5: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[17:45:21]] [SUCCESS] Screenshot refreshed successfully
[[17:45:21]] [SUCCESS] Screenshot refreshed successfully
[[17:45:21]] [SUCCESS] Screenshot refreshed
[[17:45:21]] [INFO] Refreshing screenshot...
[[17:45:16]] [INFO] Executing Multi Step action step 3/5: iOS Function: text - Text: "<EMAIL>"
[[17:45:16]] [SUCCESS] Screenshot refreshed successfully
[[17:45:16]] [SUCCESS] Screenshot refreshed successfully
[[17:45:16]] [SUCCESS] Screenshot refreshed
[[17:45:16]] [INFO] Refreshing screenshot...
[[17:45:12]] [INFO] Executing Multi Step action step 2/5: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[17:45:11]] [SUCCESS] Screenshot refreshed successfully
[[17:45:11]] [SUCCESS] Screenshot refreshed successfully
[[17:45:11]] [SUCCESS] Screenshot refreshed
[[17:45:11]] [INFO] Refreshing screenshot...
[[17:45:06]] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:45:06]] [INFO] Loaded 5 steps from test case: Kmart-Signin
[[17:45:06]] [INFO] Loading steps for multiStep action: Kmart-Signin
[[17:45:06]] [INFO] El6k4IPZly=running
[[17:45:06]] [INFO] Executing action 61/643: Execute Test Case: Kmart-Signin (8 steps)
[[17:45:05]] [SUCCESS] Screenshot refreshed successfully
[[17:45:05]] [SUCCESS] Screenshot refreshed successfully
[[17:45:05]] [SUCCESS] Screenshot refreshed
[[17:45:05]] [INFO] Refreshing screenshot...
[[17:45:05]] [INFO] 3caMBvQX7k=pass
[[17:45:02]] [INFO] 3caMBvQX7k=running
[[17:45:02]] [INFO] Executing action 60/643: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[17:45:02]] [SUCCESS] Screenshot refreshed successfully
[[17:45:02]] [SUCCESS] Screenshot refreshed successfully
[[17:45:01]] [SUCCESS] Screenshot refreshed
[[17:45:01]] [INFO] Refreshing screenshot...
[[17:45:01]] [INFO] yUJyVO5Wev=pass
[[17:44:59]] [INFO] yUJyVO5Wev=running
[[17:44:59]] [INFO] Executing action 59/643: iOS Function: alert_accept
[[17:44:59]] [SUCCESS] Screenshot refreshed successfully
[[17:44:59]] [SUCCESS] Screenshot refreshed successfully
[[17:44:59]] [SUCCESS] Screenshot refreshed
[[17:44:59]] [INFO] Refreshing screenshot...
[[17:44:59]] [INFO] rkL0oz4kiL=pass
[[17:44:51]] [INFO] rkL0oz4kiL=running
[[17:44:51]] [INFO] Executing action 58/643: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:44:51]] [SUCCESS] Screenshot refreshed successfully
[[17:44:51]] [SUCCESS] Screenshot refreshed successfully
[[17:44:51]] [SUCCESS] Screenshot refreshed
[[17:44:51]] [INFO] Refreshing screenshot...
[[17:44:51]] [INFO] HotUJOd6oB=pass
[[17:44:38]] [SUCCESS] Screenshot refreshed successfully
[[17:44:38]] [SUCCESS] Screenshot refreshed successfully
[[17:44:37]] [INFO] HotUJOd6oB=running
[[17:44:37]] [INFO] Executing action 57/643: Restart app: env[appid]
[[17:44:37]] [SUCCESS] Screenshot refreshed
[[17:44:37]] [INFO] Refreshing screenshot...
[[17:44:37]] [SUCCESS] Screenshot refreshed successfully
[[17:44:37]] [SUCCESS] Screenshot refreshed successfully
[[17:44:37]] [SUCCESS] Screenshot refreshed
[[17:44:37]] [INFO] Refreshing screenshot...
[[17:44:35]] [INFO] Executing Multi Step action step 6/6: Terminate app: au.com.kmart
[[17:44:34]] [SUCCESS] Screenshot refreshed successfully
[[17:44:34]] [SUCCESS] Screenshot refreshed successfully
[[17:44:34]] [SUCCESS] Screenshot refreshed
[[17:44:34]] [INFO] Refreshing screenshot...
[[17:44:22]] [INFO] Executing Multi Step action step 5/6: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="txtSign out"]"
[[17:44:21]] [SUCCESS] Screenshot refreshed successfully
[[17:44:21]] [SUCCESS] Screenshot refreshed successfully
[[17:44:21]] [SUCCESS] Screenshot refreshed
[[17:44:21]] [INFO] Refreshing screenshot...
[[17:44:17]] [INFO] Executing Multi Step action step 4/6: Swipe from (50%, 70%) to (50%, 30%)
[[17:44:17]] [SUCCESS] Screenshot refreshed successfully
[[17:44:17]] [SUCCESS] Screenshot refreshed successfully
[[17:44:17]] [SUCCESS] Screenshot refreshed
[[17:44:17]] [INFO] Refreshing screenshot...
[[17:44:13]] [INFO] Executing Multi Step action step 3/6: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[17:44:13]] [SUCCESS] Screenshot refreshed successfully
[[17:44:13]] [SUCCESS] Screenshot refreshed successfully
[[17:44:13]] [SUCCESS] Screenshot refreshed
[[17:44:13]] [INFO] Refreshing screenshot...
[[17:44:06]] [INFO] Executing Multi Step action step 2/6: Wait for 5 ms
[[17:44:06]] [SUCCESS] Screenshot refreshed successfully
[[17:44:06]] [SUCCESS] Screenshot refreshed successfully
[[17:44:06]] [SUCCESS] Screenshot refreshed
[[17:44:06]] [INFO] Refreshing screenshot...
[[17:44:00]] [INFO] Executing Multi Step action step 1/6: Restart app: au.com.kmart
[[17:44:00]] [INFO] Loaded 6 steps from test case: Kmart_AU_Cleanup
[[17:44:00]] [INFO] Loading steps for cleanupSteps action: Kmart_AU_Cleanup
[[17:44:00]] [INFO] vKo6Ox3YrP=running
[[17:44:00]] [INFO] Executing action 56/643: cleanupSteps action
[[17:44:00]] [SUCCESS] Screenshot refreshed successfully
[[17:44:00]] [SUCCESS] Screenshot refreshed successfully
[[17:44:00]] [SUCCESS] Screenshot refreshed
[[17:44:00]] [INFO] Refreshing screenshot...
[[17:44:00]] [INFO] x4yLCZHaCR=pass
[[17:43:57]] [INFO] x4yLCZHaCR=running
[[17:43:57]] [INFO] Executing action 55/643: Terminate app: env[appid]
[[17:43:57]] [SUCCESS] Screenshot refreshed successfully
[[17:43:57]] [SUCCESS] Screenshot refreshed successfully
[[17:43:57]] [SUCCESS] Screenshot refreshed
[[17:43:57]] [INFO] Refreshing screenshot...
[[17:43:57]] [INFO] 2p13JoJbbA=pass
[[17:43:53]] [INFO] 2p13JoJbbA=running
[[17:43:53]] [INFO] Executing action 54/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:43:53]] [SUCCESS] Screenshot refreshed successfully
[[17:43:53]] [SUCCESS] Screenshot refreshed successfully
[[17:43:53]] [SUCCESS] Screenshot refreshed
[[17:43:53]] [INFO] Refreshing screenshot...
[[17:43:53]] [INFO] 2p13JoJbbA=pass
[[17:43:49]] [INFO] 2p13JoJbbA=running
[[17:43:49]] [INFO] Executing action 53/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[17:43:49]] [SUCCESS] Screenshot refreshed successfully
[[17:43:49]] [SUCCESS] Screenshot refreshed successfully
[[17:43:49]] [SUCCESS] Screenshot refreshed
[[17:43:49]] [INFO] Refreshing screenshot...
[[17:43:49]] [INFO] nyBidG0kHp=pass
[[17:43:42]] [INFO] nyBidG0kHp=running
[[17:43:42]] [INFO] Executing action 52/643: Swipe from (50%, 50%) to (50%, 20%)
[[17:43:41]] [SUCCESS] Screenshot refreshed successfully
[[17:43:41]] [SUCCESS] Screenshot refreshed successfully
[[17:43:41]] [SUCCESS] Screenshot refreshed
[[17:43:41]] [INFO] Refreshing screenshot...
[[17:43:41]] [INFO] w7I4F66YKQ=pass
[[17:43:29]] [INFO] w7I4F66YKQ=running
[[17:43:29]] [INFO] Executing action 51/643: Tap if locator exists: xpath="//XCUIElementTypeButton[@name="Checkout"]"
[[17:43:29]] [SUCCESS] Screenshot refreshed successfully
[[17:43:29]] [SUCCESS] Screenshot refreshed successfully
[[17:43:29]] [SUCCESS] Screenshot refreshed
[[17:43:29]] [INFO] Refreshing screenshot...
[[17:43:29]] [INFO] F4NGh9HrLw=pass
[[17:43:25]] [INFO] F4NGh9HrLw=running
[[17:43:25]] [INFO] Executing action 50/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[17:43:24]] [SUCCESS] Screenshot refreshed successfully
[[17:43:24]] [SUCCESS] Screenshot refreshed successfully
[[17:43:24]] [SUCCESS] Screenshot refreshed
[[17:43:24]] [INFO] Refreshing screenshot...
[[17:43:24]] [INFO] VtMfqK1V9t=pass
[[17:43:05]] [INFO] VtMfqK1V9t=running
[[17:43:05]] [INFO] Executing action 49/643: Tap on element with accessibility_id: Add to bag
[[17:43:05]] [SUCCESS] Screenshot refreshed successfully
[[17:43:05]] [SUCCESS] Screenshot refreshed successfully
[[17:43:05]] [SUCCESS] Screenshot refreshed
[[17:43:05]] [INFO] Refreshing screenshot...
[[17:43:05]] [INFO] NOnuFzXy63=pass
[[17:43:01]] [INFO] NOnuFzXy63=running
[[17:43:01]] [INFO] Executing action 48/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:43:01]] [SUCCESS] Screenshot refreshed successfully
[[17:43:01]] [SUCCESS] Screenshot refreshed successfully
[[17:43:00]] [SUCCESS] Screenshot refreshed
[[17:43:00]] [INFO] Refreshing screenshot...
[[17:43:00]] [INFO] kz9lnCdwoH=pass
[[17:42:56]] [INFO] kz9lnCdwoH=running
[[17:42:56]] [INFO] Executing action 47/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[17:42:56]] [SUCCESS] Screenshot refreshed successfully
[[17:42:56]] [SUCCESS] Screenshot refreshed successfully
[[17:42:56]] [SUCCESS] Screenshot refreshed
[[17:42:56]] [INFO] Refreshing screenshot...
[[17:42:56]] [INFO] kz9lnCdwoH=pass
[[17:42:52]] [INFO] kz9lnCdwoH=running
[[17:42:52]] [INFO] Executing action 46/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:42:52]] [SUCCESS] Screenshot refreshed successfully
[[17:42:52]] [SUCCESS] Screenshot refreshed successfully
[[17:42:52]] [SUCCESS] Screenshot refreshed
[[17:42:52]] [INFO] Refreshing screenshot...
[[17:42:52]] [INFO] qIF9CVPc56=pass
[[17:42:48]] [INFO] qIF9CVPc56=running
[[17:42:48]] [INFO] Executing action 45/643: iOS Function: text - Text: "mat"
[[17:42:48]] [SUCCESS] Screenshot refreshed successfully
[[17:42:48]] [SUCCESS] Screenshot refreshed successfully
[[17:42:48]] [SUCCESS] Screenshot refreshed
[[17:42:48]] [INFO] Refreshing screenshot...
[[17:42:48]] [INFO] yEga5MkcRe=pass
[[17:42:44]] [INFO] yEga5MkcRe=running
[[17:42:44]] [INFO] Executing action 44/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:42:44]] [SUCCESS] Screenshot refreshed successfully
[[17:42:44]] [SUCCESS] Screenshot refreshed successfully
[[17:42:44]] [SUCCESS] Screenshot refreshed
[[17:42:44]] [INFO] Refreshing screenshot...
[[17:42:44]] [INFO] F4NGh9HrLw=pass
[[17:42:40]] [INFO] F4NGh9HrLw=running
[[17:42:40]] [INFO] Executing action 43/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:42:40]] [SUCCESS] Screenshot refreshed successfully
[[17:42:40]] [SUCCESS] Screenshot refreshed successfully
[[17:42:39]] [SUCCESS] Screenshot refreshed
[[17:42:39]] [INFO] Refreshing screenshot...
[[17:42:39]] [INFO] kz9lnCdwoH=pass
[[17:42:35]] [INFO] kz9lnCdwoH=running
[[17:42:35]] [INFO] Executing action 42/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[17:42:35]] [SUCCESS] Screenshot refreshed successfully
[[17:42:35]] [SUCCESS] Screenshot refreshed successfully
[[17:42:35]] [SUCCESS] Screenshot refreshed
[[17:42:35]] [INFO] Refreshing screenshot...
[[17:42:35]] [INFO] kz9lnCdwoH=pass
[[17:42:31]] [INFO] kz9lnCdwoH=running
[[17:42:31]] [INFO] Executing action 41/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:42:31]] [SUCCESS] Screenshot refreshed successfully
[[17:42:31]] [SUCCESS] Screenshot refreshed successfully
[[17:42:31]] [SUCCESS] Screenshot refreshed
[[17:42:31]] [INFO] Refreshing screenshot...
[[17:42:31]] [INFO] JRheDTvpJf=pass
[[17:42:27]] [INFO] JRheDTvpJf=running
[[17:42:27]] [INFO] Executing action 40/643: iOS Function: text - Text: "Kid toy"
[[17:42:27]] [SUCCESS] Screenshot refreshed successfully
[[17:42:27]] [SUCCESS] Screenshot refreshed successfully
[[17:42:26]] [SUCCESS] Screenshot refreshed
[[17:42:26]] [INFO] Refreshing screenshot...
[[17:42:26]] [INFO] yEga5MkcRe=pass
[[17:42:23]] [INFO] yEga5MkcRe=running
[[17:42:23]] [INFO] Executing action 39/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:42:23]] [SUCCESS] Screenshot refreshed successfully
[[17:42:23]] [SUCCESS] Screenshot refreshed successfully
[[17:42:22]] [SUCCESS] Screenshot refreshed
[[17:42:22]] [INFO] Refreshing screenshot...
[[17:42:22]] [INFO] F4NGh9HrLw=pass
[[17:42:19]] [INFO] F4NGh9HrLw=running
[[17:42:19]] [INFO] Executing action 38/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:42:19]] [SUCCESS] Screenshot refreshed successfully
[[17:42:19]] [SUCCESS] Screenshot refreshed successfully
[[17:42:18]] [SUCCESS] Screenshot refreshed
[[17:42:18]] [INFO] Refreshing screenshot...
[[17:42:18]] [INFO] XPEr3w6Zof=pass
[[17:42:14]] [INFO] XPEr3w6Zof=running
[[17:42:14]] [INFO] Executing action 37/643: Restart app: env[appid]
[[17:42:13]] [SUCCESS] Screenshot refreshed successfully
[[17:42:13]] [SUCCESS] Screenshot refreshed successfully
[[17:42:13]] [SUCCESS] Screenshot refreshed
[[17:42:13]] [INFO] Refreshing screenshot...
[[17:42:13]] [INFO] PiQRBWBe3E=pass
[[17:42:10]] [INFO] PiQRBWBe3E=running
[[17:42:10]] [INFO] Executing action 36/643: Tap on image: env[device-back-img]
[[17:42:09]] [SUCCESS] Screenshot refreshed successfully
[[17:42:09]] [SUCCESS] Screenshot refreshed successfully
[[17:42:09]] [SUCCESS] Screenshot refreshed
[[17:42:09]] [INFO] Refreshing screenshot...
[[17:42:09]] [INFO] GWoppouz1l=pass
[[17:42:07]] [INFO] GWoppouz1l=running
[[17:42:07]] [INFO] Executing action 35/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[17:42:06]] [SUCCESS] Screenshot refreshed successfully
[[17:42:06]] [SUCCESS] Screenshot refreshed successfully
[[17:42:06]] [SUCCESS] Screenshot refreshed
[[17:42:06]] [INFO] Refreshing screenshot...
[[17:42:06]] [INFO] B6GDXWAmWp=pass
[[17:42:02]] [INFO] B6GDXWAmWp=running
[[17:42:02]] [INFO] Executing action 34/643: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[17:42:02]] [SUCCESS] Screenshot refreshed successfully
[[17:42:02]] [SUCCESS] Screenshot refreshed successfully
[[17:42:02]] [SUCCESS] Screenshot refreshed
[[17:42:02]] [INFO] Refreshing screenshot...
[[17:42:02]] [INFO] mtYqeDttRc=pass
[[17:41:58]] [INFO] mtYqeDttRc=running
[[17:41:58]] [INFO] Executing action 33/643: Tap on image: env[paypal-close-img]
[[17:41:57]] [SUCCESS] Screenshot refreshed successfully
[[17:41:57]] [SUCCESS] Screenshot refreshed successfully
[[17:41:57]] [SUCCESS] Screenshot refreshed
[[17:41:57]] [INFO] Refreshing screenshot...
[[17:41:57]] [INFO] q6cKxgMAIn=pass
[[17:41:51]] [INFO] q6cKxgMAIn=running
[[17:41:51]] [INFO] Executing action 32/643: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[17:41:51]] [SUCCESS] Screenshot refreshed successfully
[[17:41:51]] [SUCCESS] Screenshot refreshed successfully
[[17:41:51]] [SUCCESS] Screenshot refreshed
[[17:41:51]] [INFO] Refreshing screenshot...
[[17:41:51]] [INFO] KRQDBv2D3A=pass
[[17:41:47]] [INFO] KRQDBv2D3A=running
[[17:41:47]] [INFO] Executing action 31/643: Tap on image: env[device-back-img]
[[17:41:47]] [SUCCESS] Screenshot refreshed successfully
[[17:41:47]] [SUCCESS] Screenshot refreshed successfully
[[17:41:47]] [SUCCESS] Screenshot refreshed
[[17:41:47]] [INFO] Refreshing screenshot...
[[17:41:47]] [INFO] P4b2BITpCf=pass
[[17:41:44]] [INFO] P4b2BITpCf=running
[[17:41:44]] [INFO] Executing action 30/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[17:41:44]] [SUCCESS] Screenshot refreshed successfully
[[17:41:44]] [SUCCESS] Screenshot refreshed successfully
[[17:41:43]] [SUCCESS] Screenshot refreshed
[[17:41:43]] [INFO] Refreshing screenshot...
[[17:41:43]] [INFO] inrxgdWzXr=pass
[[17:41:37]] [INFO] inrxgdWzXr=running
[[17:41:37]] [INFO] Executing action 29/643: Tap on element with accessibility_id: Learn more about Zip
[[17:41:37]] [SUCCESS] Screenshot refreshed successfully
[[17:41:37]] [SUCCESS] Screenshot refreshed successfully
[[17:41:37]] [SUCCESS] Screenshot refreshed
[[17:41:37]] [INFO] Refreshing screenshot...
[[17:41:37]] [INFO] Et3kvnFdxh=pass
[[17:41:33]] [INFO] Et3kvnFdxh=running
[[17:41:33]] [INFO] Executing action 28/643: Tap on image: env[device-back-img]
[[17:41:33]] [INFO] Skipping disabled action 27/643: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[17:41:33]] [SUCCESS] Screenshot refreshed successfully
[[17:41:33]] [SUCCESS] Screenshot refreshed successfully
[[17:41:33]] [SUCCESS] Screenshot refreshed
[[17:41:33]] [INFO] Refreshing screenshot...
[[17:41:33]] [INFO] pk2DLZFBmx=pass
[[17:41:27]] [INFO] pk2DLZFBmx=running
[[17:41:27]] [INFO] Executing action 26/643: Tap on element with accessibility_id: Learn more about AfterPay
[[17:41:26]] [SUCCESS] Screenshot refreshed successfully
[[17:41:26]] [SUCCESS] Screenshot refreshed successfully
[[17:41:26]] [SUCCESS] Screenshot refreshed
[[17:41:26]] [INFO] Refreshing screenshot...
[[17:41:26]] [INFO] ShJSdXvmVL=pass
[[17:41:18]] [INFO] ShJSdXvmVL=running
[[17:41:18]] [INFO] Executing action 25/643: Swipe up till element accessibility_id: "Learn more about AfterPay" is visible
[[17:41:18]] [SUCCESS] Screenshot refreshed successfully
[[17:41:18]] [SUCCESS] Screenshot refreshed successfully
[[17:41:17]] [SUCCESS] Screenshot refreshed
[[17:41:17]] [INFO] Refreshing screenshot...
[[17:41:17]] [INFO] eShagNJmzI=pass
[[17:41:13]] [INFO] eShagNJmzI=running
[[17:41:13]] [INFO] Executing action 24/643: Wait for 3 ms
[[17:41:13]] [SUCCESS] Screenshot refreshed successfully
[[17:41:13]] [SUCCESS] Screenshot refreshed successfully
[[17:41:12]] [SUCCESS] Screenshot refreshed
[[17:41:12]] [INFO] Refreshing screenshot...
[[17:41:12]] [INFO] sHQtYzpI4s=pass
[[17:41:08]] [INFO] sHQtYzpI4s=running
[[17:41:08]] [INFO] Executing action 23/643: Tap on image: env[closebtnimage]
[[17:41:08]] [SUCCESS] Screenshot refreshed successfully
[[17:41:08]] [SUCCESS] Screenshot refreshed successfully
[[17:41:08]] [SUCCESS] Screenshot refreshed
[[17:41:08]] [INFO] Refreshing screenshot...
[[17:41:08]] [INFO] 83tV9A4NOn=pass
[[17:41:04]] [INFO] 83tV9A4NOn=running
[[17:41:04]] [INFO] Executing action 22/643: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[17:41:04]] [SUCCESS] Screenshot refreshed successfully
[[17:41:04]] [SUCCESS] Screenshot refreshed successfully
[[17:41:04]] [SUCCESS] Screenshot refreshed
[[17:41:04]] [INFO] Refreshing screenshot...
[[17:41:04]] [INFO] dCqKBG3e7u=pass
[[17:41:00]] [INFO] dCqKBG3e7u=running
[[17:41:00]] [INFO] Executing action 21/643: Tap on image: env[product-share-img]
[[17:41:00]] [SUCCESS] Screenshot refreshed successfully
[[17:41:00]] [SUCCESS] Screenshot refreshed successfully
[[17:41:00]] [SUCCESS] Screenshot refreshed
[[17:41:00]] [INFO] Refreshing screenshot...
[[17:41:00]] [INFO] kAQ1yIIw3h=pass
[[17:40:56]] [INFO] kAQ1yIIw3h=running
[[17:40:56]] [INFO] Executing action 20/643: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] with fallback: Coordinates (98, 308)
[[17:40:56]] [SUCCESS] Screenshot refreshed successfully
[[17:40:56]] [SUCCESS] Screenshot refreshed successfully
[[17:40:55]] [SUCCESS] Screenshot refreshed
[[17:40:55]] [INFO] Refreshing screenshot...
[[17:40:55]] [INFO] OmKfD9iBjD=pass
[[17:40:52]] [INFO] OmKfD9iBjD=running
[[17:40:52]] [INFO] Executing action 19/643: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[17:40:52]] [SUCCESS] Screenshot refreshed successfully
[[17:40:52]] [SUCCESS] Screenshot refreshed successfully
[[17:40:51]] [SUCCESS] Screenshot refreshed
[[17:40:51]] [INFO] Refreshing screenshot...
[[17:40:51]] [INFO] dMl1PH9Dlc=pass
[[17:40:40]] [INFO] dMl1PH9Dlc=running
[[17:40:40]] [INFO] Executing action 18/643: Wait for 10 ms
[[17:40:40]] [SUCCESS] Screenshot refreshed successfully
[[17:40:40]] [SUCCESS] Screenshot refreshed successfully
[[17:40:39]] [SUCCESS] Screenshot refreshed
[[17:40:39]] [INFO] Refreshing screenshot...
[[17:40:39]] [INFO] eHLWiRoqqS=pass
[[17:40:35]] [INFO] eHLWiRoqqS=running
[[17:40:35]] [INFO] Executing action 17/643: Swipe from (50%, 70%) to (50%, 30%)
[[17:40:35]] [SUCCESS] Screenshot refreshed successfully
[[17:40:35]] [SUCCESS] Screenshot refreshed successfully
[[17:40:35]] [SUCCESS] Screenshot refreshed
[[17:40:35]] [INFO] Refreshing screenshot...
[[17:40:35]] [INFO] huUnpMMjVR=pass
[[17:40:31]] [INFO] huUnpMMjVR=running
[[17:40:31]] [INFO] Executing action 16/643: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[17:40:30]] [SUCCESS] Screenshot refreshed successfully
[[17:40:30]] [SUCCESS] Screenshot refreshed successfully
[[17:40:30]] [SUCCESS] Screenshot refreshed
[[17:40:30]] [INFO] Refreshing screenshot...
[[17:40:30]] [INFO] XmAxcBtFI0=pass
[[17:40:27]] [INFO] XmAxcBtFI0=running
[[17:40:27]] [INFO] Executing action 15/643: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[17:40:27]] [SUCCESS] Screenshot refreshed successfully
[[17:40:27]] [SUCCESS] Screenshot refreshed successfully
[[17:40:27]] [SUCCESS] Screenshot refreshed
[[17:40:27]] [INFO] Refreshing screenshot...
[[17:40:27]] [INFO] ktAufkDJnF=pass
[[17:40:23]] [INFO] ktAufkDJnF=running
[[17:40:23]] [INFO] Executing action 14/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show (")]
[[17:40:23]] [SUCCESS] Screenshot refreshed successfully
[[17:40:23]] [SUCCESS] Screenshot refreshed successfully
[[17:40:23]] [SUCCESS] Screenshot refreshed
[[17:40:23]] [INFO] Refreshing screenshot...
[[17:40:23]] [INFO] dMl1PH9Dlc=pass
[[17:40:16]] [INFO] dMl1PH9Dlc=running
[[17:40:16]] [INFO] Executing action 13/643: Wait for 5 ms
[[17:40:16]] [SUCCESS] Screenshot refreshed successfully
[[17:40:16]] [SUCCESS] Screenshot refreshed successfully
[[17:40:16]] [SUCCESS] Screenshot refreshed
[[17:40:16]] [INFO] Refreshing screenshot...
[[17:40:16]] [INFO] a50JhCx0ir=pass
[[17:40:12]] [INFO] a50JhCx0ir=running
[[17:40:12]] [INFO] Executing action 12/643: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[17:40:12]] [SUCCESS] Screenshot refreshed successfully
[[17:40:12]] [SUCCESS] Screenshot refreshed successfully
[[17:40:12]] [SUCCESS] Screenshot refreshed
[[17:40:12]] [INFO] Refreshing screenshot...
[[17:40:12]] [INFO] Y1O1clhMSJ=pass
[[17:40:08]] [INFO] Y1O1clhMSJ=running
[[17:40:08]] [INFO] Executing action 11/643: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[17:40:08]] [SUCCESS] Screenshot refreshed successfully
[[17:40:08]] [SUCCESS] Screenshot refreshed successfully
[[17:40:07]] [SUCCESS] Screenshot refreshed
[[17:40:07]] [INFO] Refreshing screenshot...
[[17:40:07]] [INFO] lYPskZt0Ya=pass
[[17:40:04]] [INFO] lYPskZt0Ya=running
[[17:40:04]] [INFO] Executing action 10/643: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[17:40:04]] [SUCCESS] Screenshot refreshed successfully
[[17:40:04]] [SUCCESS] Screenshot refreshed successfully
[[17:40:04]] [SUCCESS] Screenshot refreshed
[[17:40:04]] [INFO] Refreshing screenshot...
[[17:40:04]] [INFO] xUbWFa8Ok2=pass
[[17:39:59]] [INFO] xUbWFa8Ok2=running
[[17:39:59]] [INFO] Executing action 9/643: Tap on Text: "Latest"
[[17:39:59]] [SUCCESS] Screenshot refreshed successfully
[[17:39:59]] [SUCCESS] Screenshot refreshed successfully
[[17:39:59]] [SUCCESS] Screenshot refreshed
[[17:39:59]] [INFO] Refreshing screenshot...
[[17:39:59]] [INFO] RbNtEW6N9T=pass
[[17:39:55]] [INFO] RbNtEW6N9T=running
[[17:39:55]] [INFO] Executing action 8/643: Tap on Text: "Toys"
[[17:39:55]] [SUCCESS] Screenshot refreshed successfully
[[17:39:55]] [SUCCESS] Screenshot refreshed successfully
[[17:39:54]] [SUCCESS] Screenshot refreshed
[[17:39:54]] [INFO] Refreshing screenshot...
[[17:39:54]] [INFO] ltDXyWvtEz=pass
[[17:39:51]] [INFO] ltDXyWvtEz=running
[[17:39:51]] [INFO] Executing action 7/643: Tap on image: env[device-back-img]
[[17:39:50]] [SUCCESS] Screenshot refreshed successfully
[[17:39:50]] [SUCCESS] Screenshot refreshed successfully
[[17:39:50]] [SUCCESS] Screenshot refreshed
[[17:39:50]] [INFO] Refreshing screenshot...
[[17:39:50]] [INFO] QPKR6jUF9O=pass
[[17:39:47]] [INFO] QPKR6jUF9O=running
[[17:39:47]] [INFO] Executing action 6/643: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[17:39:47]] [SUCCESS] Screenshot refreshed successfully
[[17:39:47]] [SUCCESS] Screenshot refreshed successfully
[[17:39:47]] [SUCCESS] Screenshot refreshed
[[17:39:47]] [INFO] Refreshing screenshot...
[[17:39:47]] [INFO] vfwUVEyq6X=pass
[[17:39:44]] [INFO] vfwUVEyq6X=running
[[17:39:44]] [INFO] Executing action 5/643: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[17:39:44]] [SUCCESS] Screenshot refreshed successfully
[[17:39:44]] [SUCCESS] Screenshot refreshed successfully
[[17:39:44]] [SUCCESS] Screenshot refreshed
[[17:39:44]] [INFO] Refreshing screenshot...
[[17:39:44]] [INFO] Xr6F8gdd8q=pass
[[17:39:40]] [INFO] Xr6F8gdd8q=running
[[17:39:40]] [INFO] Executing action 4/643: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:39:40]] [SUCCESS] Screenshot refreshed successfully
[[17:39:40]] [SUCCESS] Screenshot refreshed successfully
[[17:39:40]] [SUCCESS] Screenshot refreshed
[[17:39:40]] [INFO] Refreshing screenshot...
[[17:39:40]] [INFO] Xr6F8gdd8q=pass
[[17:39:37]] [INFO] Xr6F8gdd8q=running
[[17:39:37]] [INFO] Executing action 3/643: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[17:39:36]] [SUCCESS] Screenshot refreshed successfully
[[17:39:36]] [SUCCESS] Screenshot refreshed successfully
[[17:39:36]] [SUCCESS] Screenshot refreshed
[[17:39:36]] [INFO] Refreshing screenshot...
[[17:39:36]] [INFO] F4NGh9HrLw=pass
[[17:39:32]] [INFO] F4NGh9HrLw=running
[[17:39:32]] [INFO] Executing action 2/643: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[17:39:32]] [SUCCESS] Screenshot refreshed successfully
[[17:39:32]] [SUCCESS] Screenshot refreshed successfully
[[17:39:31]] [SUCCESS] Screenshot refreshed
[[17:39:31]] [INFO] Refreshing screenshot...
[[17:39:31]] [INFO] H9fy9qcFbZ=pass
[[17:39:27]] [INFO] H9fy9qcFbZ=running
[[17:39:27]] [INFO] Executing action 1/643: Restart app: env[appid]
[[17:39:27]] [INFO] ExecutionManager: Starting execution of 643 actions...
[[17:39:26]] [SUCCESS] Cleared 0 screenshots from database
[[17:39:26]] [INFO] Clearing screenshots from database before execution...
[[17:39:26]] [SUCCESS] All screenshots deleted successfully
[[17:39:26]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[17:39:26]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250806_173926/screenshots
[[17:39:26]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250806_173926
[[17:39:26]] [SUCCESS] Report directory initialized successfully
[[17:39:26]] [INFO] Initializing report directory and screenshots folder for test suite...
[[17:39:20]] [INFO] Collapsed all test cases
[[17:39:17]] [SUCCESS] All screenshots deleted successfully
[[17:39:17]] [INFO] All actions cleared
[[17:39:17]] [INFO] Cleaning up screenshots...
[[17:39:10]] [SUCCESS] Screenshot refreshed successfully
[[17:39:09]] [SUCCESS] Screenshot refreshed
[[17:39:09]] [INFO] Refreshing screenshot...
[[17:39:08]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[17:39:08]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[17:39:02]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[17:39:01]] [SUCCESS] Found 1 device(s)
[[17:39:00]] [INFO] Refreshing device list...
