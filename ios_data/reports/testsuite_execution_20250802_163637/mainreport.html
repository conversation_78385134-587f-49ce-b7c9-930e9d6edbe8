<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 8/2/2025, 4:37:44 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-passed">passed</span>
            <span class="stats-summary">
                <span class="passed-count">2</span> passed,
                <span class="failed-count">0</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 02/08/2025, 16:37:44
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="11 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 Woolworth1
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            11 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="woolworths.png" data-action-id="woolworths" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.woolworths.supers <span class="action-id-badge" title="Action ID: woolworths">woolworths</span>
                            </div>
                            <span class="test-step-duration">3420ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeImage[@name="tabLists"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2850ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Products"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1019ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="SearchButton"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1306ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Input text: "bread" <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">787ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Search"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">890ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeButton[@name="ProductCellActionButton"])[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">845ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="HomePageQuantitySelectorAddToCartButton"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">999ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"in Cart")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">933ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="TrolleyListRemoveProductButton"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">841ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-0-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Shopping" <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1633ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="8 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            8 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">1171ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1207ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="Screenshot.png" data-action-id="Screenshot" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Take Screenshot: "after_edit_link_click" <span class="action-id-badge" title="Action ID: Screenshot">Screenshot</span>
                            </div>
                            <span class="test-step-duration">273ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1646ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1188ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1561ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-1-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1102ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="Screenshot.png" data-action-id="Screenshot" onclick="showStepDetails('step-1-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Take Screenshot: "after_closing_health_app" <span class="action-id-badge" title="Action ID: Screenshot">Screenshot</span>
                            </div>
                            <span class="test-step-duration">392ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 02/08/2025, 16:37:44","testCases":[{"name":"Woolworth1\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            11 actions","status":"passed","steps":[{"name":"Restart app: com.woolworths.supers","status":"passed","duration":"3420ms","action_id":"woolworths","screenshot_filename":"woolworths.png","report_screenshot":"woolworths.png","resolved_screenshot":"screenshots/woolworths.png","action_id_screenshot":"screenshots/woolworths.png"},{"name":"Tap on element with xpath: //XCUIElementTypeImage[@name=\"tabLists\"]","status":"passed","duration":"2850ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Products\"]","status":"passed","duration":"1019ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"SearchButton\"]","status":"passed","duration":"1306ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Input text: \"bread\"","status":"passed","duration":"787ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Search\"]","status":"passed","duration":"890ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeButton[@name=\"ProductCellActionButton\"])[1]","status":"passed","duration":"845ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"HomePageQuantitySelectorAddToCartButton\"]","status":"passed","duration":"999ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"in Cart\")]","status":"passed","duration":"933ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"TrolleyListRemoveProductButton\"]","status":"passed","duration":"841ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Shopping\"","status":"passed","duration":"1633ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"}]},{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1171ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1207ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Take Screenshot: \"after_edit_link_click\"","status":"passed","duration":"273ms","action_id":"Screenshot","screenshot_filename":"Screenshot.png","report_screenshot":"Screenshot.png","resolved_screenshot":"screenshots/Screenshot.png","action_id_screenshot":"screenshots/Screenshot.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1646ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1188ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1561ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1102ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"Take Screenshot: \"after_closing_health_app\"","status":"passed","duration":"392ms","action_id":"Screenshot","screenshot_filename":"Screenshot.png","report_screenshot":"Screenshot.png","resolved_screenshot":"screenshots/Screenshot.png","action_id_screenshot":"screenshots/Screenshot.png"}]}],"passed":2,"failed":0,"skipped":0,"status":"passed","availableScreenshots":["0OyVUxNbaF.png","7MOUNxtPJz.png","A0IS4A1nhc.png","KFfbUGUAsp.png","PHhUKvweRf.png","SaJtvXOGlT.png","To6rgFtm9R.png","UppP3ZuqY6.png","WaakzZc6xF.png","YDCEwNh5Nm.png","after_closing_health_app.png","after_edit_link_click.png","ag29wsBP24.png","g35EwHVyF1.png","hHL72YSZHM.png","jF4jRny1iE.png","latest.png","oIAtyQB5wY.png","q1ZdKSiX2c.png","screenshot_20250802_163641.png","screenshot_20250802_163646.png","screenshot_20250802_163648.png","screenshot_20250802_163652.png","screenshot_20250802_163654.png","screenshot_20250802_163657.png","screenshot_20250802_163659.png","screenshot_20250802_163702.png","screenshot_20250802_163705.png","screenshot_20250802_163707.png","screenshot_20250802_163708.png","screenshot_20250802_163711.png","screenshot_20250802_163724.png","screenshot_20250802_163727.png","screenshot_20250802_163729.png","screenshot_20250802_163732.png","screenshot_20250802_163735.png","screenshot_20250802_163738.png","screenshot_20250802_163741.png","screenshot_20250802_163743.png","vwjicir8ov.png","yHeSvvpOZo.png","yL81dtqigl.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>