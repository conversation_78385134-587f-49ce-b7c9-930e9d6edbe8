Action Log - 2025-08-13 09:27:35
================================================================================

[[09:27:35]] [INFO] Generating execution report...
[[09:27:35]] [WARNING] 2 tests failed.
[[09:27:35]] [INFO] Skipping remaining steps in failed test case (moving from action 8 to next test case at 13)
[[09:27:35]] [ERROR] Action 8 failed: No device connected
[[09:27:35]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[09:27:33]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest.json (Attempt 3 of 3) ===
[[09:27:33]] [ERROR] Action 8 failed: No device connected
[[09:27:33]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[09:27:31]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest.json (Attempt 2 of 3) ===
[[09:27:31]] [ERROR] Error executing action 8: Failed to fetch
[[09:27:21]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[09:27:21]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 7)
[[09:27:21]] [ERROR] Error executing action 1: Failed to fetch
[[09:27:21]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[09:27:19]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 3 of 3) ===
[[09:27:19]] [ERROR] Error executing action 2: Failed to fetch
[[09:21:23]] [SUCCESS] Screenshot refreshed successfully
[[09:21:23]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[09:21:22]] [SUCCESS] Screenshot refreshed
[[09:21:22]] [INFO] Refreshing screenshot...
[[09:21:20]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[09:21:18]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 2 of 3) ===
[[09:21:18]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[09:20:45]] [SUCCESS] Screenshot refreshed successfully
[[09:20:44]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[09:20:44]] [SUCCESS] Screenshot refreshed
[[09:20:44]] [INFO] Refreshing screenshot...
[[09:20:41]] [SUCCESS] Screenshot refreshed successfully
[[09:20:40]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[09:20:40]] [SUCCESS] Screenshot refreshed
[[09:20:40]] [INFO] Refreshing screenshot...
[[09:20:37]] [SUCCESS] Screenshot refreshed successfully
[[09:20:37]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[09:20:37]] [SUCCESS] Screenshot refreshed
[[09:20:37]] [INFO] Refreshing screenshot...
[[09:20:35]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[09:20:35]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[09:20:35]] [SUCCESS] Cleared 0 screenshots from database
[[09:20:35]] [INFO] Clearing screenshots from database before execution...
[[09:20:35]] [SUCCESS] All screenshots deleted successfully
[[09:20:35]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:20:35]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_092035/screenshots
[[09:20:35]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_092035
[[09:20:35]] [SUCCESS] Report directory initialized successfully
[[09:20:35]] [INFO] Initializing report directory and screenshots folder for test suite...
[[09:20:28]] [SUCCESS] All screenshots deleted successfully
[[09:20:28]] [INFO] All actions cleared
[[09:20:28]] [INFO] Cleaning up screenshots...
[[09:20:20]] [SUCCESS] Screenshot refreshed successfully
[[09:20:19]] [SUCCESS] Screenshot refreshed
[[09:20:19]] [INFO] Refreshing screenshot...
[[09:20:18]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[09:20:18]] [INFO] Device info updated: RMX2151
[[09:20:10]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[09:20:07]] [SUCCESS] Found 1 device(s)
[[09:20:07]] [INFO] Refreshing device list...
