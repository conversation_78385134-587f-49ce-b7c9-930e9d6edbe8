{"name": "UI Execution 13/08/2025, 09:27:35", "testCases": [{"name": "Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "failed", "duration": "602ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "unknown", "duration": "538ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/placeholder_report.png", "action_id_screenshot": "screenshots/placeholder_report.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "1683ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/placeholder_report.png", "action_id_screenshot": "screenshots/placeholder_report.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]", "status": "unknown", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "ImageButto", "prefixed_action_id": "al_ImageButto", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/ImageButto.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "Scientific", "prefixed_action_id": "al_Scientific", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/Scientific.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Execute Test Case: Calc-subtest (3 steps) [CLEANUP]", "status": "unknown", "duration": "0ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/placeholder_report.png", "action_id_screenshot": "screenshots/placeholder_report.png"}]}, {"name": "Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "failed", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "unknown", "duration": "0ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/placeholder_report.png", "action_id_screenshot": "screenshots/placeholder_report.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "unknown", "duration": "0ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/placeholder_report.png", "action_id_screenshot": "screenshots/placeholder_report.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]", "status": "unknown", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "ImageButto", "prefixed_action_id": "al_ImageButto", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/ImageButto.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "Scientific", "prefixed_action_id": "al_Scientific", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/Scientific.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-13 09:27:35", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}]}], "passed": 0, "failed": 2, "skipped": 0, "status": "failed", "id": "615e32f8-9d02-450d-b2f0-a704f968e75d"}