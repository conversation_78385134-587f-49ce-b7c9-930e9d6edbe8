Action Log - 2025-08-13 08:04:24
================================================================================

[[08:04:23]] [INFO] Generating execution report...
[[08:04:23]] [WARNING] 2 tests failed.
[[08:04:23]] [INFO] Skipping remaining steps in failed test case (moving from action 8 to next test case at 13)
[[08:04:23]] [ERROR] Action 8 failed: No device connected
[[08:04:23]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[08:04:16]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest.json (Attempt 2 of 2) ===
[[08:04:16]] [ERROR] Error executing action 8: Failed to fetch
[[08:04:05]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[08:04:05]] [INFO] Skipping remaining steps in failed test case (moving from action 3 to next test case at 7)
[[08:04:05]] [ERROR] Error executing action 3: Failed to fetch
[[07:56:21]] [SUCCESS] Screenshot refreshed successfully
[[07:56:21]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[07:56:21]] [SUCCESS] Screenshot refreshed
[[07:56:21]] [INFO] Refreshing screenshot...
[[07:56:18]] [SUCCESS] Screenshot refreshed successfully
[[07:56:18]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[07:56:18]] [SUCCESS] Screenshot refreshed
[[07:56:18]] [INFO] Refreshing screenshot...
[[07:56:16]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[07:56:14]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 2 of 2) ===
[[07:56:14]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[07:53:01]] [SUCCESS] Screenshot refreshed successfully
[[07:53:01]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[07:53:00]] [SUCCESS] Screenshot refreshed
[[07:53:00]] [INFO] Refreshing screenshot...
[[07:52:57]] [SUCCESS] Screenshot refreshed successfully
[[07:52:57]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[07:52:56]] [SUCCESS] Screenshot refreshed
[[07:52:56]] [INFO] Refreshing screenshot...
[[07:52:54]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[07:52:54]] [SUCCESS] Screenshot refreshed successfully
[[07:52:53]] [SUCCESS] Screenshot refreshed
[[07:52:53]] [INFO] Refreshing screenshot...
[[07:52:43]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[07:52:43]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[07:52:43]] [SUCCESS] Cleared 0 screenshots from database
[[07:52:43]] [INFO] Clearing screenshots from database before execution...
[[07:52:43]] [SUCCESS] All screenshots deleted successfully
[[07:52:43]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[07:52:43]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_075243/screenshots
[[07:52:43]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_075243
[[07:52:43]] [SUCCESS] Report directory initialized successfully
[[07:52:43]] [INFO] Initializing report directory and screenshots folder for test suite...
[[07:52:36]] [SUCCESS] All screenshots deleted successfully
[[07:52:36]] [INFO] All actions cleared
[[07:52:36]] [INFO] Cleaning up screenshots...
[[07:52:13]] [SUCCESS] Opened Appium Web Inspector with pre-filled connection details.
[[07:52:13]] [INFO] Opening URL: http://localhost:4724/inspector?host=127.0.0.1&port=4724&path=/wd/hub/
[[07:52:13]] [INFO] Found active session ID: 1eead9f8-9b1d-4d8e-a28c-8d2e411694d6. Inspector should list this session under 'Attach to Session'.
[[07:52:13]] [INFO] Opening Appium Web Inspector in a new window...
[[07:52:13]] [INFO] Checking Appium Web Inspector availability...
[[07:52:12]] [SUCCESS] Screenshot refreshed successfully
[[07:52:11]] [SUCCESS] Screenshot refreshed
[[07:52:11]] [INFO] Refreshing screenshot...
[[07:52:10]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[07:52:10]] [INFO] Device info updated: RMX2151
[[07:52:02]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[07:52:00]] [SUCCESS] Found 1 device(s)
[[07:52:00]] [INFO] Refreshing device list...
