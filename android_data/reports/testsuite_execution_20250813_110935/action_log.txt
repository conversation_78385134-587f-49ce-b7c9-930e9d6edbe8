Action Log - 2025-08-13 11:14:41
================================================================================

[[11:14:41]] [INFO] Generating execution report...
[[11:14:41]] [WARNING] 1 test failed.
[[11:14:40]] [SUCCESS] Screenshot refreshed
[[11:14:40]] [INFO] Refreshing screenshot...
[[11:14:38]] [SUCCESS] Screenshot refreshed successfully
[[11:14:38]] [INFO] Executing action 13/13: Terminate app: com.coloros.calculator
[[11:14:37]] [SUCCESS] Screenshot refreshed
[[11:14:37]] [INFO] Refreshing screenshot...
[[11:14:30]] [SUCCESS] Screenshot refreshed successfully
[[11:14:30]] [INFO] Executing action 12/13: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[11:14:30]] [SUCCESS] Screenshot refreshed
[[11:14:30]] [INFO] Refreshing screenshot...
[[11:14:28]] [SUCCESS] Screenshot refreshed successfully
[[11:14:28]] [INFO] Executing action 11/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"]
[[11:14:27]] [SUCCESS] Screenshot refreshed
[[11:14:27]] [INFO] Refreshing screenshot...
[[11:14:24]] [SUCCESS] Screenshot refreshed successfully
[[11:14:23]] [INFO] Executing action 10/13: Tap on Text: "Settings"
[[11:14:23]] [SUCCESS] Screenshot refreshed
[[11:14:23]] [INFO] Refreshing screenshot...
[[11:14:21]] [SUCCESS] Screenshot refreshed successfully
[[11:14:21]] [INFO] Executing action 9/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[11:14:20]] [SUCCESS] Screenshot refreshed
[[11:14:20]] [INFO] Refreshing screenshot...
[[11:14:08]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[11:14:08]] [INFO] Skipping remaining steps in failed test case (moving from action 4 to next test case at 7)
[[11:14:08]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[11:12:20]] [SUCCESS] Screenshot refreshed successfully
[[11:12:20]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[11:12:19]] [SUCCESS] Screenshot refreshed
[[11:12:19]] [INFO] Refreshing screenshot...
[[11:12:16]] [SUCCESS] Screenshot refreshed successfully
[[11:12:16]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[11:12:15]] [SUCCESS] Screenshot refreshed
[[11:12:15]] [INFO] Refreshing screenshot...
[[11:12:13]] [SUCCESS] Screenshot refreshed successfully
[[11:12:13]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[11:12:12]] [SUCCESS] Screenshot refreshed
[[11:12:12]] [INFO] Refreshing screenshot...
[[11:12:10]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[11:12:08]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 2 of 2) ===
[[11:12:08]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[11:10:07]] [SUCCESS] Screenshot refreshed successfully
[[11:10:07]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[11:10:07]] [SUCCESS] Screenshot refreshed
[[11:10:07]] [INFO] Refreshing screenshot...
[[11:10:03]] [SUCCESS] Screenshot refreshed successfully
[[11:10:03]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[11:10:02]] [SUCCESS] Screenshot refreshed
[[11:10:02]] [INFO] Refreshing screenshot...
[[11:09:37]] [SUCCESS] Screenshot refreshed successfully
[[11:09:37]] [SUCCESS] Screenshot refreshed successfully
[[11:09:37]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[11:09:36]] [SUCCESS] Screenshot refreshed
[[11:09:36]] [INFO] Refreshing screenshot...
[[11:09:35]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[11:09:35]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[11:09:35]] [SUCCESS] Cleared 0 screenshots from database
[[11:09:35]] [INFO] Clearing screenshots from database before execution...
[[11:09:35]] [SUCCESS] All screenshots deleted successfully
[[11:09:35]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[11:09:35]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_110935/screenshots
[[11:09:35]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_110935
[[11:09:35]] [SUCCESS] Report directory initialized successfully
[[11:09:35]] [INFO] Initializing report directory and screenshots folder for test suite...
[[11:09:25]] [SUCCESS] All screenshots deleted successfully
[[11:09:25]] [INFO] All actions cleared
[[11:09:25]] [INFO] Cleaning up screenshots...
[[11:09:12]] [SUCCESS] Screenshot refreshed successfully
[[11:09:11]] [SUCCESS] Screenshot refreshed
[[11:09:11]] [INFO] Refreshing screenshot...
[[11:09:10]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[11:09:10]] [INFO] Device info updated: RMX2151
[[11:09:05]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[11:08:56]] [SUCCESS] Found 1 device(s)
[[11:08:56]] [INFO] Refreshing device list...
