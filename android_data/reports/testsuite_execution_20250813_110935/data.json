{"name": "UI Execution 13/08/2025, 11:14:41", "testCases": [{"name": "Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "246ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "384ms", "action_id": "1HaNzOIxlr", "screenshot_filename": "1HaNzOIxlr.png", "report_screenshot": "1HaNzOIxlr.png", "resolved_screenshot": "screenshots/1HaNzOIxlr.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/1HaNzOIxlr.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "1656ms", "action_id": "68214Atpzp", "screenshot_filename": "68214Atpzp.png", "report_screenshot": "68214Atpzp.png", "resolved_screenshot": "screenshots/68214Atpzp.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/68214Atpzp.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]", "status": "failed", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Execute Test Case: Calc-subtest (3 steps) [CLEANUP]", "status": "unknown", "duration": "0ms", "action_id": "Y7p6jE0gtT", "screenshot_filename": "Y7p6jE0gtT.png", "report_screenshot": "Y7p6jE0gtT.png", "resolved_screenshot": "screenshots/Y7p6jE0gtT.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/Y7p6jE0gtT.png"}]}, {"name": "Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "passed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "228ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "320ms", "action_id": "1HaNzOIxlr", "screenshot_filename": "1HaNzOIxlr.png", "report_screenshot": "1HaNzOIxlr.png", "resolved_screenshot": "screenshots/1HaNzOIxlr.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/1HaNzOIxlr.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "1706ms", "action_id": "68214Atpzp", "screenshot_filename": "68214Atpzp.png", "report_screenshot": "68214Atpzp.png", "resolved_screenshot": "screenshots/68214Atpzp.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/68214Atpzp.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]", "status": "passed", "duration": "205ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "passed", "duration": "4934ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "passed", "duration": "465ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}]}], "passed": 1, "failed": 1, "skipped": 0, "status": "failed"}