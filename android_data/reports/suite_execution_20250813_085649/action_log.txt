Action Log - 2025-08-13 08:56:49
================================================================================

[[08:56:49]] [INFO] Generating execution report...
[[08:56:49]] [WARNING] 2 tests failed.
[[08:56:49]] [INFO] Skipping remaining steps in failed test case (moving from action 8 to next test case at 13)
[[08:56:49]] [ERROR] Action 8 failed: No device connected
[[08:56:49]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[08:56:47]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest.json (Attempt 3 of 3) ===
[[08:56:47]] [ERROR] Action 8 failed: No device connected
[[08:56:47]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[08:56:45]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest.json (Attempt 2 of 3) ===
[[08:56:45]] [ERROR] Error executing action 8: Failed to fetch
[[08:56:35]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[08:56:35]] [INFO] Skipping remaining steps in failed test case (moving from action 1 to next test case at 7)
[[08:56:35]] [ERROR] Error executing action 1: Failed to fetch
[[08:56:35]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[08:56:33]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 3 of 3) ===
[[08:56:33]] [ERROR] Error executing action 4: Failed to fetch
[[08:51:33]] [SUCCESS] Screenshot refreshed successfully
[[08:51:33]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[08:51:32]] [SUCCESS] Screenshot refreshed
[[08:51:32]] [INFO] Refreshing screenshot...
[[08:51:29]] [SUCCESS] Screenshot refreshed successfully
[[08:51:28]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[08:51:28]] [SUCCESS] Screenshot refreshed
[[08:51:28]] [INFO] Refreshing screenshot...
[[08:51:26]] [SUCCESS] Screenshot refreshed successfully
[[08:51:26]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[08:51:25]] [SUCCESS] Screenshot refreshed
[[08:51:25]] [INFO] Refreshing screenshot...
[[08:51:23]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[08:51:21]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 2 of 3) ===
[[08:51:21]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[08:50:53]] [SUCCESS] Screenshot refreshed successfully
[[08:50:53]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[08:50:52]] [SUCCESS] Screenshot refreshed
[[08:50:52]] [INFO] Refreshing screenshot...
[[08:50:49]] [SUCCESS] Screenshot refreshed successfully
[[08:50:49]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[08:50:48]] [SUCCESS] Screenshot refreshed
[[08:50:48]] [INFO] Refreshing screenshot...
[[08:50:46]] [SUCCESS] Screenshot refreshed successfully
[[08:50:46]] [SUCCESS] Screenshot refreshed successfully
[[08:50:46]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[08:50:45]] [SUCCESS] Screenshot refreshed
[[08:50:45]] [INFO] Refreshing screenshot...
[[08:50:43]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[08:50:43]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[08:50:43]] [SUCCESS] Cleared 0 screenshots from database
[[08:50:43]] [INFO] Clearing screenshots from database before execution...
[[08:50:43]] [SUCCESS] All screenshots deleted successfully
[[08:50:43]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[08:50:43]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_085043/screenshots
[[08:50:43]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_085043
[[08:50:43]] [SUCCESS] Report directory initialized successfully
[[08:50:43]] [INFO] Initializing report directory and screenshots folder for test suite...
[[08:50:35]] [SUCCESS] All screenshots deleted successfully
[[08:50:35]] [INFO] All actions cleared
[[08:50:35]] [INFO] Cleaning up screenshots...
[[08:50:34]] [SUCCESS] Screenshot refreshed successfully
[[08:50:33]] [SUCCESS] Screenshot refreshed
[[08:50:33]] [INFO] Refreshing screenshot...
[[08:50:32]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[08:50:32]] [INFO] Device info updated: RMX2151
[[08:50:23]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[08:50:21]] [SUCCESS] Found 1 device(s)
[[08:50:21]] [INFO] Refreshing device list...
