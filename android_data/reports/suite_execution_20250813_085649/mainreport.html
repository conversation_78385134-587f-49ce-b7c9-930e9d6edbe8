<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 8/13/2025, 8:56:49 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">0</span> passed,
                <span class="failed-count">2</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 13/08/2025, 08:56:49
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 Calc-AndroidTest-Extra-Steps
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">615ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">393ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">1648ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Calc-subtest (3 steps) [CLEANUP] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="6 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #2 Calc-AndroidTest
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            6 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 13/08/2025, 08:56:49","testCases":[{"name":"Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Restart app: com.coloros.calculator","status":"failed","duration":"615ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"393ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/placeholder_report.png","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"1648ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/placeholder_report.png","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]","status":"unknown","duration":"0ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","clean_action_id":"ImageButto","prefixed_action_id":"al_ImageButto","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"unknown","duration":"0ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","clean_action_id":"Scientific","prefixed_action_id":"al_Scientific","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"unknown","duration":"0ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Execute Test Case: Calc-subtest (3 steps) [CLEANUP]","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/placeholder_report.png","action_id_screenshot":"screenshots/placeholder_report.png"}]},{"name":"Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions","status":"failed","steps":[{"name":"Restart app: com.coloros.calculator","status":"failed","duration":"0ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/placeholder_report.png","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on Text: \"Settings\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/placeholder_report.png","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]","status":"unknown","duration":"0ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","clean_action_id":"ImageButto","prefixed_action_id":"al_ImageButto","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"unknown","duration":"0ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","clean_action_id":"Scientific","prefixed_action_id":"al_Scientific","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"unknown","duration":"0ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-13 08:56:49","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"}]}],"passed":0,"failed":2,"skipped":0,"status":"failed","id":"8ab6e458-fd90-46a8-b162-ccf6a5fd50c9","availableScreenshots":["ImageButto.png","Scientific.png","calculator.png","placeholder_report.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>