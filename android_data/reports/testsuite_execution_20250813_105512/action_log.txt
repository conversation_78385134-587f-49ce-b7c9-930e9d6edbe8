Action Log - 2025-08-13 10:59:41
================================================================================

[[10:59:41]] [INFO] Generating execution report...
[[10:59:41]] [WARNING] 1 test failed.
[[10:59:40]] [SUCCESS] Screenshot refreshed
[[10:59:40]] [INFO] Refreshing screenshot...
[[10:59:38]] [SUCCESS] Screenshot refreshed successfully
[[10:59:38]] [INFO] Executing action 13/13: Terminate app: com.coloros.calculator
[[10:59:37]] [SUCCESS] Screenshot refreshed
[[10:59:37]] [INFO] Refreshing screenshot...
[[10:59:31]] [SUCCESS] Screenshot refreshed successfully
[[10:59:31]] [INFO] Executing action 12/13: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[10:59:30]] [SUCCESS] Screenshot refreshed
[[10:59:30]] [INFO] Refreshing screenshot...
[[10:59:28]] [SUCCESS] Screenshot refreshed successfully
[[10:59:28]] [INFO] Executing action 11/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"]
[[10:59:28]] [SUCCESS] Screenshot refreshed
[[10:59:28]] [INFO] Refreshing screenshot...
[[10:59:24]] [SUCCESS] Screenshot refreshed successfully
[[10:59:24]] [INFO] Executing action 10/13: Tap on Text: "Settings"
[[10:59:23]] [SUCCESS] Screenshot refreshed
[[10:59:23]] [INFO] Refreshing screenshot...
[[10:59:21]] [SUCCESS] Screenshot refreshed successfully
[[10:59:21]] [INFO] Executing action 9/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[10:59:21]] [SUCCESS] Screenshot refreshed
[[10:59:21]] [INFO] Refreshing screenshot...
[[10:59:09]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[10:59:09]] [INFO] Skipping remaining steps in failed test case (moving from action 4 to next test case at 7)
[[10:59:09]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[10:57:20]] [SUCCESS] Screenshot refreshed successfully
[[10:57:20]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[10:57:20]] [SUCCESS] Screenshot refreshed
[[10:57:20]] [INFO] Refreshing screenshot...
[[10:57:16]] [SUCCESS] Screenshot refreshed successfully
[[10:57:16]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[10:57:15]] [SUCCESS] Screenshot refreshed
[[10:57:15]] [INFO] Refreshing screenshot...
[[10:57:13]] [SUCCESS] Screenshot refreshed successfully
[[10:57:13]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[10:57:13]] [SUCCESS] Screenshot refreshed
[[10:57:13]] [INFO] Refreshing screenshot...
[[10:57:11]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[10:57:09]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 2 of 2) ===
[[10:57:09]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[10:55:22]] [SUCCESS] Screenshot refreshed successfully
[[10:55:21]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[10:55:21]] [SUCCESS] Screenshot refreshed
[[10:55:21]] [INFO] Refreshing screenshot...
[[10:55:17]] [SUCCESS] Screenshot refreshed successfully
[[10:55:17]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[10:55:17]] [SUCCESS] Screenshot refreshed
[[10:55:17]] [INFO] Refreshing screenshot...
[[10:55:14]] [SUCCESS] Screenshot refreshed successfully
[[10:55:14]] [SUCCESS] Screenshot refreshed successfully
[[10:55:14]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[10:55:14]] [SUCCESS] Screenshot refreshed
[[10:55:14]] [INFO] Refreshing screenshot...
[[10:55:12]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[10:55:12]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[10:55:12]] [SUCCESS] Cleared 0 screenshots from database
[[10:55:12]] [INFO] Clearing screenshots from database before execution...
[[10:55:12]] [SUCCESS] All screenshots deleted successfully
[[10:55:12]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:55:12]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_105512/screenshots
[[10:55:12]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_105512
[[10:55:12]] [SUCCESS] Report directory initialized successfully
[[10:55:12]] [INFO] Initializing report directory and screenshots folder for test suite...
[[10:55:07]] [SUCCESS] All screenshots deleted successfully
[[10:55:07]] [INFO] All actions cleared
[[10:55:07]] [INFO] Cleaning up screenshots...
[[10:54:53]] [SUCCESS] Screenshot refreshed successfully
[[10:54:53]] [SUCCESS] Screenshot refreshed
[[10:54:53]] [INFO] Refreshing screenshot...
[[10:54:52]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[10:54:52]] [INFO] Device info updated: RMX2151
[[10:54:44]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[10:54:42]] [SUCCESS] Found 1 device(s)
[[10:54:42]] [INFO] Refreshing device list...
