{"name": "UI Execution 13/08/2025, 10:59:41", "testCases": [{"name": "Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "231ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "323ms", "action_id": "4TWBEZjIHA", "screenshot_filename": "4TWBEZjIHA.png", "report_screenshot": "4TWBEZjIHA.png", "resolved_screenshot": "screenshots/4TWBEZjIHA.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/4TWBEZjIHA.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "1656ms", "action_id": "4xkJFme1G6", "screenshot_filename": "4xkJFme1G6.png", "report_screenshot": "4xkJFme1G6.png", "resolved_screenshot": "screenshots/4xkJFme1G6.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/4xkJFme1G6.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]", "status": "failed", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Execute Test Case: Calc-subtest (3 steps) [CLEANUP]", "status": "unknown", "duration": "0ms", "action_id": "E2TDIa1jIs", "screenshot_filename": "E2TDIa1jIs.png", "report_screenshot": "E2TDIa1jIs.png", "resolved_screenshot": "screenshots/E2TDIa1jIs.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/E2TDIa1jIs.png"}]}, {"name": "Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "passed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "237ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "345ms", "action_id": "4TWBEZjIHA", "screenshot_filename": "4TWBEZjIHA.png", "report_screenshot": "4TWBEZjIHA.png", "resolved_screenshot": "screenshots/4TWBEZjIHA.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/4TWBEZjIHA.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "1700ms", "action_id": "4xkJFme1G6", "screenshot_filename": "4xkJFme1G6.png", "report_screenshot": "4xkJFme1G6.png", "resolved_screenshot": "screenshots/4xkJFme1G6.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/4xkJFme1G6.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]", "status": "passed", "duration": "192ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "passed", "duration": "4728ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "passed", "duration": "425ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}]}], "passed": 1, "failed": 1, "skipped": 0, "status": "failed"}