<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 8/13/2025, 10:59:41 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 13/08/2025, 10:59:41
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 Calc-AndroidTest-Extra-Steps
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">231ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="4TWBEZjIHA.png" data-action-id="4TWBEZjIHA" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: 4TWBEZjIHA">4TWBEZjIHA</span>
                            </div>
                            <span class="test-step-duration">323ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="4xkJFme1G6.png" data-action-id="4xkJFme1G6" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: 4xkJFme1G6">4xkJFme1G6</span>
                            </div>
                            <span class="test-step-duration">1656ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="failed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="E2TDIa1jIs.png" data-action-id="E2TDIa1jIs" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Calc-subtest (3 steps) [CLEANUP] <span class="action-id-badge" title="Action ID: E2TDIa1jIs">E2TDIa1jIs</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="6 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 Calc-AndroidTest
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            6 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">237ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="4TWBEZjIHA.png" data-action-id="4TWBEZjIHA" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: 4TWBEZjIHA">4TWBEZjIHA</span>
                            </div>
                            <span class="test-step-duration">345ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="4xkJFme1G6.png" data-action-id="4xkJFme1G6" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: 4xkJFme1G6">4xkJFme1G6</span>
                            </div>
                            <span class="test-step-duration">1700ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">192ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">4728ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">425ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 13/08/2025, 10:59:41","testCases":[{"name":"Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"231ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"323ms","action_id":"4TWBEZjIHA","screenshot_filename":"4TWBEZjIHA.png","report_screenshot":"4TWBEZjIHA.png","resolved_screenshot":"screenshots/4TWBEZjIHA.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/4TWBEZjIHA.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"1656ms","action_id":"4xkJFme1G6","screenshot_filename":"4xkJFme1G6.png","report_screenshot":"4xkJFme1G6.png","resolved_screenshot":"screenshots/4xkJFme1G6.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/4xkJFme1G6.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]","status":"failed","duration":"0ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"unknown","duration":"0ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"unknown","duration":"0ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Execute Test Case: Calc-subtest (3 steps) [CLEANUP]","status":"unknown","duration":"0ms","action_id":"E2TDIa1jIs","screenshot_filename":"E2TDIa1jIs.png","report_screenshot":"E2TDIa1jIs.png","resolved_screenshot":"screenshots/E2TDIa1jIs.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/E2TDIa1jIs.png"}]},{"name":"Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions","status":"passed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"237ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"345ms","action_id":"4TWBEZjIHA","screenshot_filename":"4TWBEZjIHA.png","report_screenshot":"4TWBEZjIHA.png","resolved_screenshot":"screenshots/4TWBEZjIHA.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/4TWBEZjIHA.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"1700ms","action_id":"4xkJFme1G6","screenshot_filename":"4xkJFme1G6.png","report_screenshot":"4xkJFme1G6.png","resolved_screenshot":"screenshots/4xkJFme1G6.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/4xkJFme1G6.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]","status":"passed","duration":"192ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"passed","duration":"4728ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"passed","duration":"425ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"}]}],"passed":1,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["0OV5Q6wTvz.png","4TWBEZjIHA.png","4xkJFme1G6.png","CVSJfs84Il.png","E1vFW6LSVk.png","E23QM28CRK.png","E2TDIa1jIs.png","HBOzxlfPjI.png","LaGiSDt1n5.png","NlshBuAjCk.png","VZqlvBFahC.png","ZA25tKtBZH.png","iTBm42IY48.png","ukF1giid3f.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>