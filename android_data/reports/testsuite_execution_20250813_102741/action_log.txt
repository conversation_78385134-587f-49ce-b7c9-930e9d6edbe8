Action Log - 2025-08-13 10:31:40
================================================================================

[[10:31:40]] [INFO] Generating execution report...
[[10:31:40]] [WARNING] 1 test failed.
[[10:31:40]] [SUCCESS] Screenshot refreshed
[[10:31:40]] [INFO] Refreshing screenshot...
[[10:31:37]] [SUCCESS] Screenshot refreshed successfully
[[10:31:37]] [INFO] Executing action 13/13: Terminate app: com.coloros.calculator
[[10:31:37]] [SUCCESS] Screenshot refreshed
[[10:31:37]] [INFO] Refreshing screenshot...
[[10:31:31]] [SUCCESS] Screenshot refreshed successfully
[[10:31:31]] [INFO] Executing action 12/13: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[10:31:30]] [SUCCESS] Screenshot refreshed
[[10:31:30]] [INFO] Refreshing screenshot...
[[10:31:28]] [SUCCESS] Screenshot refreshed successfully
[[10:31:28]] [INFO] Executing action 11/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"]
[[10:31:27]] [SUCCESS] Screenshot refreshed
[[10:31:27]] [INFO] Refreshing screenshot...
[[10:31:24]] [SUCCESS] Screenshot refreshed successfully
[[10:31:24]] [INFO] Executing action 10/13: Tap on Text: "Settings"
[[10:31:23]] [SUCCESS] Screenshot refreshed
[[10:31:23]] [INFO] Refreshing screenshot...
[[10:31:21]] [INFO] Executing action 9/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[10:31:21]] [SUCCESS] Screenshot refreshed successfully
[[10:31:21]] [SUCCESS] Screenshot refreshed
[[10:31:21]] [INFO] Refreshing screenshot...
[[10:31:09]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[10:31:09]] [INFO] Skipping remaining steps in failed test case (moving from action 4 to next test case at 7)
[[10:31:09]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[10:29:54]] [SUCCESS] Screenshot refreshed successfully
[[10:29:54]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[10:29:53]] [SUCCESS] Screenshot refreshed
[[10:29:53]] [INFO] Refreshing screenshot...
[[10:29:50]] [SUCCESS] Screenshot refreshed successfully
[[10:29:49]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[10:29:49]] [SUCCESS] Screenshot refreshed
[[10:29:49]] [INFO] Refreshing screenshot...
[[10:29:47]] [SUCCESS] Screenshot refreshed successfully
[[10:29:47]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[10:29:46]] [SUCCESS] Screenshot refreshed
[[10:29:46]] [INFO] Refreshing screenshot...
[[10:29:44]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[10:29:42]] [INFO] === RETRYING TEST CASE: Calc-AndroidTest-Extra-Steps.json (Attempt 2 of 2) ===
[[10:29:42]] [ERROR] Action 4 failed: Element not found or not tappable: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[10:27:51]] [SUCCESS] Screenshot refreshed successfully
[[10:27:51]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[10:27:50]] [SUCCESS] Screenshot refreshed
[[10:27:50]] [INFO] Refreshing screenshot...
[[10:27:47]] [SUCCESS] Screenshot refreshed successfully
[[10:27:46]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[10:27:46]] [SUCCESS] Screenshot refreshed
[[10:27:46]] [INFO] Refreshing screenshot...
[[10:27:43]] [SUCCESS] Screenshot refreshed successfully
[[10:27:43]] [SUCCESS] Screenshot refreshed successfully
[[10:27:43]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[10:27:43]] [SUCCESS] Screenshot refreshed
[[10:27:43]] [INFO] Refreshing screenshot...
[[10:27:41]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[10:27:41]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[10:27:41]] [SUCCESS] Cleared 0 screenshots from database
[[10:27:41]] [INFO] Clearing screenshots from database before execution...
[[10:27:41]] [SUCCESS] All screenshots deleted successfully
[[10:27:41]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:27:41]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_102741/screenshots
[[10:27:41]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250813_102741
[[10:27:41]] [SUCCESS] Report directory initialized successfully
[[10:27:41]] [INFO] Initializing report directory and screenshots folder for test suite...
[[10:27:36]] [SUCCESS] All screenshots deleted successfully
[[10:27:36]] [INFO] All actions cleared
[[10:27:36]] [INFO] Cleaning up screenshots...
[[10:27:29]] [SUCCESS] Screenshot refreshed successfully
[[10:27:28]] [SUCCESS] Screenshot refreshed
[[10:27:28]] [INFO] Refreshing screenshot...
[[10:27:27]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[10:27:27]] [INFO] Device info updated: RMX2151
[[10:27:18]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[10:27:16]] [SUCCESS] Found 1 device(s)
[[10:27:15]] [INFO] Refreshing device list...
