<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 8/13/2025, 10:31:40 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 13/08/2025, 10:31:40
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 Calc-AndroidTest-Extra-Steps
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">225ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="PnNUuO9G41.png" data-action-id="PnNUuO9G41" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: PnNUuO9G41">PnNUuO9G41</span>
                            </div>
                            <span class="test-step-duration">373ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="Sz1LJ3uziS.png" data-action-id="Sz1LJ3uziS" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: Sz1LJ3uziS">Sz1LJ3uziS</span>
                            </div>
                            <span class="test-step-duration">1734ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="failed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="dGQDCfNGhv.png" data-action-id="dGQDCfNGhv" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Execute Test Case: Calc-subtest (3 steps) [CLEANUP] <span class="action-id-badge" title="Action ID: dGQDCfNGhv">dGQDCfNGhv</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="6 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 Calc-AndroidTest
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            6 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">214ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="PnNUuO9G41.png" data-action-id="PnNUuO9G41" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: PnNUuO9G41">PnNUuO9G41</span>
                            </div>
                            <span class="test-step-duration">313ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="Sz1LJ3uziS.png" data-action-id="Sz1LJ3uziS" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: Sz1LJ3uziS">Sz1LJ3uziS</span>
                            </div>
                            <span class="test-step-duration">1723ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">184ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">3987ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">472ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 13/08/2025, 10:31:40","testCases":[{"name":"Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"225ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"373ms","action_id":"PnNUuO9G41","screenshot_filename":"PnNUuO9G41.png","report_screenshot":"PnNUuO9G41.png","resolved_screenshot":"screenshots/PnNUuO9G41.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/PnNUuO9G41.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"1734ms","action_id":"Sz1LJ3uziS","screenshot_filename":"Sz1LJ3uziS.png","report_screenshot":"Sz1LJ3uziS.png","resolved_screenshot":"screenshots/Sz1LJ3uziS.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/Sz1LJ3uziS.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]","status":"failed","duration":"0ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"unknown","duration":"0ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"unknown","duration":"0ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Execute Test Case: Calc-subtest (3 steps) [CLEANUP]","status":"unknown","duration":"0ms","action_id":"dGQDCfNGhv","screenshot_filename":"dGQDCfNGhv.png","report_screenshot":"dGQDCfNGhv.png","resolved_screenshot":"screenshots/dGQDCfNGhv.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/dGQDCfNGhv.png"}]},{"name":"Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions","status":"passed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"214ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"313ms","action_id":"PnNUuO9G41","screenshot_filename":"PnNUuO9G41.png","report_screenshot":"PnNUuO9G41.png","resolved_screenshot":"screenshots/PnNUuO9G41.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/PnNUuO9G41.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"1723ms","action_id":"Sz1LJ3uziS","screenshot_filename":"Sz1LJ3uziS.png","report_screenshot":"Sz1LJ3uziS.png","resolved_screenshot":"screenshots/Sz1LJ3uziS.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/Sz1LJ3uziS.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]","status":"passed","duration":"184ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"passed","duration":"3987ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"passed","duration":"472ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"}]}],"passed":1,"failed":1,"skipped":0,"status":"failed","availableScreenshots":["7LMHIAS1vB.png","PnNUuO9G41.png","Sz1LJ3uziS.png","Vb0vIiXsmB.png","Zk2URBrmoQ.png","cwCdZZvbh9.png","dGQDCfNGhv.png","jsAiE5MJlj.png","q7OMOkQeGE.png","qRa8hHy3dP.png","te0hB60RCB.png","tk949phD9Y.png","uY0IjJHreJ.png","ulLkfVpSX8.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>