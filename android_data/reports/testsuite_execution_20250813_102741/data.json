{"name": "UI Execution 13/08/2025, 10:31:40", "testCases": [{"name": "Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "225ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "373ms", "action_id": "PnNUuO9G41", "screenshot_filename": "PnNUuO9G41.png", "report_screenshot": "PnNUuO9G41.png", "resolved_screenshot": "screenshots/PnNUuO9G41.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/PnNUuO9G41.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "1734ms", "action_id": "Sz1LJ3uziS", "screenshot_filename": "Sz1LJ3uziS.png", "report_screenshot": "Sz1LJ3uziS.png", "resolved_screenshot": "screenshots/Sz1LJ3uziS.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/Sz1LJ3uziS.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]", "status": "failed", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Execute Test Case: Calc-subtest (3 steps) [CLEANUP]", "status": "unknown", "duration": "0ms", "action_id": "dGQDCfNGhv", "screenshot_filename": "dGQDCfNGhv.png", "report_screenshot": "dGQDCfNGhv.png", "resolved_screenshot": "screenshots/dGQDCfNGhv.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/dGQDCfNGhv.png"}]}, {"name": "Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "passed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "214ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "313ms", "action_id": "PnNUuO9G41", "screenshot_filename": "PnNUuO9G41.png", "report_screenshot": "PnNUuO9G41.png", "resolved_screenshot": "screenshots/PnNUuO9G41.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/PnNUuO9G41.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "1723ms", "action_id": "Sz1LJ3uziS", "screenshot_filename": "Sz1LJ3uziS.png", "report_screenshot": "Sz1LJ3uziS.png", "resolved_screenshot": "screenshots/Sz1LJ3uziS.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report", "action_id_screenshot": "screenshots/Sz1LJ3uziS.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]", "status": "passed", "duration": "184ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "passed", "duration": "3987ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "passed", "duration": "472ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png"}]}], "passed": 1, "failed": 1, "skipped": 0, "status": "failed"}